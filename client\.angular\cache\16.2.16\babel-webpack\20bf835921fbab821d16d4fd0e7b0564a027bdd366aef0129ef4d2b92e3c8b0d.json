{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nfunction MatMenuItem__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 3);\n    i0.ɵɵelement(1, \"polygon\", 4);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.panelId)(\"ngClass\", ctx_r0._classList)(\"@transformMenu\", ctx_r0._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0.ariaLabelledby || null)(\"aria-describedby\", ctx_r0.ariaDescribedby || null);\n  }\n}\nconst _c3 = [\"*\"];\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {}));\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem extends _MatMenuItemBase {\n  constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n    super();\n    this._elementRef = _elementRef;\n    this._document = _document;\n    this._focusMonitor = _focusMonitor;\n    this._parentMenu = _parentMenu;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ARIA role for the menu item. */\n    this.role = 'menuitem';\n    /** Stream that emits when the menu item is hovered. */\n    this._hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    this._focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    this._highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    this._triggersSubmenu = false;\n    _parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return clone.textContent?.trim() || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n  static {\n    this.ɵfac = function MatMenuItem_Factory(t) {\n      return new (t || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenuItem,\n      selectors: [[\"\", \"mat-menu-item\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-mdc-focus-indicator\"],\n      hostVars: 8,\n      hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n            return ctx._checkDisabled($event);\n          })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n            return ctx._handleMouseEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        role: \"role\"\n      },\n      exportAs: [\"matMenuItem\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"class\", \"mat-mdc-menu-submenu-icon\", \"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n      template: function MatMenuItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 1);\n          i0.ɵɵtemplate(4, MatMenuItem__svg_svg_4_Template, 2, 0, \"svg\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx._triggersSubmenu);\n        }\n      },\n      dependencies: [i2.NgIf, i3.MatRipple],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      inputs: ['disabled', 'disableRipple'],\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"\\n  aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    role: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\nclass _MatMenuContentBase {\n  constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n    this._template = _template;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._injector = _injector;\n    this._viewContainerRef = _viewContainerRef;\n    this._document = _document;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Emits when the menu content has been attached. */\n    this._attached = new Subject();\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n    this._changeDetectorRef?.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n  static {\n    this.ɵfac = function _MatMenuContentBase_Factory(t) {\n      return new (t || _MatMenuContentBase)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatMenuContentBase\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuContentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent extends _MatMenuContentBase {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatMenuContent_BaseFactory;\n      return function MatMenuContent_Factory(t) {\n        return (ɵMatMenuContent_BaseFactory || (ɵMatMenuContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuContent)))(t || MatMenuContent);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuContent,\n      selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [state('void', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))]),\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [\n  // TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', style({\n    opacity: 1\n  })), transition('void => *', [style({\n    opacity: 0\n  }), animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Base class with all of the `MatMenu` functionality. */\nclass _MatMenuBase {\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /** Whether the menu should overlap its trigger. */\n  get overlapTrigger() {\n    return this._overlapTrigger;\n  }\n  set overlapTrigger(value) {\n    this._overlapTrigger = coerceBooleanProperty(value);\n  }\n  /** Whether the menu has a backdrop. */\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        this._classList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        this._classList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  constructor(_elementRef, _ngZone, defaultOptions,\n  // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n  _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Only the direct descendant menu items. */\n    this._directDescendantItems = new QueryList();\n    /** Config object to be passed into the menu's ngClass */\n    this._classList = {};\n    /** Current state of the panel animation. */\n    this._panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    this._animationDone = new Subject();\n    /** Event emitted when the menu is closed. */\n    this.closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    this.close = this.closed;\n    this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this._overlapTrigger = defaultOptions.overlapTrigger;\n    this._hasBackdrop = defaultOptions.hasBackdrop;\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusSubscription?.unsubscribe();\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n    // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n    event.stopPropagation();\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusSubscription?.unsubscribe();\n    this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n  setElevation(depth) {\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n    if (!customElevation || customElevation === this._previousElevation) {\n      if (this._previousElevation) {\n        this._classList[this._previousElevation] = false;\n      }\n      this._classList[newElevation] = true;\n      this._previousElevation = newElevation;\n    }\n  }\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    const classes = this._classList;\n    classes['mat-menu-before'] = posX === 'before';\n    classes['mat-menu-after'] = posX === 'after';\n    classes['mat-menu-above'] = posY === 'above';\n    classes['mat-menu-below'] = posY === 'below';\n    // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n    this._changeDetectorRef?.markForCheck();\n  }\n  /** Starts the enter animation. */\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n  /** Resets the panel animation to its initial state. */\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(event) {\n    this._animationDone.next(event);\n    this._isAnimating = false;\n  }\n  _onAnimationStart(event) {\n    this._isAnimating = true;\n    // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n  static {\n    this.ɵfac = function _MatMenuBase_Factory(t) {\n      return new (t || _MatMenuBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatMenuBase,\n      contentQueries: function _MatMenuBase_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function _MatMenuBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      inputs: {\n        backdropClass: \"backdropClass\",\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n        xPosition: \"xPosition\",\n        yPosition: \"yPosition\",\n        overlapTrigger: \"overlapTrigger\",\n        hasBackdrop: \"hasBackdrop\",\n        panelClass: [\"class\", \"panelClass\"],\n        classList: \"classList\"\n      },\n      outputs: {\n        closed: \"closed\",\n        close: \"close\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenu extends _MatMenuBase {\n  constructor(_elementRef, _ngZone, _defaultOptions, changeDetectorRef) {\n    super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n    this._elevationPrefix = 'mat-elevation-z';\n    this._baseElevation = 8;\n  }\n  static {\n    this.ɵfac = function MatMenu_Factory(t) {\n      return new (t || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenu,\n      selectors: [[\"mat-menu\"]],\n      hostAttrs: [\"ngSkipHydration\", \"\"],\n      hostVars: 3,\n      hostBindings: function MatMenu_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n        }\n      },\n      exportAs: [\"matMenu\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", \"mat-mdc-elevation-specific\", 3, \"id\", \"ngClass\", \"keydown\", \"click\"], [1, \"mat-mdc-menu-content\"]],\n      template: function MatMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 6, \"ng-template\");\n        }\n      },\n      dependencies: [i2.NgClass],\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{--mat-menu-container-shape:4px;min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null',\n        'ngSkipHydration': ''\n      },\n      animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{--mat-menu-container-shape:4px;min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\nclass _MatMenuTriggerBase {\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu,\n  // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n  // tslint:disable-next-line: lightweight-tokens\n  _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n    this._overlay = _overlay;\n    this._element = _element;\n    this._viewContainerRef = _viewContainerRef;\n    this._menuItemInstance = _menuItemInstance;\n    this._dir = _dir;\n    this._focusMonitor = _focusMonitor;\n    this._ngZone = _ngZone;\n    this._overlayRef = null;\n    this._menuOpen = false;\n    this._closingActionsSubscription = Subscription.EMPTY;\n    this._hoverSubscription = Subscription.EMPTY;\n    this._menuCloseSubscription = Subscription.EMPTY;\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Handles touch start events on the trigger.\n     * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n     */\n    this._handleTouchStart = event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    };\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    this._openedBy = undefined;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    this.restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    this.menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    this.menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuClose = this.menuClosed;\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n    _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n    this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    this._initMenu(menu);\n    if (menu instanceof _MatMenuBase) {\n      menu._startAnimation();\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n    const menu = this.menu;\n    this._closingActionsSubscription.unsubscribe();\n    this._overlayRef.detach();\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    if (menu instanceof _MatMenuBase) {\n      menu._resetAnimation();\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1),\n        // Interrupt if the content got re-attached.\n        takeUntil(menu.lazyContent._attached)).subscribe({\n          next: () => menu.lazyContent.detach(),\n          // No matter whether the content got re-attached, reset the menu.\n          complete: () => this._setIsMenuOpen(false)\n        });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n      menu?.lazyContent?.detach();\n    }\n  }\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n  _initMenu(menu) {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    this._setMenuElevation(menu);\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n  }\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n  _setMenuElevation(menu) {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n      menu.setElevation(depth);\n    }\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = this._overlay.create(config);\n      // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n      this._overlayRef.keydownEvents().subscribe();\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n        // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n        } else {\n          menu.setPositionClasses(posX, posY);\n        }\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n    this._hoverSubscription = this._parentMaterialMenu._hovered()\n    // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n    // with different data and triggers), we have to delay it by a tick to ensure that\n    // it won't be closed immediately after it is opened.\n    .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n      this._openedBy = 'mouse';\n      // If the same menu is used between multiple triggers, it might still be animating\n      // while the new trigger tries to re-open it. Wait for the animation to finish\n      // before doing so. Also interrupt if the user moves to another item.\n      if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n        // We need the `delay(0)` here in order to avoid\n        // 'changed after checked' errors in some cases. See #12194.\n        this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n      } else {\n        this.openMenu();\n      }\n    });\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n  static {\n    this.ɵfac = function _MatMenuTriggerBase_Factory(t) {\n      return new (t || _MatMenuTriggerBase)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatMenuTriggerBase,\n      hostVars: 3,\n      hostBindings: function _MatMenuTriggerBase_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function _MatMenuTriggerBase_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"mousedown\", function _MatMenuTriggerBase_mousedown_HostBindingHandler($event) {\n            return ctx._handleMousedown($event);\n          })(\"keydown\", function _MatMenuTriggerBase_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n        }\n      },\n      inputs: {\n        _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n        menu: [\"matMenuTriggerFor\", \"menu\"],\n        menuData: [\"matMenuTriggerData\", \"menuData\"],\n        restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n      },\n      outputs: {\n        menuOpened: \"menuOpened\",\n        onMenuOpen: \"onMenuOpen\",\n        menuClosed: \"menuClosed\",\n        onMenuClose: \"onMenuClose\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuTriggerBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: MatMenuItem,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }, {\n      type: i3$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger extends _MatMenuTriggerBase {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatMenuTrigger_BaseFactory;\n      return function MatMenuTrigger_Factory(t) {\n        return (ɵMatMenuTrigger_BaseFactory || (ɵMatMenuTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuTrigger)))(t || MatMenuTrigger);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuTrigger,\n      selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n      exportAs: [\"matMenuTrigger\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger'\n      },\n      exportAs: 'matMenuTrigger'\n    }]\n  }], null, null);\n})();\nclass MatMenuModule {\n  static {\n    this.ɵfac = function MatMenuModule_Factory(t) {\n      return new (t || MatMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatMenuModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Optional", "Input", "Directive", "QueryList", "EventEmitter", "TemplateRef", "ContentChildren", "ViewChild", "ContentChild", "Output", "inject", "ChangeDetectorRef", "Self", "NgModule", "i1", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "coerceBooleanProperty", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "asapScheduler", "startWith", "switchMap", "take", "takeUntil", "filter", "delay", "i3", "mixinDisableRipple", "mixinDisabled", "MatRippleModule", "MatCommonModule", "i2", "DOCUMENT", "CommonModule", "TemplatePortal", "DomPortalOutlet", "trigger", "state", "style", "transition", "animate", "i3$1", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "normalizePassiveListenerOptions", "CdkScrollableModule", "_c0", "MatMenuItem__svg_svg_4_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "_c1", "_c2", "MatMenu_ng_template_0_Template", "_r2", "ɵɵgetCurrentView", "ɵɵlistener", "MatMenu_ng_template_0_Template_div_keydown_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_handleKeydown", "MatMenu_ng_template_0_Template_div_click_0_listener", "ctx_r3", "closed", "emit", "MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener", "ctx_r4", "_onAnimationStart", "MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener", "ctx_r5", "_onAnimationDone", "ɵɵprojection", "ctx_r0", "ɵɵproperty", "panelId", "_classList", "_panelAnimationState", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "_c3", "MAT_MENU_PANEL", "_MatMenuItemBase", "MatMenuItem", "constructor", "_elementRef", "_document", "_focusMonitor", "_parentMenu", "_changeDetectorRef", "role", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "next", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "disabled", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_hasFocus", "activeElement", "ɵfac", "MatMenuItem_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusMonitor", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatMenuItem_HostBindings", "MatMenuItem_click_HostBindingHandler", "MatMenuItem_mouseenter_HostBindingHandler", "ɵɵclassProp", "inputs", "disable<PERSON><PERSON><PERSON>", "exportAs", "features", "ɵɵInheritDefinitionFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatMenuItem_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵadvance", "dependencies", "NgIf", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "undefined", "decorators", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "_MatMenuContentBase", "_template", "_componentFactoryResolver", "_appRef", "_injector", "_viewContainerRef", "_attached", "attach", "context", "_portal", "detach", "_outlet", "createElement", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "isAttached", "dispose", "_MatMenuContentBase_Factory", "ComponentFactoryResolver", "ApplicationRef", "Injector", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵMatMenuContent_BaseFactory", "MatMenuContent_Factory", "ɵɵgetInheritedFactory", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "matMenuAnimations", "transformMenu", "opacity", "transform", "fadeInItems", "menuPanelUid", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "_MatMenuBase", "_xPosition", "value", "setPositionClasses", "_yPosition", "_overlapTrigger", "hasBackdrop", "_hasBackdrop", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "split", "for<PERSON>ach", "className", "classList", "_ngZone", "defaultOptions", "_directDescendantItems", "_animationDone", "close", "overlayPanelClass", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "_keyManager", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "changes", "pipe", "items", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "_firstItemFocusSubscription", "unsubscribe", "itemChanges", "_item", "keyCode", "parentMenu", "direction", "setFocusOrigin", "onKeydown", "focusFirstItem", "onStable", "menuPanel", "first", "closest", "contains", "document", "setFirstItemActive", "resetActiveItem", "setElevation", "depth", "elevation", "_baseElevation", "newElevation", "_elevationPrefix", "customElevation", "Object", "keys", "find", "startsWith", "_previousElevation", "posX", "posY", "_startAnimation", "_resetAnimation", "_isAnimating", "toState", "scrollTop", "_allItems", "reset", "notifyOn<PERSON><PERSON>es", "_MatMenuBase_Factory", "NgZone", "contentQueries", "_MatMenuBase_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "lazyContent", "viewQuery", "_MatMenuBase_Query", "ɵɵviewQuery", "templateRef", "outputs", "descendants", "MatMenu", "_defaultOptions", "changeDetectorRef", "MatMenu_Factory", "MatMenu_HostBindings", "MatMenu_Template", "Ng<PERSON><PERSON>", "styles", "data", "animation", "animations", "MAT_MENU_SCROLL_STRATEGY", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "passiveEventListenerOptions", "passive", "MENU_PANEL_TOP_PADDING", "_MatMenuTriggerBase", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "_menuCloseSubscription", "_parentMaterialMenu", "reason", "_destroyMenu", "_menuItemInstance", "_overlay", "_element", "scrollStrategy", "_dir", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "EMPTY", "_hoverSubscription", "_handleTouchStart", "_openedBy", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "_scrollStrategy", "addEventListener", "_handleHover", "removeEventListener", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "_getPortal", "menuData", "_menuClosingActions", "_initMenu", "withLockedPosition", "reapplyLastPosition", "updatePosition", "_setIsMenuOpen", "_setMenuElevation", "isOpen", "config", "_getOverlayConfig", "_subscribeToPositions", "create", "keydownEvents", "position", "flexibleConnectedTo", "withGrowAfterOpen", "withTransformOriginOn", "position<PERSON><PERSON>es", "change", "connectionPair", "overlayX", "overlayY", "run", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "_parentInnerPadding", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "_MatMenuTriggerBase_Factory", "Directionality", "_MatMenuTriggerBase_HostBindings", "_MatMenuTriggerBase_click_HostBindingHandler", "_MatMenuTriggerBase_mousedown_HostBindingHandler", "_MatMenuTriggerBase_keydown_HostBindingHandler", "MatMenuTrigger", "ɵMatMenuTrigger_BaseFactory", "MatMenuTrigger_Factory", "MatMenuModule", "MatMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/findit/client/node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {\n}));\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem extends _MatMenuItemBase {\n    constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n        super();\n        this._elementRef = _elementRef;\n        this._document = _document;\n        this._focusMonitor = _focusMonitor;\n        this._parentMenu = _parentMenu;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** ARIA role for the menu item. */\n        this.role = 'menuitem';\n        /** Stream that emits when the menu item is hovered. */\n        this._hovered = new Subject();\n        /** Stream that emits when the menu item is focused. */\n        this._focused = new Subject();\n        /** Whether the menu item is highlighted. */\n        this._highlighted = false;\n        /** Whether the menu item acts as a trigger for a sub-menu. */\n        this._triggersSubmenu = false;\n        _parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element, so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons, so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuItem, deps: [{ token: i0.ElementRef }, { token: DOCUMENT }, { token: i1.FocusMonitor }, { token: MAT_MENU_PANEL, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatMenuItem, selector: \"[mat-menu-item]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", role: \"role\" }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-mdc-focus-indicator\" }, exportAs: [\"matMenuItem\"], usesInheritance: true, ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"\\n  aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\", dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', inputs: ['disabled', 'disableRipple'], host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-mdc-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"\\n  aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { role: [{\n                type: Input\n            }] } });\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\nclass _MatMenuContentBase {\n    constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n        this._template = _template;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._injector = _injector;\n        this._viewContainerRef = _viewContainerRef;\n        this._document = _document;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Emits when the menu content has been attached. */\n        this._attached = new Subject();\n    }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        // @breaking-change 9.0.0 Make change detector ref required\n        this._changeDetectorRef?.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        if (this._outlet) {\n            this._outlet.dispose();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuContentBase, deps: [{ token: i0.TemplateRef }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.Injector }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatMenuContentBase, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuContentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.Injector }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent extends _MatMenuContentBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuContent, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatMenuContent, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }] });\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: trigger('transformMenu', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(0.8)',\n        })),\n        transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1)',\n        }))),\n        transition('* => void', animate('100ms 25ms linear', style({ opacity: 0 }))),\n    ]),\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: trigger('fadeInItems', [\n        // TODO(crisbeto): this is inside the `transformMenu`\n        // now. Remove next time we do breaking changes.\n        state('showing', style({ opacity: 1 })),\n        transition('void => *', [\n            style({ opacity: 0 }),\n            animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\n/** Base class with all of the `MatMenu` functionality. */\nclass _MatMenuBase {\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** Whether the menu should overlap its trigger. */\n    get overlapTrigger() {\n        return this._overlapTrigger;\n    }\n    set overlapTrigger(value) {\n        this._overlapTrigger = coerceBooleanProperty(value);\n    }\n    /** Whether the menu has a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                this._classList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                this._classList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    constructor(_elementRef, _ngZone, defaultOptions, \n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    _changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Only the direct descendant menu items. */\n        this._directDescendantItems = new QueryList();\n        /** Config object to be passed into the menu's ngClass */\n        this._classList = {};\n        /** Current state of the panel animation. */\n        this._panelAnimationState = 'void';\n        /** Emits whenever an animation on the menu completes. */\n        this._animationDone = new Subject();\n        /** Event emitted when the menu is closed. */\n        this.closed = new EventEmitter();\n        /**\n         * Event emitted when the menu is closed.\n         * @deprecated Switch to `closed` instead\n         * @breaking-change 8.0.0\n         */\n        this.close = this.closed;\n        this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n        this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n        this._xPosition = defaultOptions.xPosition;\n        this._yPosition = defaultOptions.yPosition;\n        this.backdropClass = defaultOptions.backdropClass;\n        this._overlapTrigger = defaultOptions.overlapTrigger;\n        this._hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusSubscription?.unsubscribe();\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n        // Don't allow the event to propagate if we've already handled it, or it may\n        // end up reaching other overlays that were opened earlier (see #22694).\n        event.stopPropagation();\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusSubscription?.unsubscribe();\n        this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            let menuPanel = null;\n            if (this._directDescendantItems.length) {\n                // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n                // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n                // because the panel is inside an `ng-template`. We work around it by starting from one of\n                // the items and walking up the DOM.\n                menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n            }\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * Sets the menu panel elevation.\n     * @param depth Number of parent menus that come before the menu.\n     */\n    setElevation(depth) {\n        // The elevation starts at the base and increases by one for each level.\n        // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n        const elevation = Math.min(this._baseElevation + depth, 24);\n        const newElevation = `${this._elevationPrefix}${elevation}`;\n        const customElevation = Object.keys(this._classList).find(className => {\n            return className.startsWith(this._elevationPrefix);\n        });\n        if (!customElevation || customElevation === this._previousElevation) {\n            if (this._previousElevation) {\n                this._classList[this._previousElevation] = false;\n            }\n            this._classList[newElevation] = true;\n            this._previousElevation = newElevation;\n        }\n    }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        const classes = this._classList;\n        classes['mat-menu-before'] = posX === 'before';\n        classes['mat-menu-after'] = posX === 'after';\n        classes['mat-menu-above'] = posY === 'above';\n        classes['mat-menu-below'] = posY === 'below';\n        // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n        this._changeDetectorRef?.markForCheck();\n    }\n    /** Starts the enter animation. */\n    _startAnimation() {\n        // @breaking-change 8.0.0 Combine with _resetAnimation.\n        this._panelAnimationState = 'enter';\n    }\n    /** Resets the panel animation to its initial state. */\n    _resetAnimation() {\n        // @breaking-change 8.0.0 Combine with _startAnimation.\n        this._panelAnimationState = 'void';\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(event) {\n        this._animationDone.next(event);\n        this._isAnimating = false;\n    }\n    _onAnimationStart(event) {\n        this._isAnimating = true;\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n            event.element.scrollTop = 0;\n        }\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuBase, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatMenuBase, inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: \"overlapTrigger\", hasBackdrop: \"hasBackdrop\", panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\nclass MatMenu extends _MatMenuBase {\n    constructor(_elementRef, _ngZone, _defaultOptions, changeDetectorRef) {\n        super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n        this._elevationPrefix = 'mat-elevation-z';\n        this._baseElevation = 8;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenu, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatMenu, selector: \"mat-menu\", host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], exportAs: [\"matMenu\"], usesInheritance: true, ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{--mat-menu-container-shape:4px;min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                        'ngSkipHydration': '',\n                    }, animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{--mat-menu-container-shape:4px;min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\nclass _MatMenuTriggerBase {\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu, \n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n        this._overlay = _overlay;\n        this._element = _element;\n        this._viewContainerRef = _viewContainerRef;\n        this._menuItemInstance = _menuItemInstance;\n        this._dir = _dir;\n        this._focusMonitor = _focusMonitor;\n        this._ngZone = _ngZone;\n        this._overlayRef = null;\n        this._menuOpen = false;\n        this._closingActionsSubscription = Subscription.EMPTY;\n        this._hoverSubscription = Subscription.EMPTY;\n        this._menuCloseSubscription = Subscription.EMPTY;\n        this._changeDetectorRef = inject(ChangeDetectorRef);\n        /**\n         * Handles touch start events on the trigger.\n         * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n         */\n        this._handleTouchStart = (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        };\n        // Tracking input type is necessary so it's possible to only auto-focus\n        // the first item of the list when the menu is opened via the keyboard\n        this._openedBy = undefined;\n        /**\n         * Whether focus should be restored when the menu is closed.\n         * Note that disabling this option can have accessibility implications\n         * and it's up to you to manage focus, if you decide to turn it off.\n         */\n        this.restoreFocus = true;\n        /** Event emitted when the associated menu is opened. */\n        this.menuOpened = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is opened.\n         * @deprecated Switch to `menuOpened` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuOpen = this.menuOpened;\n        /** Event emitted when the associated menu is closed. */\n        this.menuClosed = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is closed.\n         * @deprecated Switch to `menuClosed` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuClose = this.menuClosed;\n        this._scrollStrategy = scrollStrategy;\n        this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n        _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n        this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        overlayRef.attach(this._getPortal(menu));\n        if (menu.lazyContent) {\n            menu.lazyContent.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        this._initMenu(menu);\n        if (menu instanceof _MatMenuBase) {\n            menu._startAnimation();\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        if (!this._overlayRef || !this.menuOpen) {\n            return;\n        }\n        const menu = this.menu;\n        this._closingActionsSubscription.unsubscribe();\n        this._overlayRef.detach();\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        if (menu instanceof _MatMenuBase) {\n            menu._resetAnimation();\n            if (menu.lazyContent) {\n                // Wait for the exit animation to finish before detaching the content.\n                menu._animationDone\n                    .pipe(filter(event => event.toState === 'void'), take(1), \n                // Interrupt if the content got re-attached.\n                takeUntil(menu.lazyContent._attached))\n                    .subscribe({\n                    next: () => menu.lazyContent.detach(),\n                    // No matter whether the content got re-attached, reset the menu.\n                    complete: () => this._setIsMenuOpen(false),\n                });\n            }\n            else {\n                this._setIsMenuOpen(false);\n            }\n        }\n        else {\n            this._setIsMenuOpen(false);\n            menu?.lazyContent?.detach();\n        }\n    }\n    /**\n     * This method sets the menu state to open and focuses the first item if\n     * the menu was opened via the keyboard.\n     */\n    _initMenu(menu) {\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        this._setMenuElevation(menu);\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n    }\n    /** Updates the menu elevation based on the amount of parent menus that it has. */\n    _setMenuElevation(menu) {\n        if (menu.setElevation) {\n            let depth = 0;\n            let parentMenu = menu.parentMenu;\n            while (parentMenu) {\n                depth++;\n                parentMenu = parentMenu.parentMenu;\n            }\n            menu.setElevation(depth);\n        }\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = this._overlay.create(config);\n            // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n            // Ideally we'd also have our keyboard event logic in here, however doing so will\n            // break anybody that may have implemented the `MatMenuPanel` themselves.\n            this._overlayRef.keydownEvents().subscribe();\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: this._overlay\n                .position()\n                .flexibleConnectedTo(this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                // @breaking-change 15.0.0 Remove null check for `ngZone`.\n                // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n                // updating something in the view so we need to bring it back in.\n                if (this._ngZone) {\n                    this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n                }\n                else {\n                    menu.setPositionClasses(posX, posY);\n                }\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n            return;\n        }\n        this._hoverSubscription = this._parentMaterialMenu\n            ._hovered()\n            // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n            // with different data and triggers), we have to delay it by a tick to ensure that\n            // it won't be closed immediately after it is opened.\n            .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler))\n            .subscribe(() => {\n            this._openedBy = 'mouse';\n            // If the same menu is used between multiple triggers, it might still be animating\n            // while the new trigger tries to re-open it. Wait for the animation to finish\n            // before doing so. Also interrupt if the user moves to another item.\n            if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n                // We need the `delay(0)` here in order to avoid\n                // 'changed after checked' errors in some cases. See #12194.\n                this.menu._animationDone\n                    .pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered()))\n                    .subscribe(() => this.openMenu());\n            }\n            else {\n                this.openMenu();\n            }\n        });\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuTriggerBase, deps: [{ token: i1$1.Overlay }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: MAT_MENU_SCROLL_STRATEGY }, { token: MAT_MENU_PANEL, optional: true }, { token: MatMenuItem, optional: true, self: true }, { token: i3$1.Directionality, optional: true }, { token: i1.FocusMonitor }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatMenuTriggerBase, inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu.panelId : null\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatMenuTriggerBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_SCROLL_STRATEGY]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: MatMenuItem, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i3$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i1.FocusMonitor }, { type: i0.NgZone }]; }, propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger extends _MatMenuTriggerBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatMenuTrigger, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", host: { classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }] });\n\nclass MatMenuModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuModule, declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger], imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule], exports: [CdkScrollableModule,\n            MatMenu,\n            MatCommonModule,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    declarations: [MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAC5Q,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AACtH,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACtE,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AACrF,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC5G,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,mBAAmB,QAAQ,wBAAwB;;AAE5D;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0GoGpE,EAAE,CAAAsE,cAAA,CAC6hC,CAAC;IADhiCtE,EAAE,CAAAuE,cAAA,YAC6hC,CAAC;IADhiCvE,EAAE,CAAAwE,SAAA,gBAC+jC,CAAC;IADlkCxE,EAAE,CAAAyE,YAAA,CACqkC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAS,GAAA,GADxkC7E,EAAE,CAAA8E,gBAAA;IAAF9E,EAAE,CAAAuE,cAAA,YAyjB23B,CAAC;IAzjB93BvE,EAAE,CAAA+E,UAAA,qBAAAC,sDAAAC,MAAA;MAAFjF,EAAE,CAAAkF,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFnF,EAAE,CAAAoF,aAAA;MAAA,OAAFpF,EAAE,CAAAqF,WAAA,CAyjBudF,MAAA,CAAAG,cAAA,CAAAL,MAAqB,EAAC;IAAA,CAAC,CAAC,mBAAAM,oDAAA;MAzjBjfvF,EAAE,CAAAkF,aAAA,CAAAL,GAAA;MAAA,MAAAW,MAAA,GAAFxF,EAAE,CAAAoF,aAAA;MAAA,OAAFpF,EAAE,CAAAqF,WAAA,CAyjB+fG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAY,OAAO,EAAC;IAAA,CAArC,CAAC,kCAAAC,4EAAAV,MAAA;MAzjBjfjF,EAAE,CAAAkF,aAAA,CAAAL,GAAA;MAAA,MAAAe,MAAA,GAAF5F,EAAE,CAAAoF,aAAA;MAAA,OAAFpF,EAAE,CAAAqF,WAAA,CAyjBmmBO,MAAA,CAAAC,iBAAA,CAAAZ,MAAwB,EAAC;IAAA,CAA9I,CAAC,iCAAAa,2EAAAb,MAAA;MAzjBjfjF,EAAE,CAAAkF,aAAA,CAAAL,GAAA;MAAA,MAAAkB,MAAA,GAAF/F,EAAE,CAAAoF,aAAA;MAAA,OAAFpF,EAAE,CAAAqF,WAAA,CAyjB4pBU,MAAA,CAAAC,gBAAA,CAAAf,MAAuB,EAAC;IAAA,CAAtM,CAAC;IAzjBjfjF,EAAE,CAAAuE,cAAA,YAyjBq6B,CAAC;IAzjBx6BvE,EAAE,CAAAiG,YAAA,EAyjBs8B,CAAC;IAzjBz8BjG,EAAE,CAAAyE,YAAA,CAyjBk9B,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAA8B,MAAA,GAzjBr9BlG,EAAE,CAAAoF,aAAA;IAAFpF,EAAE,CAAAmG,UAAA,OAAAD,MAAA,CAAAE,OAyjBsa,CAAC,YAAAF,MAAA,CAAAG,UAAD,CAAC,mBAAAH,MAAA,CAAAI,oBAAD,CAAC;IAzjBzatG,EAAE,CAAAuG,WAAA,eAAAL,MAAA,CAAAM,SAAA,QAyjB0wB,CAAC,oBAAAN,MAAA,CAAAO,cAAA,QAAD,CAAC,qBAAAP,MAAA,CAAAQ,eAAA,QAAD,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AA/pBj3B,MAAMC,cAAc,GAAG,IAAI3G,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA,MAAM4G,gBAAgB,GAAGhE,kBAAkB,CAACC,aAAa,CAAC,MAAM,EAC/D,CAAC,CAAC;AACH;AACA;AACA;AACA,MAAMgE,WAAW,SAASD,gBAAgB,CAAC;EACvCE,WAAWA,CAACC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAE;IAChF,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIrF,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACsF,QAAQ,GAAG,IAAItF,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACuF,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7BN,WAAW,EAAEO,OAAO,GAAG,IAAI,CAAC;EAChC;EACA;EACAC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACX,aAAa,IAAIU,MAAM,EAAE;MAC9B,IAAI,CAACV,aAAa,CAACY,QAAQ,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAEH,MAAM,EAAEC,OAAO,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACE,eAAe,CAAC,CAAC,CAACJ,KAAK,CAACE,OAAO,CAAC;IACzC;IACA,IAAI,CAACN,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;EAC5B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACf,aAAa,EAAE;MACpB;MACA;MACA;MACA,IAAI,CAACA,aAAa,CAACgB,OAAO,CAAC,IAAI,CAAClB,WAAW,EAAE,KAAK,CAAC;IACvD;EACJ;EACAmB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACkB,cAAc,CAAC,IAAI,CAACpB,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAACG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACkB,UAAU,EAAE;MACjD,IAAI,CAAClB,WAAW,CAACkB,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACf,QAAQ,CAACgB,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACf,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAT,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACf,WAAW,CAACyB,aAAa;EACzC;EACA;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACH,QAAQ,EAAE;MACfG,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxB,QAAQ,CAACU,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAe,QAAQA,CAAA,EAAG;IACP,MAAMC,KAAK,GAAG,IAAI,CAAChC,WAAW,CAACyB,aAAa,CAACQ,SAAS,CAAC,IAAI,CAAC;IAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;IACjE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;IACrB;IACA,OAAON,KAAK,CAACO,WAAW,EAAEC,IAAI,CAAC,CAAC,IAAI,EAAE;EAC1C;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAI,CAAClC,YAAY,GAAGkC,aAAa;IACjC,IAAI,CAACtC,kBAAkB,EAAEuC,YAAY,CAAC,CAAC;EAC3C;EACAC,mBAAmBA,CAACC,eAAe,EAAE;IACjC;IACA,IAAI,CAACpC,gBAAgB,GAAGoC,eAAe;IACvC,IAAI,CAACzC,kBAAkB,EAAEuC,YAAY,CAAC,CAAC;EAC3C;EACAG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC7C,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC8C,aAAa,KAAK,IAAI,CAAChC,eAAe,CAAC,CAAC;EACpF;EACA;IAAS,IAAI,CAACiC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFpD,WAAW,EAArB9G,EAAE,CAAAmK,iBAAA,CAAqCnK,EAAE,CAACoK,UAAU,GAApDpK,EAAE,CAAAmK,iBAAA,CAA+DjH,QAAQ,GAAzElD,EAAE,CAAAmK,iBAAA,CAAoF/I,EAAE,CAACiJ,YAAY,GAArGrK,EAAE,CAAAmK,iBAAA,CAAgHvD,cAAc,MAAhI5G,EAAE,CAAAmK,iBAAA,CAA2JnK,EAAE,CAACiB,iBAAiB;IAAA,CAA4C;EAAE;EAC/T;IAAS,IAAI,CAACqJ,IAAI,kBAD8EtK,EAAE,CAAAuK,iBAAA;MAAAC,IAAA,EACJ1D,WAAW;MAAA2D,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAzG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTpE,EAAE,CAAA+E,UAAA,mBAAA+F,qCAAA7F,MAAA;YAAA,OACJZ,GAAA,CAAAqE,cAAA,CAAAzD,MAAqB,CAAC;UAAA,0BAAA8F,0CAAA;YAAA,OAAtB1G,GAAA,CAAAyE,iBAAA,CAAkB,CAAC;UAAA;QAAA;QAAA,IAAA1E,EAAA;UADjBpE,EAAE,CAAAuG,WAAA,SAAAlC,GAAA,CAAAgD,IAAA,cAAAhD,GAAA,CAAAkE,YAAA,qBAAAlE,GAAA,CAAAmE,QAAA,cAAAnE,GAAA,CAAAmE,QAAA;UAAFxI,EAAE,CAAAgL,WAAA,kCAAA3G,GAAA,CAAAmD,YAAA,uCAAAnD,GAAA,CAAAoD,gBAAA;QAAA;MAAA;MAAAwD,MAAA;QAAAzC,QAAA;QAAA0C,aAAA;QAAA7D,IAAA;MAAA;MAAA8D,QAAA;MAAAC,QAAA,GAAFpL,EAAE,CAAAqL,0BAAA;MAAAC,KAAA,EAAApH,GAAA;MAAAqH,kBAAA,EAAA5G,GAAA;MAAA6G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAxH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpE,EAAE,CAAA6L,eAAA,CAAAnH,GAAA;UAAF1E,EAAE,CAAAiG,YAAA,EACmqB,CAAC;UADtqBjG,EAAE,CAAAuE,cAAA,aAC4sB,CAAC;UAD/sBvE,EAAE,CAAAiG,YAAA,KACquB,CAAC;UADxuBjG,EAAE,CAAAyE,YAAA,CAC4uB,CAAC;UAD/uBzE,EAAE,CAAAwE,SAAA,YAC04B,CAAC;UAD74BxE,EAAE,CAAA8L,UAAA,IAAA3H,+BAAA,gBACqkC,CAAC;QAAA;QAAA,IAAAC,EAAA;UADxkCpE,EAAE,CAAA+L,SAAA,EACk1B,CAAC;UADr1B/L,EAAE,CAAAmG,UAAA,sBAAA9B,GAAA,CAAA6G,aAAA,IAAA7G,GAAA,CAAAmE,QACk1B,CAAC,qBAAAnE,GAAA,CAAA0D,eAAA,EAAD,CAAC;UADr1B/H,EAAE,CAAA+L,SAAA,EAC46B,CAAC;UAD/6B/L,EAAE,CAAAmG,UAAA,SAAA9B,GAAA,CAAAoD,gBAC46B,CAAC;QAAA;MAAA;MAAAuE,YAAA,GAAwM/I,EAAE,CAACgJ,IAAI,EAA6FrJ,EAAE,CAACsJ,SAAS;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AAC1oD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrM,EAAE,CAAAsM,iBAAA,CAGXxF,WAAW,EAAc,CAAC;IACzG0D,IAAI,EAAEtK,SAAS;IACfqM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAErB,QAAQ,EAAE,aAAa;MAAEF,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;MAAEwB,IAAI,EAAE;QAChG,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,2CAA2C;QACpD,uCAAuC,EAAE,cAAc;QACvD,2CAA2C,EAAE,kBAAkB;QAC/D,iBAAiB,EAAE,gBAAgB;QACnC,sBAAsB,EAAE,UAAU;QAClC,iBAAiB,EAAE,kBAAkB;QACrC,SAAS,EAAE,wBAAwB;QACnC,cAAc,EAAE;MACpB,CAAC;MAAEL,eAAe,EAAEjM,uBAAuB,CAACuM,MAAM;MAAEP,aAAa,EAAE/L,iBAAiB,CAACuM,IAAI;MAAEhB,QAAQ,EAAE;IAAue,CAAC;EACzlB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnB,IAAI,EAAExK,EAAE,CAACoK;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvFrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAACrJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEsH,IAAI,EAAEpJ,EAAE,CAACiJ;IAAa,CAAC,EAAE;MAAEG,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC7DrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAAC3F,cAAc;MACzB,CAAC,EAAE;QACC4D,IAAI,EAAElK;MACV,CAAC;IAAE,CAAC,EAAE;MAAEkK,IAAI,EAAExK,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoG,IAAI,EAAE,CAAC;MACnEmD,IAAI,EAAEjK;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAASuM,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,KAAK,CAAE;AACjB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMD,KAAK,CAAE;AACjB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,MAAMF,KAAK,CAAE,gFAA+E,GACvF,sEAAqE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAIjN,cAAc,CAAC,gBAAgB,CAAC;AAC7D,MAAMkN,mBAAmB,CAAC;EACtBpG,WAAWA,CAACqG,SAAS,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,SAAS,EAAEC,iBAAiB,EAAEvG,SAAS,EAAEG,kBAAkB,EAAE;IACpH,IAAI,CAACgG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACvG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACqG,SAAS,GAAG,IAAIxL,OAAO,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIyL,MAAMA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIxK,cAAc,CAAC,IAAI,CAACgK,SAAS,EAAE,IAAI,CAACI,iBAAiB,CAAC;IAC7E;IACA,IAAI,CAACK,MAAM,CAAC,CAAC;IACb,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIzK,eAAe,CAAC,IAAI,CAAC4D,SAAS,CAAC8G,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAACV,yBAAyB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IACzI;IACA,MAAMS,OAAO,GAAG,IAAI,CAACZ,SAAS,CAACa,UAAU,CAACxF,aAAa;IACvD;IACA;IACA;IACAuF,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACL,OAAO,CAACM,aAAa,EAAEJ,OAAO,CAAC;IACpE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5G,kBAAkB,EAAEuC,YAAY,CAAC,CAAC;IACvC,IAAI,CAACiE,OAAO,CAACF,MAAM,CAAC,IAAI,CAACI,OAAO,EAAEH,OAAO,CAAC;IAC1C,IAAI,CAACF,SAAS,CAACzF,IAAI,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACI6F,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACD,OAAO,CAACS,UAAU,EAAE;MACzB,IAAI,CAACT,OAAO,CAACC,MAAM,CAAC,CAAC;IACzB;EACJ;EACA1F,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC2F,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAAC,CAAC;IAC1B;EACJ;EACA;IAAS,IAAI,CAACtE,IAAI,YAAAuE,4BAAArE,CAAA;MAAA,YAAAA,CAAA,IAAwFiD,mBAAmB,EAnH7BnN,EAAE,CAAAmK,iBAAA,CAmH6CnK,EAAE,CAACW,WAAW,GAnH7DX,EAAE,CAAAmK,iBAAA,CAmHwEnK,EAAE,CAACwO,wBAAwB,GAnHrGxO,EAAE,CAAAmK,iBAAA,CAmHgHnK,EAAE,CAACyO,cAAc,GAnHnIzO,EAAE,CAAAmK,iBAAA,CAmH8InK,EAAE,CAAC0O,QAAQ,GAnH3J1O,EAAE,CAAAmK,iBAAA,CAmHsKnK,EAAE,CAAC2O,gBAAgB,GAnH3L3O,EAAE,CAAAmK,iBAAA,CAmHsMjH,QAAQ,GAnHhNlD,EAAE,CAAAmK,iBAAA,CAmH2NnK,EAAE,CAACiB,iBAAiB;IAAA,CAA4C;EAAE;EAC/X;IAAS,IAAI,CAAC2N,IAAI,kBApH8E5O,EAAE,CAAA6O,iBAAA;MAAArE,IAAA,EAoHJ2C;IAAmB,EAAiB;EAAE;AACxI;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAtHoGrM,EAAE,CAAAsM,iBAAA,CAsHXa,mBAAmB,EAAc,CAAC;IACjH3C,IAAI,EAAEhK;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgK,IAAI,EAAExK,EAAE,CAACW;IAAY,CAAC,EAAE;MAAE6J,IAAI,EAAExK,EAAE,CAACwO;IAAyB,CAAC,EAAE;MAAEhE,IAAI,EAAExK,EAAE,CAACyO;IAAe,CAAC,EAAE;MAAEjE,IAAI,EAAExK,EAAE,CAAC0O;IAAS,CAAC,EAAE;MAAElE,IAAI,EAAExK,EAAE,CAAC2O;IAAiB,CAAC,EAAE;MAAEnE,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClNrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAACrJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEsH,IAAI,EAAExK,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;AACxD;AACA,MAAM6N,cAAc,SAAS3B,mBAAmB,CAAC;EAC7C;IAAS,IAAI,CAACnD,IAAI;MAAA,IAAA+E,2BAAA;MAAA,gBAAAC,uBAAA9E,CAAA;QAAA,QAAA6E,2BAAA,KAAAA,2BAAA,GA9H8E/O,EAAE,CAAAiP,qBAAA,CA8HQH,cAAc,IAAA5E,CAAA,IAAd4E,cAAc;MAAA;IAAA,GAAqD;EAAE;EAC/K;IAAS,IAAI,CAACF,IAAI,kBA/H8E5O,EAAE,CAAA6O,iBAAA;MAAArE,IAAA,EA+HJsE,cAAc;MAAArE,SAAA;MAAAW,QAAA,GA/HZpL,EAAE,CAAAkP,kBAAA,CA+HgE,CAAC;QAAEC,OAAO,EAAEjC,gBAAgB;QAAEkC,WAAW,EAAEN;MAAe,CAAC,CAAC,GA/H9H9O,EAAE,CAAAqL,0BAAA;IAAA,EA+HoK;EAAE;AAC5Q;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAjIoGrM,EAAE,CAAAsM,iBAAA,CAiIXwC,cAAc,EAAc,CAAC;IAC5GtE,IAAI,EAAEhK,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvC6C,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEjC,gBAAgB;QAAEkC,WAAW,EAAEN;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,iBAAiB,GAAG;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAEjM,OAAO,CAAC,eAAe,EAAE,CACpCC,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChBgM,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHhM,UAAU,CAAC,eAAe,EAAEC,OAAO,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IAC1EgM,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJhM,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;IAAEgM,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC/E,CAAC;EACF;AACJ;AACA;AACA;EACIE,WAAW,EAAEpM,OAAO,CAAC,aAAa,EAAE;EAChC;EACA;EACAC,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IAAEgM,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EACvC/L,UAAU,CAAC,WAAW,EAAE,CACpBD,KAAK,CAAC;IAAEgM,OAAO,EAAE;EAAE,CAAC,CAAC,EACrB9L,OAAO,CAAC,8CAA8C,CAAC,CAC1D,CAAC,CACL;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMgM,WAAW,GAAGJ,iBAAiB,CAACI,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMH,aAAa,GAAGD,iBAAiB,CAACC,aAAa;AAErD,IAAII,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,wBAAwB,GAAG,IAAI3P,cAAc,CAAC,0BAA0B,EAAE;EAC5E4P,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,gCAAgCA,CAAA,EAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA;AACA,MAAMC,YAAY,CAAC;EACf;EACA,IAAIH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;EACA,IAAIJ,SAASA,CAACK,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAOjE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjDS,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAACuD,UAAU,GAAGC,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIL,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACM,UAAU;EAC1B;EACA,IAAIN,SAASA,CAACI,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAOjE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3FW,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAACwD,UAAU,GAAGF,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIP,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACS,eAAe;EAC/B;EACA,IAAIT,cAAcA,CAACM,KAAK,EAAE;IACtB,IAAI,CAACG,eAAe,GAAGjP,qBAAqB,CAAC8O,KAAK,CAAC;EACvD;EACA;EACA,IAAII,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACJ,KAAK,EAAE;IACnB,IAAI,CAACK,YAAY,GAAGnP,qBAAqB,CAAC8O,KAAK,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIM,UAAUA,CAACC,OAAO,EAAE;IACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IACnD,IAAID,kBAAkB,IAAIA,kBAAkB,CAACzH,MAAM,EAAE;MACjDyH,kBAAkB,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACjD,IAAI,CAAC7K,UAAU,CAAC6K,SAAS,CAAC,GAAG,KAAK;MACtC,CAAC,CAAC;IACN;IACA,IAAI,CAACH,mBAAmB,GAAGF,OAAO;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAACxH,MAAM,EAAE;MAC3BwH,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACtC,IAAI,CAAC7K,UAAU,CAAC6K,SAAS,CAAC,GAAG,IAAI;MACrC,CAAC,CAAC;MACF,IAAI,CAAClK,WAAW,CAACyB,aAAa,CAACyI,SAAS,GAAG,EAAE;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACP,UAAU;EAC1B;EACA,IAAIO,SAASA,CAACN,OAAO,EAAE;IACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;EAC7B;EACA9J,WAAWA,CAACC,WAAW,EAAEoK,OAAO,EAAEC,cAAc;EAChD;EACAjK,kBAAkB,EAAE;IAChB,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACoK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChK,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACkK,sBAAsB,GAAG,IAAI7Q,SAAS,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC4F,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACC,oBAAoB,GAAG,MAAM;IAClC;IACA,IAAI,CAACiL,cAAc,GAAG,IAAItP,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACwD,MAAM,GAAG,IAAI/E,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC8Q,KAAK,GAAG,IAAI,CAAC/L,MAAM;IACxB,IAAI,CAACW,OAAO,GAAI,kBAAiBuJ,YAAY,EAAG,EAAC;IACjD,IAAI,CAAC8B,iBAAiB,GAAGJ,cAAc,CAACI,iBAAiB,IAAI,EAAE;IAC/D,IAAI,CAACpB,UAAU,GAAGgB,cAAc,CAACpB,SAAS;IAC1C,IAAI,CAACO,UAAU,GAAGa,cAAc,CAACnB,SAAS;IAC1C,IAAI,CAACC,aAAa,GAAGkB,cAAc,CAAClB,aAAa;IACjD,IAAI,CAACM,eAAe,GAAGY,cAAc,CAACrB,cAAc;IACpD,IAAI,CAACW,YAAY,GAAGU,cAAc,CAACX,WAAW;EAClD;EACAgB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnB,kBAAkB,CAAC,CAAC;EAC7B;EACAoB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAIxQ,eAAe,CAAC,IAAI,CAACiQ,sBAAsB,CAAC,CAC9DQ,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,WAAW,CAACI,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACzM,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE;IACA;IACA;IACA,IAAI,CAAC4L,sBAAsB,CAACa,OAAO,CAC9BC,IAAI,CAAC9P,SAAS,CAAC,IAAI,CAACgP,sBAAsB,CAAC,EAAE/O,SAAS,CAAC8P,KAAK,IAAInQ,KAAK,CAAC,GAAGmQ,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAChL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9G2K,SAAS,CAACM,WAAW,IAAI,IAAI,CAACX,WAAW,CAACY,gBAAgB,CAACD,WAAW,CAAC,CAAC;IAC7E,IAAI,CAAClB,sBAAsB,CAACa,OAAO,CAACD,SAAS,CAAEQ,SAAS,IAAK;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,IAAI,CAACd,WAAW;MAChC,IAAI,IAAI,CAACvL,oBAAoB,KAAK,OAAO,IAAIqM,OAAO,CAACC,UAAU,EAAE9I,SAAS,CAAC,CAAC,EAAE;QAC1E,MAAMuI,KAAK,GAAGK,SAAS,CAACG,OAAO,CAAC,CAAC;QACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACZ,KAAK,CAAChJ,MAAM,GAAG,CAAC,EAAEsJ,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;QACnF,IAAIb,KAAK,CAACS,KAAK,CAAC,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC,CAACtK,QAAQ,EAAE;UACxCmK,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;QAChC,CAAC,MACI;UACDH,OAAO,CAACS,iBAAiB,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;EACN;EACAjL,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0J,WAAW,EAAEwB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC/B,sBAAsB,CAAC+B,OAAO,CAAC,CAAC;IACrC,IAAI,CAAC5N,MAAM,CAAC6C,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACgL,2BAA2B,EAAEC,WAAW,CAAC,CAAC;EACnD;EACA;EACAjM,QAAQA,CAAA,EAAG;IACP;IACA,MAAMkM,WAAW,GAAG,IAAI,CAAClC,sBAAsB,CAACa,OAAO;IACvD,OAAOqB,WAAW,CAACpB,IAAI,CAAC9P,SAAS,CAAC,IAAI,CAACgP,sBAAsB,CAAC,EAAE/O,SAAS,CAAC8P,KAAK,IAAInQ,KAAK,CAAC,GAAGmQ,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACjL,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,OAAOA,CAAC+L,KAAK,EAAE,CAAE;EACjB;AACJ;AACA;AACA;AACA;AACA;EACIpL,UAAUA,CAACoL,KAAK,EAAE,CAAE;EACpB;EACAnO,cAAcA,CAACqD,KAAK,EAAE;IAClB,MAAM+K,OAAO,GAAG/K,KAAK,CAAC+K,OAAO;IAC7B,MAAMf,OAAO,GAAG,IAAI,CAACd,WAAW;IAChC,QAAQ6B,OAAO;MACX,KAAK7R,MAAM;QACP,IAAI,CAACC,cAAc,CAAC6G,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAI,CAACnD,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAK9D,UAAU;QACX,IAAI,IAAI,CAAC+R,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACnO,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAK/D,WAAW;QACZ,IAAI,IAAI,CAACgS,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAACnO,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ;QACI,IAAIgO,OAAO,KAAKjS,QAAQ,IAAIiS,OAAO,KAAKhS,UAAU,EAAE;UAChDiR,OAAO,CAACkB,cAAc,CAAC,UAAU,CAAC;QACtC;QACAlB,OAAO,CAACmB,SAAS,CAACnL,KAAK,CAAC;QACxB;IACR;IACA;IACA;IACAA,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACIkL,cAAcA,CAACnM,MAAM,GAAG,SAAS,EAAE;IAC/B;IACA,IAAI,CAAC0L,2BAA2B,EAAEC,WAAW,CAAC,CAAC;IAC/C,IAAI,CAACD,2BAA2B,GAAG,IAAI,CAAClC,OAAO,CAAC4C,QAAQ,CAAC5B,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0P,SAAS,CAAC,MAAM;MACnF,IAAI+B,SAAS,GAAG,IAAI;MACpB,IAAI,IAAI,CAAC3C,sBAAsB,CAACjI,MAAM,EAAE;QACpC;QACA;QACA;QACA;QACA4K,SAAS,GAAG,IAAI,CAAC3C,sBAAsB,CAAC4C,KAAK,CAACnM,eAAe,CAAC,CAAC,CAACoM,OAAO,CAAC,eAAe,CAAC;MAC5F;MACA;MACA,IAAI,CAACF,SAAS,IAAI,CAACA,SAAS,CAACG,QAAQ,CAACC,QAAQ,CAACtK,aAAa,CAAC,EAAE;QAC3D,MAAM4I,OAAO,GAAG,IAAI,CAACd,WAAW;QAChCc,OAAO,CAACkB,cAAc,CAACjM,MAAM,CAAC,CAAC0M,kBAAkB,CAAC,CAAC;QACnD;QACA;QACA;QACA,IAAI,CAAC3B,OAAO,CAACC,UAAU,IAAIqB,SAAS,EAAE;UAClCA,SAAS,CAACtM,KAAK,CAAC,CAAC;QACrB;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI4M,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC1C,WAAW,CAACsB,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIqB,YAAYA,CAACC,KAAK,EAAE;IAChB;IACA;IACA,MAAMC,SAAS,GAAG3B,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC0B,cAAc,GAAGF,KAAK,EAAE,EAAE,CAAC;IAC3D,MAAMG,YAAY,GAAI,GAAE,IAAI,CAACC,gBAAiB,GAAEH,SAAU,EAAC;IAC3D,MAAMI,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3O,UAAU,CAAC,CAAC4O,IAAI,CAAC/D,SAAS,IAAI;MACnE,OAAOA,SAAS,CAACgE,UAAU,CAAC,IAAI,CAACL,gBAAgB,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACC,eAAe,IAAIA,eAAe,KAAK,IAAI,CAACK,kBAAkB,EAAE;MACjE,IAAI,IAAI,CAACA,kBAAkB,EAAE;QACzB,IAAI,CAAC9O,UAAU,CAAC,IAAI,CAAC8O,kBAAkB,CAAC,GAAG,KAAK;MACpD;MACA,IAAI,CAAC9O,UAAU,CAACuO,YAAY,CAAC,GAAG,IAAI;MACpC,IAAI,CAACO,kBAAkB,GAAGP,YAAY;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIrE,kBAAkBA,CAAC6E,IAAI,GAAG,IAAI,CAACnF,SAAS,EAAEoF,IAAI,GAAG,IAAI,CAACnF,SAAS,EAAE;IAC7D,MAAMW,OAAO,GAAG,IAAI,CAACxK,UAAU;IAC/BwK,OAAO,CAAC,iBAAiB,CAAC,GAAGuE,IAAI,KAAK,QAAQ;IAC9CvE,OAAO,CAAC,gBAAgB,CAAC,GAAGuE,IAAI,KAAK,OAAO;IAC5CvE,OAAO,CAAC,gBAAgB,CAAC,GAAGwE,IAAI,KAAK,OAAO;IAC5CxE,OAAO,CAAC,gBAAgB,CAAC,GAAGwE,IAAI,KAAK,OAAO;IAC5C;IACA,IAAI,CAACjO,kBAAkB,EAAEuC,YAAY,CAAC,CAAC;EAC3C;EACA;EACA2L,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAAChP,oBAAoB,GAAG,OAAO;EACvC;EACA;EACAiP,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACjP,oBAAoB,GAAG,MAAM;EACtC;EACA;EACAN,gBAAgBA,CAAC2C,KAAK,EAAE;IACpB,IAAI,CAAC4I,cAAc,CAACvJ,IAAI,CAACW,KAAK,CAAC;IAC/B,IAAI,CAAC6M,YAAY,GAAG,KAAK;EAC7B;EACA3P,iBAAiBA,CAAC8C,KAAK,EAAE;IACrB,IAAI,CAAC6M,YAAY,GAAG,IAAI;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI7M,KAAK,CAAC8M,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC5D,WAAW,CAACqB,eAAe,KAAK,CAAC,EAAE;MACrEvK,KAAK,CAACqF,OAAO,CAAC0H,SAAS,GAAG,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9D,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC+D,SAAS,CAACxD,OAAO,CACjBC,IAAI,CAAC9P,SAAS,CAAC,IAAI,CAACqT,SAAS,CAAC,CAAC,CAC/BzD,SAAS,CAAEG,KAAK,IAAK;MACtB,IAAI,CAACf,sBAAsB,CAACsE,KAAK,CAACvD,KAAK,CAAC3P,MAAM,CAAC6P,IAAI,IAAIA,IAAI,CAACpL,WAAW,KAAK,IAAI,CAAC,CAAC;MAClF,IAAI,CAACmK,sBAAsB,CAACuE,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC7L,IAAI,YAAA8L,qBAAA5L,CAAA;MAAA,YAAAA,CAAA,IAAwFkG,YAAY,EAjgBtBpQ,EAAE,CAAAmK,iBAAA,CAigBsCnK,EAAE,CAACoK,UAAU,GAjgBrDpK,EAAE,CAAAmK,iBAAA,CAigBgEnK,EAAE,CAAC+V,MAAM,GAjgB3E/V,EAAE,CAAAmK,iBAAA,CAigBsFyF,wBAAwB,GAjgBhH5P,EAAE,CAAAmK,iBAAA,CAigB2HnK,EAAE,CAACiB,iBAAiB;IAAA,CAA4C;EAAE;EAC/R;IAAS,IAAI,CAAC2N,IAAI,kBAlgB8E5O,EAAE,CAAA6O,iBAAA;MAAArE,IAAA,EAkgBJ4F,YAAY;MAAA4F,cAAA,WAAAC,4BAAA7R,EAAA,EAAAC,GAAA,EAAA6R,QAAA;QAAA,IAAA9R,EAAA;UAlgBVpE,EAAE,CAAAmW,cAAA,CAAAD,QAAA,EAkgB0ehJ,gBAAgB;UAlgB5flN,EAAE,CAAAmW,cAAA,CAAAD,QAAA,EAkgByjBpP,WAAW;UAlgBtkB9G,EAAE,CAAAmW,cAAA,CAAAD,QAAA,EAkgB+nBpP,WAAW;QAAA;QAAA,IAAA1C,EAAA;UAAA,IAAAgS,EAAA;UAlgB5oBpW,EAAE,CAAAqW,cAAA,CAAAD,EAAA,GAAFpW,EAAE,CAAAsW,WAAA,QAAAjS,GAAA,CAAAkS,WAAA,GAAAH,EAAA,CAAAlC,KAAA;UAAFlU,EAAE,CAAAqW,cAAA,CAAAD,EAAA,GAAFpW,EAAE,CAAAsW,WAAA,QAAAjS,GAAA,CAAAsR,SAAA,GAAAS,EAAA;UAAFpW,EAAE,CAAAqW,cAAA,CAAAD,EAAA,GAAFpW,EAAE,CAAAsW,WAAA,QAAAjS,GAAA,CAAAgO,KAAA,GAAA+D,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAC,mBAAArS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpE,EAAE,CAAA0W,WAAA,CAkgBotB/V,WAAW;QAAA;QAAA,IAAAyD,EAAA;UAAA,IAAAgS,EAAA;UAlgBjuBpW,EAAE,CAAAqW,cAAA,CAAAD,EAAA,GAAFpW,EAAE,CAAAsW,WAAA,QAAAjS,GAAA,CAAAsS,WAAA,GAAAP,EAAA,CAAAlC,KAAA;QAAA;MAAA;MAAAjJ,MAAA;QAAAkF,aAAA;QAAA3J,SAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAuJ,SAAA;QAAAC,SAAA;QAAAF,cAAA;QAAAU,WAAA;QAAAE,UAAA;QAAAO,SAAA;MAAA;MAAAyF,OAAA;QAAAnR,MAAA;QAAA+L,KAAA;MAAA;IAAA,EAkgBswB;EAAE;AAC92B;AACA;EAAA,QAAAnF,SAAA,oBAAAA,SAAA,KApgBoGrM,EAAE,CAAAsM,iBAAA,CAogBX8D,YAAY,EAAc,CAAC;IAC1G5F,IAAI,EAAEhK;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgK,IAAI,EAAExK,EAAE,CAACoK;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAExK,EAAE,CAAC+V;IAAO,CAAC,EAAE;MAAEvL,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5GrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAACqD,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAEpF,IAAI,EAAExK,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0U,SAAS,EAAE,CAAC;MACxEnL,IAAI,EAAE5J,eAAe;MACrB2L,IAAI,EAAE,CAACzF,WAAW,EAAE;QAAE+P,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAE1G,aAAa,EAAE,CAAC;MAChB3F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEiG,SAAS,EAAE,CAAC;MACZgE,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE9F,cAAc,EAAE,CAAC;MACjB+D,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE7F,eAAe,EAAE,CAAC;MAClB8D,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE0D,SAAS,EAAE,CAAC;MACZzF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE2P,SAAS,EAAE,CAAC;MACZ1F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEoW,WAAW,EAAE,CAAC;MACdnM,IAAI,EAAE3J,SAAS;MACf0L,IAAI,EAAE,CAAC5L,WAAW;IACtB,CAAC,CAAC;IAAE0R,KAAK,EAAE,CAAC;MACR7H,IAAI,EAAE5J,eAAe;MACrB2L,IAAI,EAAE,CAACzF,WAAW,EAAE;QAAE+P,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEN,WAAW,EAAE,CAAC;MACd/L,IAAI,EAAE1J,YAAY;MAClByL,IAAI,EAAE,CAACW,gBAAgB;IAC3B,CAAC,CAAC;IAAE8C,cAAc,EAAE,CAAC;MACjBxF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEmQ,WAAW,EAAE,CAAC;MACdlG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEqQ,UAAU,EAAE,CAAC;MACbpG,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE4E,SAAS,EAAE,CAAC;MACZ3G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEkF,MAAM,EAAE,CAAC;MACT+E,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAEyQ,KAAK,EAAE,CAAC;MACRhH,IAAI,EAAEzJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM+V,OAAO,SAAS1G,YAAY,CAAC;EAC/BrJ,WAAWA,CAACC,WAAW,EAAEoK,OAAO,EAAE2F,eAAe,EAAEC,iBAAiB,EAAE;IAClE,KAAK,CAAChQ,WAAW,EAAEoK,OAAO,EAAE2F,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACnC,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACF,cAAc,GAAG,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC3K,IAAI,YAAAiN,gBAAA/M,CAAA;MAAA,YAAAA,CAAA,IAAwF4M,OAAO,EAxjBjB9W,EAAE,CAAAmK,iBAAA,CAwjBiCnK,EAAE,CAACoK,UAAU,GAxjBhDpK,EAAE,CAAAmK,iBAAA,CAwjB2DnK,EAAE,CAAC+V,MAAM,GAxjBtE/V,EAAE,CAAAmK,iBAAA,CAwjBiFyF,wBAAwB,GAxjB3G5P,EAAE,CAAAmK,iBAAA,CAwjBsHnK,EAAE,CAACiB,iBAAiB;IAAA,CAA4C;EAAE;EAC1R;IAAS,IAAI,CAACqJ,IAAI,kBAzjB8EtK,EAAE,CAAAuK,iBAAA;MAAAC,IAAA,EAyjBJsM,OAAO;MAAArM,SAAA;MAAAC,SAAA,sBAAiE,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAsM,qBAAA9S,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzjBxEpE,EAAE,CAAAuG,WAAA;QAAA;MAAA;MAAA4E,QAAA;MAAAC,QAAA,GAAFpL,EAAE,CAAAkP,kBAAA,CAyjBmM,CAAC;QAAEC,OAAO,EAAEvI,cAAc;QAAEwI,WAAW,EAAE0H;MAAQ,CAAC,CAAC,GAzjBxP9W,EAAE,CAAAqL,0BAAA;MAAAE,kBAAA,EAAA5E,GAAA;MAAA6E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwL,iBAAA/S,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpE,EAAE,CAAA6L,eAAA;UAAF7L,EAAE,CAAA8L,UAAA,IAAAlH,8BAAA,qBAyjB4+B,CAAC;QAAA;MAAA;MAAAoH,YAAA,GAA0oG/I,EAAE,CAACmU,OAAO;MAAAC,MAAA;MAAAlL,aAAA;MAAAmL,IAAA;QAAAC,SAAA,EAAsE,CAACjI,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACI,WAAW;MAAC;MAAAtD,eAAA;IAAA,EAAiG;EAAE;AACh9I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3jBoGrM,EAAE,CAAAsM,iBAAA,CA2jBXwK,OAAO,EAAc,CAAC;IACrGtM,IAAI,EAAEtK,SAAS;IACfqM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEJ,eAAe,EAAEjM,uBAAuB,CAACuM,MAAM;MAAEP,aAAa,EAAE/L,iBAAiB,CAACuM,IAAI;MAAExB,QAAQ,EAAE,SAAS;MAAEsB,IAAI,EAAE;QACtI,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE,MAAM;QACjC,iBAAiB,EAAE;MACvB,CAAC;MAAE+K,UAAU,EAAE,CAAClI,iBAAiB,CAACC,aAAa,EAAED,iBAAiB,CAACI,WAAW,CAAC;MAAEL,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEvI,cAAc;QAAEwI,WAAW,EAAE0H;MAAQ,CAAC,CAAC;MAAEnL,QAAQ,EAAE,krBAAkrB;MAAE0L,MAAM,EAAE,CAAC,+kGAA+kG;IAAE,CAAC;EACt7H,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7M,IAAI,EAAExK,EAAE,CAACoK;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAExK,EAAE,CAAC+V;IAAO,CAAC,EAAE;MAAEvL,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5GrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAACqD,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAEpF,IAAI,EAAExK,EAAE,CAACiB;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExD;AACA,MAAMwW,wBAAwB,GAAG,IAAIxX,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA,SAASyX,gCAAgCA,CAACC,OAAO,EAAE;EAC/C,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAMC,yCAAyC,GAAG;EAC9C3I,OAAO,EAAEsI,wBAAwB;EACjCM,IAAI,EAAE,CAAClU,OAAO,CAAC;EACfmU,UAAU,EAAEN;AAChB,CAAC;AACD;AACA,MAAMO,2BAA2B,GAAGjU,+BAA+B,CAAC;EAAEkU,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;EACI,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,4BAA4BA,CAACE,CAAC,EAAE;IAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;EACjB;EACA;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACX,IAAIA,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,sBAAsB,CAAClF,WAAW,CAAC,CAAC;IACzC,IAAI+E,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACI,mBAAmB,KAAK,OAAOrM,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACtFY,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAI,CAACwL,sBAAsB,GAAGH,IAAI,CAAC9G,KAAK,CAACU,SAAS,CAAEyG,MAAM,IAAK;QAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QACzB;QACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACD,mBAAmB,EAAE;UACtE,IAAI,CAACA,mBAAmB,CAACjT,MAAM,CAACC,IAAI,CAACiT,MAAM,CAAC;QAChD;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACE,iBAAiB,EAAEjP,mBAAmB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;EACvE;EACA9C,WAAWA,CAAC+R,QAAQ,EAAEC,QAAQ,EAAEvL,iBAAiB,EAAEwL,cAAc,EAAErF,UAAU;EAC7E;EACA;EACAkF,iBAAiB,EAAEI,IAAI,EAAE/R,aAAa,EAAEkK,OAAO,EAAE;IAC7C,IAAI,CAAC0H,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACvL,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACqL,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC/R,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACkK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8H,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,2BAA2B,GAAGjX,YAAY,CAACkX,KAAK;IACrD,IAAI,CAACC,kBAAkB,GAAGnX,YAAY,CAACkX,KAAK;IAC5C,IAAI,CAACZ,sBAAsB,GAAGtW,YAAY,CAACkX,KAAK;IAChD,IAAI,CAACjS,kBAAkB,GAAGpG,MAAM,CAACC,iBAAiB,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACsY,iBAAiB,GAAI5Q,KAAK,IAAK;MAChC,IAAI,CAACrH,gCAAgC,CAACqH,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAC6Q,SAAS,GAAG,OAAO;MAC5B;IACJ,CAAC;IACD;IACA;IACA,IAAI,CAACA,SAAS,GAAG5M,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6M,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,UAAU,GAAG,IAAIhZ,YAAY,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACiZ,UAAU,GAAG,IAAI,CAACD,UAAU;IACjC;IACA,IAAI,CAACE,UAAU,GAAG,IAAIlZ,YAAY,CAAC,CAAC;IACpC;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACmZ,WAAW,GAAG,IAAI,CAACD,UAAU;IAClC,IAAI,CAACE,eAAe,GAAGd,cAAc;IACrC,IAAI,CAACN,mBAAmB,GAAG/E,UAAU,YAAYvD,YAAY,GAAGuD,UAAU,GAAG/G,SAAS;IACtFmM,QAAQ,CAACtQ,aAAa,CAACsR,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACR,iBAAiB,EAAEtB,2BAA2B,CAAC;EAC9G;EACAtG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACqI,YAAY,CAAC,CAAC;EACvB;EACA7R,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC+Q,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC5K,OAAO,CAAC,CAAC;MAC1B,IAAI,CAAC4K,WAAW,GAAG,IAAI;IAC3B;IACA,IAAI,CAACH,QAAQ,CAACtQ,aAAa,CAACwR,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACV,iBAAiB,EAAEtB,2BAA2B,CAAC;IAClH,IAAI,CAACQ,sBAAsB,CAAClF,WAAW,CAAC,CAAC;IACzC,IAAI,CAAC6F,2BAA2B,CAAC7F,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC+F,kBAAkB,CAAC/F,WAAW,CAAC,CAAC;EACzC;EACA;EACA,IAAI2G,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACf,SAAS;EACzB;EACA;EACA,IAAIgB,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3I,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAzG,eAAeA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,IAAI,CAACgP,iBAAiB,IAAI,IAAI,CAACH,mBAAmB,IAAI,IAAI,CAACJ,IAAI,CAAC;EAC9E;EACA;EACA8B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAA,QAAQA,CAAA,EAAG;IACP,MAAMhC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAACa,SAAS,IAAI,CAACb,IAAI,EAAE;MACzB;IACJ;IACA,MAAMiC,UAAU,GAAG,IAAI,CAACC,cAAc,CAAClC,IAAI,CAAC;IAC5C,MAAMmC,aAAa,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC;IAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;IACvD,IAAI,CAACC,YAAY,CAACtC,IAAI,EAAEqC,gBAAgB,CAAC;IACzCF,aAAa,CAAC/J,WAAW,GACrB4H,IAAI,CAAC5H,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC7G,eAAe,CAAC,CAAC,GAAGyO,IAAI,CAAC5H,WAAW;IACzE6J,UAAU,CAAC7M,MAAM,CAAC,IAAI,CAACmN,UAAU,CAACvC,IAAI,CAAC,CAAC;IACxC,IAAIA,IAAI,CAAC/B,WAAW,EAAE;MAClB+B,IAAI,CAAC/B,WAAW,CAAC7I,MAAM,CAAC,IAAI,CAACoN,QAAQ,CAAC;IAC1C;IACA,IAAI,CAAC1B,2BAA2B,GAAG,IAAI,CAAC2B,mBAAmB,CAAC,CAAC,CAAC7I,SAAS,CAAC,MAAM,IAAI,CAACmI,SAAS,CAAC,CAAC,CAAC;IAC/F,IAAI,CAACW,SAAS,CAAC1C,IAAI,CAAC;IACpB,IAAIA,IAAI,YAAYlI,YAAY,EAAE;MAC9BkI,IAAI,CAAChD,eAAe,CAAC,CAAC;MACtBgD,IAAI,CAAChH,sBAAsB,CAACa,OAAO,CAACC,IAAI,CAAC3P,SAAS,CAAC6V,IAAI,CAAC9G,KAAK,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;QAC5E;QACA;QACAyI,gBAAgB,CAACM,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,CAAC,CAAC;QAChEP,gBAAgB,CAACM,kBAAkB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ;EACA;EACAZ,SAASA,CAAA,EAAG;IACR,IAAI,CAAC/B,IAAI,EAAE9G,KAAK,CAAC9L,IAAI,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACIiC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACX,aAAa,IAAIU,MAAM,EAAE;MAC9B,IAAI,CAACV,aAAa,CAACY,QAAQ,CAAC,IAAI,CAACiR,QAAQ,EAAEnR,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACkR,QAAQ,CAACtQ,aAAa,CAACd,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;EACIsT,cAAcA,CAAA,EAAG;IACb,IAAI,CAACjC,WAAW,EAAEiC,cAAc,CAAC,CAAC;EACtC;EACA;EACAvC,YAAYA,CAACD,MAAM,EAAE;IACjB,IAAI,CAAC,IAAI,CAACO,WAAW,IAAI,CAAC,IAAI,CAACgB,QAAQ,EAAE;MACrC;IACJ;IACA,MAAM5B,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACc,2BAA2B,CAAC7F,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC2F,WAAW,CAACrL,MAAM,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC4L,YAAY,KAAKd,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACa,SAAS,IAAI,CAAC,IAAI,CAAC3P,eAAe,CAAC,CAAC,CAAC,EAAE;MAC3F,IAAI,CAAClC,KAAK,CAAC,IAAI,CAAC6R,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,GAAG5M,SAAS;IAC1B,IAAI0L,IAAI,YAAYlI,YAAY,EAAE;MAC9BkI,IAAI,CAAC/C,eAAe,CAAC,CAAC;MACtB,IAAI+C,IAAI,CAAC/B,WAAW,EAAE;QAClB;QACA+B,IAAI,CAAC/G,cAAc,CACda,IAAI,CAAC1P,MAAM,CAACiG,KAAK,IAAIA,KAAK,CAAC8M,OAAO,KAAK,MAAM,CAAC,EAAEjT,IAAI,CAAC,CAAC,CAAC;QAC5D;QACAC,SAAS,CAAC6V,IAAI,CAAC/B,WAAW,CAAC9I,SAAS,CAAC,CAAC,CACjCyE,SAAS,CAAC;UACXlK,IAAI,EAAEA,CAAA,KAAMsQ,IAAI,CAAC/B,WAAW,CAAC1I,MAAM,CAAC,CAAC;UACrC;UACAvF,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAAC8S,cAAc,CAAC,KAAK;QAC7C,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC9B;IACJ,CAAC,MACI;MACD,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC;MAC1B9C,IAAI,EAAE/B,WAAW,EAAE1I,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACImN,SAASA,CAAC1C,IAAI,EAAE;IACZA,IAAI,CAAC3E,UAAU,GAAG,IAAI,CAAC9J,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC6O,mBAAmB,GAAG9L,SAAS;IAC/E0L,IAAI,CAAC1E,SAAS,GAAG,IAAI,CAACuG,GAAG;IACzB,IAAI,CAACkB,iBAAiB,CAAC/C,IAAI,CAAC;IAC5BA,IAAI,CAACvE,cAAc,CAAC,IAAI,CAACyF,SAAS,IAAI,SAAS,CAAC;IAChD,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC;EAC7B;EACA;EACAC,iBAAiBA,CAAC/C,IAAI,EAAE;IACpB,IAAIA,IAAI,CAAC9D,YAAY,EAAE;MACnB,IAAIC,KAAK,GAAG,CAAC;MACb,IAAId,UAAU,GAAG2E,IAAI,CAAC3E,UAAU;MAChC,OAAOA,UAAU,EAAE;QACfc,KAAK,EAAE;QACPd,UAAU,GAAGA,UAAU,CAACA,UAAU;MACtC;MACA2E,IAAI,CAAC9D,YAAY,CAACC,KAAK,CAAC;IAC5B;EACJ;EACA;EACA2G,cAAcA,CAACE,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK,IAAI,CAACnC,SAAS,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAGmC,MAAM;MACvB,IAAI,CAACnC,SAAS,GAAG,IAAI,CAACO,UAAU,CAAChU,IAAI,CAAC,CAAC,GAAG,IAAI,CAACkU,UAAU,CAAClU,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACmE,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACgP,iBAAiB,CAACpP,eAAe,CAAC6R,MAAM,CAAC;MAClD;MACA,IAAI,CAAClU,kBAAkB,CAACuC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACI6Q,cAAcA,CAAClC,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACY,WAAW,EAAE;MACnB,MAAMqC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAClD,IAAI,CAAC;MAC3C,IAAI,CAACmD,qBAAqB,CAACnD,IAAI,EAAEiD,MAAM,CAACZ,gBAAgB,CAAC;MACzD,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACJ,QAAQ,CAAC4C,MAAM,CAACH,MAAM,CAAC;MAC/C;MACA;MACA;MACA,IAAI,CAACrC,WAAW,CAACyC,aAAa,CAAC,CAAC,CAACzJ,SAAS,CAAC,CAAC;IAChD;IACA,OAAO,IAAI,CAACgH,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIsC,iBAAiBA,CAAClD,IAAI,EAAE;IACpB,OAAO,IAAIxU,aAAa,CAAC;MACrB6W,gBAAgB,EAAE,IAAI,CAAC7B,QAAQ,CAC1B8C,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC,IAAI,CAAC9C,QAAQ,CAAC,CAClCkC,kBAAkB,CAAC,CAAC,CACpBa,iBAAiB,CAAC,CAAC,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;MAClE5L,aAAa,EAAEmI,IAAI,CAACnI,aAAa,IAAI,kCAAkC;MACvES,UAAU,EAAE0H,IAAI,CAAC7G,iBAAiB;MAClCuH,cAAc,EAAE,IAAI,CAACc,eAAe,CAAC,CAAC;MACtClG,SAAS,EAAE,IAAI,CAACqF;IACpB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIwC,qBAAqBA,CAACnD,IAAI,EAAEsD,QAAQ,EAAE;IAClC,IAAItD,IAAI,CAAC/H,kBAAkB,EAAE;MACzBqL,QAAQ,CAACI,eAAe,CAAC9J,SAAS,CAAC+J,MAAM,IAAI;QACzC,MAAM7G,IAAI,GAAG6G,MAAM,CAACC,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;QAC5E,MAAM9G,IAAI,GAAG4G,MAAM,CAACC,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;QACzE;QACA;QACA;QACA,IAAI,IAAI,CAAChL,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACiL,GAAG,CAAC,MAAM/D,IAAI,CAAC/H,kBAAkB,CAAC6E,IAAI,EAAEC,IAAI,CAAC,CAAC;QAC/D,CAAC,MACI;UACDiD,IAAI,CAAC/H,kBAAkB,CAAC6E,IAAI,EAAEC,IAAI,CAAC;QACvC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIuF,YAAYA,CAACtC,IAAI,EAAEqC,gBAAgB,EAAE;IACjC,IAAI,CAAC2B,OAAO,EAAEC,eAAe,CAAC,GAAGjE,IAAI,CAACrI,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;IAClG,IAAI,CAACmM,QAAQ,EAAEI,gBAAgB,CAAC,GAAGlE,IAAI,CAACpI,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IACrG,IAAI,CAACuM,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACN,QAAQ,EAAEI,gBAAgB,CAAC;IAC7D,IAAI,CAACL,QAAQ,EAAEQ,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;IAC7D,IAAIK,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAAC/S,eAAe,CAAC,CAAC,EAAE;MACxB;MACA;MACA8S,gBAAgB,GAAGL,OAAO,GAAGhE,IAAI,CAACrI,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;MAC1EsM,eAAe,GAAGJ,QAAQ,GAAGG,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;MAChE,IAAI,IAAI,CAAC5D,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAACmE,mBAAmB,IAAI,IAAI,EAAE;UAClC,MAAMC,SAAS,GAAG,IAAI,CAACpE,mBAAmB,CAACrG,KAAK,CAAC6B,KAAK;UACtD,IAAI,CAAC2I,mBAAmB,GAAGC,SAAS,GAAGA,SAAS,CAAC/U,eAAe,CAAC,CAAC,CAACgV,SAAS,GAAG,CAAC;QACpF;QACAH,OAAO,GAAGR,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACS,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MAC1F;IACJ,CAAC,MACI,IAAI,CAACvE,IAAI,CAACtI,cAAc,EAAE;MAC3ByM,OAAO,GAAGL,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC/CM,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;IACnE;IACA7B,gBAAgB,CAACqC,aAAa,CAAC,CAC3B;MAAEV,OAAO;MAAEG,OAAO;MAAEN,QAAQ;MAAEC,QAAQ;MAAEQ;IAAQ,CAAC,EACjD;MAAEN,OAAO,EAAEC,eAAe;MAAEE,OAAO;MAAEN,QAAQ,EAAEQ,gBAAgB;MAAEP,QAAQ;MAAEQ;IAAQ,CAAC,EACpF;MACIN,OAAO;MACPG,OAAO,EAAEC,eAAe;MACxBP,QAAQ;MACRC,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,EACD;MACIN,OAAO,EAAEC,eAAe;MACxBE,OAAO,EAAEC,eAAe;MACxBP,QAAQ,EAAEQ,gBAAgB;MAC1BP,QAAQ,EAAEI,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,CACJ,CAAC;EACN;EACA;EACA7B,mBAAmBA,CAAA,EAAG;IAClB,MAAMkC,QAAQ,GAAG,IAAI,CAAC/D,WAAW,CAACgE,aAAa,CAAC,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACjE,WAAW,CAACiE,WAAW,CAAC,CAAC;IAClD,MAAMC,WAAW,GAAG,IAAI,CAAC1E,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACjT,MAAM,GAAGrD,EAAE,CAAC,CAAC;IACrF,MAAMib,KAAK,GAAG,IAAI,CAAC3E,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CAACpR,QAAQ,CAAC,CAAC,CAAC8K,IAAI,CAAC1P,MAAM,CAAC4a,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,CAAC,EAAEnW,MAAM,CAAC,MAAM,IAAI,CAACyW,SAAS,CAAC,CAAC,GAC3H/W,EAAE,CAAC,CAAC;IACV,OAAOF,KAAK,CAAC+a,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;EAC3D;EACA;EACAI,gBAAgBA,CAAC5U,KAAK,EAAE;IACpB,IAAI,CAACpH,+BAA+B,CAACoH,KAAK,CAAC,EAAE;MACzC;MACA;MACA,IAAI,CAAC6Q,SAAS,GAAG7Q,KAAK,CAAC6U,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG5Q,SAAS;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAAC/C,eAAe,CAAC,CAAC,EAAE;QACxBlB,KAAK,CAACC,cAAc,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA;EACAtD,cAAcA,CAACqD,KAAK,EAAE;IAClB,MAAM+K,OAAO,GAAG/K,KAAK,CAAC+K,OAAO;IAC7B;IACA,IAAIA,OAAO,KAAK3R,KAAK,IAAI2R,OAAO,KAAK1R,KAAK,EAAE;MACxC,IAAI,CAACwX,SAAS,GAAG,UAAU;IAC/B;IACA,IAAI,IAAI,CAAC3P,eAAe,CAAC,CAAC,KACpB6J,OAAO,KAAK/R,WAAW,IAAI,IAAI,CAACwY,GAAG,KAAK,KAAK,IAC1CzG,OAAO,KAAK9R,UAAU,IAAI,IAAI,CAACuY,GAAG,KAAK,KAAM,CAAC,EAAE;MACrD,IAAI,CAACX,SAAS,GAAG,UAAU;MAC3B,IAAI,CAACc,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;EACAmD,YAAYA,CAAC9U,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkB,eAAe,CAAC,CAAC,EAAE;MACxB;MACAlB,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB,IAAI,CAACyR,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAACF,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;EACAJ,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,CAAC,IAAI,CAACnQ,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC6O,mBAAmB,EAAE;MACtD;IACJ;IACA,IAAI,CAACY,kBAAkB,GAAG,IAAI,CAACZ,mBAAmB,CAC7CpR,QAAQ,CAAC;IACV;IACA;IACA;IAAA,CACC8K,IAAI,CAAC1P,MAAM,CAAC4a,MAAM,IAAIA,MAAM,KAAK,IAAI,CAACzE,iBAAiB,IAAI,CAACyE,MAAM,CAAC9U,QAAQ,CAAC,EAAE7F,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,CAAC,CACtG6P,SAAS,CAAC,MAAM;MACjB,IAAI,CAACsH,SAAS,GAAG,OAAO;MACxB;MACA;MACA;MACA,IAAI,IAAI,CAAClB,IAAI,YAAYlI,YAAY,IAAI,IAAI,CAACkI,IAAI,CAAC9C,YAAY,EAAE;QAC7D;QACA;QACA,IAAI,CAAC8C,IAAI,CAAC/G,cAAc,CACnBa,IAAI,CAAC5P,IAAI,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC,EAAEI,SAAS,CAAC,IAAI,CAACiW,mBAAmB,CAACpR,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtF4K,SAAS,CAAC,MAAM,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC;EACN;EACA;EACAO,UAAUA,CAACvC,IAAI,EAAE;IACb;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC1K,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+I,WAAW,KAAK2B,IAAI,CAAC3B,WAAW,EAAE;MAChE,IAAI,CAAC/I,OAAO,GAAG,IAAIxK,cAAc,CAACkV,IAAI,CAAC3B,WAAW,EAAE,IAAI,CAACnJ,iBAAiB,CAAC;IAC/E;IACA,OAAO,IAAI,CAACI,OAAO;EACvB;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAA0T,4BAAAxT,CAAA;MAAA,YAAAA,CAAA,IAAwFkO,mBAAmB,EA/gC7BpY,EAAE,CAAAmK,iBAAA,CA+gC6CvG,IAAI,CAACC,OAAO,GA/gC3D7D,EAAE,CAAAmK,iBAAA,CA+gCsEnK,EAAE,CAACoK,UAAU,GA/gCrFpK,EAAE,CAAAmK,iBAAA,CA+gCgGnK,EAAE,CAAC2O,gBAAgB,GA/gCrH3O,EAAE,CAAAmK,iBAAA,CA+gCgIsN,wBAAwB,GA/gC1JzX,EAAE,CAAAmK,iBAAA,CA+gCqKvD,cAAc,MA/gCrL5G,EAAE,CAAAmK,iBAAA,CA+gCgNrD,WAAW,OA/gC7N9G,EAAE,CAAAmK,iBAAA,CA+gCoQxG,IAAI,CAACga,cAAc,MA/gCzR3d,EAAE,CAAAmK,iBAAA,CA+gCoT/I,EAAE,CAACiJ,YAAY,GA/gCrUrK,EAAE,CAAAmK,iBAAA,CA+gCgVnK,EAAE,CAAC+V,MAAM;IAAA,CAA4C;EAAE;EACze;IAAS,IAAI,CAACnH,IAAI,kBAhhC8E5O,EAAE,CAAA6O,iBAAA;MAAArE,IAAA,EAghCJ4N,mBAAmB;MAAAzN,QAAA;MAAAC,YAAA,WAAAgT,iCAAAxZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhhCjBpE,EAAE,CAAA+E,UAAA,mBAAA8Y,6CAAA5Y,MAAA;YAAA,OAghCJZ,GAAA,CAAAoZ,YAAA,CAAAxY,MAAmB,CAAC;UAAA,yBAAA6Y,iDAAA7Y,MAAA;YAAA,OAApBZ,GAAA,CAAAkZ,gBAAA,CAAAtY,MAAuB,CAAC;UAAA,uBAAA8Y,+CAAA9Y,MAAA;YAAA,OAAxBZ,GAAA,CAAAiB,cAAA,CAAAL,MAAqB,CAAC;UAAA;QAAA;QAAA,IAAAb,EAAA;UAhhCpBpE,EAAE,CAAAuG,WAAA,kBAAAlC,GAAA,CAAAiU,IAAA,mCAAAjU,GAAA,CAAA6V,QAAA,mBAAA7V,GAAA,CAAA6V,QAAA,GAAA7V,GAAA,CAAAiU,IAAA,CAAAlS,OAAA;QAAA;MAAA;MAAA6E,MAAA;QAAAoN,4BAAA;QAAAC,IAAA;QAAAwC,QAAA;QAAArB,YAAA;MAAA;MAAA7C,OAAA;QAAA8C,UAAA;QAAAC,UAAA;QAAAC,UAAA;QAAAC,WAAA;MAAA;IAAA,EAghC8qB;EAAE;AACtxB;AACA;EAAA,QAAAxN,SAAA,oBAAAA,SAAA,KAlhCoGrM,EAAE,CAAAsM,iBAAA,CAkhCX8L,mBAAmB,EAAc,CAAC;IACjH5N,IAAI,EAAEhK,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCE,IAAI,EAAE;QACF,sBAAsB,EAAE,sBAAsB;QAC9C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,gCAAgC;QACxD,SAAS,EAAE,sBAAsB;QACjC,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE;MACjB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjC,IAAI,EAAE5G,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAE2G,IAAI,EAAExK,EAAE,CAACoK;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAExK,EAAE,CAAC2O;IAAiB,CAAC,EAAE;MAAEnE,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9IrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAACkL,wBAAwB;MACnC,CAAC;IAAE,CAAC,EAAE;MAAEjN,IAAI,EAAEoC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCrC,IAAI,EAAEnK,MAAM;QACZkM,IAAI,EAAE,CAAC3F,cAAc;MACzB,CAAC,EAAE;QACC4D,IAAI,EAAElK;MACV,CAAC;IAAE,CAAC,EAAE;MAAEkK,IAAI,EAAE1D,WAAW;MAAE+F,UAAU,EAAE,CAAC;QACpCrC,IAAI,EAAElK;MACV,CAAC,EAAE;QACCkK,IAAI,EAAEtJ;MACV,CAAC;IAAE,CAAC,EAAE;MAAEsJ,IAAI,EAAE7G,IAAI,CAACga,cAAc;MAAE9Q,UAAU,EAAE,CAAC;QAC5CrC,IAAI,EAAElK;MACV,CAAC;IAAE,CAAC,EAAE;MAAEkK,IAAI,EAAEpJ,EAAE,CAACiJ;IAAa,CAAC,EAAE;MAAEG,IAAI,EAAExK,EAAE,CAAC+V;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsC,4BAA4B,EAAE,CAAC;MAC3G7N,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAE+L,IAAI,EAAE,CAAC;MACP9N,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEuO,QAAQ,EAAE,CAAC;MACXtQ,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEkN,YAAY,EAAE,CAAC;MACfjP,IAAI,EAAEjK,KAAK;MACXgM,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEmN,UAAU,EAAE,CAAC;MACblP,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAE4Y,UAAU,EAAE,CAAC;MACbnP,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAE6Y,UAAU,EAAE,CAAC;MACbpP,IAAI,EAAEzJ;IACV,CAAC,CAAC;IAAE8Y,WAAW,EAAE,CAAC;MACdrP,IAAI,EAAEzJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMid,cAAc,SAAS5F,mBAAmB,CAAC;EAC7C;IAAS,IAAI,CAACpO,IAAI;MAAA,IAAAiU,2BAAA;MAAA,gBAAAC,uBAAAhU,CAAA;QAAA,QAAA+T,2BAAA,KAAAA,2BAAA,GAnkC8Eje,EAAE,CAAAiP,qBAAA,CAmkCQ+O,cAAc,IAAA9T,CAAA,IAAd8T,cAAc;MAAA;IAAA,GAAqD;EAAE;EAC/K;IAAS,IAAI,CAACpP,IAAI,kBApkC8E5O,EAAE,CAAA6O,iBAAA;MAAArE,IAAA,EAokCJwT,cAAc;MAAAvT,SAAA;MAAAC,SAAA;MAAAS,QAAA;MAAAC,QAAA,GApkCZpL,EAAE,CAAAqL,0BAAA;IAAA,EAokC2L;EAAE;AACnS;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAtkCoGrM,EAAE,CAAAsM,iBAAA,CAskCX0R,cAAc,EAAc,CAAC;IAC5GxT,IAAI,EAAEhK,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,6CAA4C;MACvDC,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDtB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMgT,aAAa,CAAC;EAChB;IAAS,IAAI,CAACnU,IAAI,YAAAoU,sBAAAlU,CAAA;MAAA,YAAAA,CAAA,IAAwFiU,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAnlC8Ere,EAAE,CAAAse,gBAAA;MAAA9T,IAAA,EAmlCS2T;IAAa,EAK9F;EAAE;EAC5B;IAAS,IAAI,CAACI,IAAI,kBAzlC8Eve,EAAE,CAAAwe,gBAAA;MAAAnP,SAAA,EAylCmC,CAACyI,yCAAyC,CAAC;MAAA2G,OAAA,GAAYtb,YAAY,EAAEJ,eAAe,EAAEC,eAAe,EAAEe,aAAa,EAAEE,mBAAmB,EACtQjB,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAAqJ,SAAA,oBAAAA,SAAA,KA5lCoGrM,EAAE,CAAAsM,iBAAA,CA4lCX6R,aAAa,EAAc,CAAC;IAC3G3T,IAAI,EAAErJ,QAAQ;IACdoL,IAAI,EAAE,CAAC;MACCkS,OAAO,EAAE,CAACtb,YAAY,EAAEJ,eAAe,EAAEC,eAAe,EAAEe,aAAa,CAAC;MACxE2a,OAAO,EAAE,CACLza,mBAAmB,EACnB6S,OAAO,EACP9T,eAAe,EACf8D,WAAW,EACXgI,cAAc,EACdkP,cAAc,CACjB;MACDW,YAAY,EAAE,CAAC7H,OAAO,EAAEhQ,WAAW,EAAEgI,cAAc,EAAEkP,cAAc,CAAC;MACpE3O,SAAS,EAAE,CAACyI,yCAAyC;IACzD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS5K,gBAAgB,EAAE0C,wBAAwB,EAAEhJ,cAAc,EAAE6Q,wBAAwB,EAAEK,yCAAyC,EAAEK,sBAAsB,EAAErB,OAAO,EAAEhI,cAAc,EAAEhI,WAAW,EAAEqX,aAAa,EAAEH,cAAc,EAAE5N,YAAY,EAAEjD,mBAAmB,EAAEiL,mBAAmB,EAAE1I,WAAW,EAAEJ,iBAAiB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}