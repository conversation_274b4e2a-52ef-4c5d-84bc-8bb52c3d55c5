{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class EmployerGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    if (this.authService.isEmployer()) {\n      return true;\n    } else {\n      this.router.navigate(['/jobs']);\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function EmployerGuard_Factory(t) {\n      return new (t || EmployerGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EmployerGuard,\n      factory: EmployerGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Employer<PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isEmployer", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\guards\\employer.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class EmployerGuard implements CanActivate {\r\n  \r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): boolean {\r\n    if (this.authService.isEmployer()) {\r\n      return true;\r\n    } else {\r\n      this.router.navigate(['/jobs']);\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAOA,OAAM,MAAOA,aAAa;EAExBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,IAAI,IAAI,CAACJ,WAAW,CAACK,UAAU,EAAE,EAAE;MACjC,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B,OAAO,KAAK;;EAEhB;;;uBAjBWR,aAAa,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAbd,aAAa;MAAAe,OAAA,EAAbf,aAAa,CAAAgB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}