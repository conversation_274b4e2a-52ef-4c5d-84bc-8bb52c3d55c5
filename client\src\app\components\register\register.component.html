<div class="register-container">
    <mat-card class="register-card">
        <mat-card-header>
            <mat-card-title>Create an Account</mat-card-title>
        </mat-card-header>
        <mat-card-content>
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
                <!-- Email -->
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Email</mat-label>
                    <input matInput formControlName="email" placeholder="Enter your email" required>
                    <mat-error *ngIf="f['email'].errors?.['required']">Email is required</mat-error>
                    <mat-error *ngIf="f['email'].errors?.['email']">Please enter a valid email address</mat-error>
                </mat-form-field>

                <!-- Password -->
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Password</mat-label>
                    <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password"
                        placeholder="Enter your password" required>
                    <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                    </button>
                    <mat-error *ngIf="f['password'].errors?.['required']">Password is required</mat-error>
                    <mat-error *ngIf="f['password'].errors?.['minlength']">Password must be at least 6
                        characters</mat-error>
                </mat-form-field>

                <!-- First Name -->
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>First Name</mat-label>
                    <input matInput formControlName="firstName" placeholder="Enter your first name" required>
                    <mat-error *ngIf="f['firstName'].errors?.['required']">First name is required</mat-error>
                </mat-form-field>

                <!-- Last Name -->
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Last Name</mat-label>
                    <input matInput formControlName="lastName" placeholder="Enter your last name" required>
                    <mat-error *ngIf="f['lastName'].errors?.['required']">Last name is required</mat-error>
                </mat-form-field>

                <!-- Account Type -->
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Account Type</mat-label>
                    <select matNativeControl formControlName="role" required>
                        <option value="job_seeker">Job Seeker</option>
                        <option value="employer">Employer</option>
                    </select>
                    <mat-error *ngIf="f['role'].errors?.['required']">Account type is required</mat-error>
                </mat-form-field>

                <div class="button-container">
                    <button mat-raised-button color="primary" type="submit"
                        [disabled]="registerForm.invalid || loading">
                        <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
                        <span *ngIf="!loading">Register</span>
                    </button>
                </div>
            </form>
        </mat-card-content>
        <mat-card-actions>
            <p>Already have an account? <a routerLink="/login">Login</a></p>
        </mat-card-actions>
    </mat-card>
</div>