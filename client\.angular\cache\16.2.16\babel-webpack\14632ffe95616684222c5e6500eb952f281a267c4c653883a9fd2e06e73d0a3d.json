{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(apiService, router) {\n    this.apiService = apiService;\n    this.router = router;\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(!!this.getUserFromStorage());\n    this.isAuthenticated = this.isAuthenticatedSubject.asObservable();\n  }\n  /**\n   * Get current user value\n   * @returns Current user\n   */\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Login user\n   * @param loginRequest Login request\n   * @returns Observable of auth response\n   */\n  login(loginRequest) {\n    return this.apiService.post('auth/login', loginRequest, false).pipe(tap(response => {\n      if (response.success && response.data) {\n        this.setSession(response.data.token, response.data.user);\n      }\n    }));\n  }\n  /**\n   * Register user\n   * @param registerRequest Register request\n   * @returns Observable of auth response\n   */\n  register(registerRequest) {\n    return this.apiService.post('auth/register', registerRequest, false).pipe(tap(response => {\n      if (response.success && response.data) {\n        this.setSession(response.data.token, response.data.user);\n      }\n    }));\n  }\n  /**\n   * Get current user profile\n   * @returns Observable of auth response\n   */\n  getCurrentUser() {\n    return this.apiService.get('auth/me').pipe(tap(response => {\n      if (response.success && response.data) {\n        this.currentUserSubject.next(response.data.user);\n      }\n    }));\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n  /**\n   * Check if user is authenticated\n   * @returns True if user is authenticated\n   */\n  checkAuth() {\n    const token = localStorage.getItem('token');\n    return !!token;\n  }\n  /**\n   * Check if user is an employer\n   * @returns True if user is an employer\n   */\n  isEmployer() {\n    const user = this.currentUserValue;\n    return user?.role === 'employer';\n  }\n  /**\n   * Check if user is a job seeker\n   * @returns True if user is a job seeker\n   */\n  isJobSeeker() {\n    const user = this.currentUserValue;\n    return user?.role === 'job_seeker';\n  }\n  /**\n   * Set session data\n   * @param token JWT token\n   * @param user User data\n   */\n  setSession(token, user) {\n    localStorage.setItem('token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n    this.currentUserSubject.next(user);\n    this.isAuthenticatedSubject.next(true);\n  }\n  /**\n   * Get user from storage\n   * @returns User from storage\n   */\n  getUserFromStorage() {\n    const userStr = localStorage.getItem('user');\n    if (userStr) {\n      try {\n        return JSON.parse(userStr);\n      } catch (e) {\n        return null;\n      }\n    }\n    return null;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "AuthService", "constructor", "apiService", "router", "currentUserSubject", "getUserFromStorage", "currentUser", "asObservable", "isAuthenticatedSubject", "isAuthenticated", "currentUserValue", "value", "login", "loginRequest", "post", "pipe", "response", "success", "data", "setSession", "token", "user", "register", "registerRequest", "getCurrentUser", "get", "next", "logout", "localStorage", "removeItem", "navigate", "checkAuth", "getItem", "isEmployer", "role", "isJobSeeker", "setItem", "JSON", "stringify", "userStr", "parse", "e", "i0", "ɵɵinject", "i1", "ApiService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\r\nimport { ApiService } from './api.service';\r\nimport { User, AuthResponse, LoginRequest, RegisterRequest } from '../models/user.model';\r\nimport { Router } from '@angular/router';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private currentUserSubject: BehaviorSubject<User | null>;\r\n  public currentUser: Observable<User | null>;\r\n  private isAuthenticatedSubject: BehaviorSubject<boolean>;\r\n  public isAuthenticated: Observable<boolean>;\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private router: Router\r\n  ) {\r\n    this.currentUserSubject = new BehaviorSubject<User | null>(this.getUserFromStorage());\r\n    this.currentUser = this.currentUserSubject.asObservable();\r\n    this.isAuthenticatedSubject = new BehaviorSubject<boolean>(!!this.getUserFromStorage());\r\n    this.isAuthenticated = this.isAuthenticatedSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get current user value\r\n   * @returns Current user\r\n   */\r\n  public get currentUserValue(): User | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  /**\r\n   * Login user\r\n   * @param loginRequest Login request\r\n   * @returns Observable of auth response\r\n   */\r\n  login(loginRequest: LoginRequest): Observable<AuthResponse> {\r\n    return this.apiService.post<AuthResponse>('auth/login', loginRequest, false)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.data) {\r\n            this.setSession(response.data.token, response.data.user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Register user\r\n   * @param registerRequest Register request\r\n   * @returns Observable of auth response\r\n   */\r\n  register(registerRequest: RegisterRequest): Observable<AuthResponse> {\r\n    return this.apiService.post<AuthResponse>('auth/register', registerRequest, false)\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.data) {\r\n            this.setSession(response.data.token, response.data.user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Get current user profile\r\n   * @returns Observable of auth response\r\n   */\r\n  getCurrentUser(): Observable<AuthResponse> {\r\n    return this.apiService.get<AuthResponse>('auth/me')\r\n      .pipe(\r\n        tap(response => {\r\n          if (response.success && response.data) {\r\n            this.currentUserSubject.next(response.data.user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Logout user\r\n   */\r\n  logout(): void {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n    this.currentUserSubject.next(null);\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  /**\r\n   * Check if user is authenticated\r\n   * @returns True if user is authenticated\r\n   */\r\n  checkAuth(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    return !!token;\r\n  }\r\n\r\n  /**\r\n   * Check if user is an employer\r\n   * @returns True if user is an employer\r\n   */\r\n  isEmployer(): boolean {\r\n    const user = this.currentUserValue;\r\n    return user?.role === 'employer';\r\n  }\r\n\r\n  /**\r\n   * Check if user is a job seeker\r\n   * @returns True if user is a job seeker\r\n   */\r\n  isJobSeeker(): boolean {\r\n    const user = this.currentUserValue;\r\n    return user?.role === 'job_seeker';\r\n  }\r\n\r\n  /**\r\n   * Set session data\r\n   * @param token JWT token\r\n   * @param user User data\r\n   */\r\n  private setSession(token: string, user: User): void {\r\n    localStorage.setItem('token', token);\r\n    localStorage.setItem('user', JSON.stringify(user));\r\n    this.currentUserSubject.next(user);\r\n    this.isAuthenticatedSubject.next(true);\r\n  }\r\n\r\n  /**\r\n   * Get user from storage\r\n   * @returns User from storage\r\n   */\r\n  private getUserFromStorage(): User | null {\r\n    const userStr = localStorage.getItem('user');\r\n    if (userStr) {\r\n      try {\r\n        return JSON.parse(userStr);\r\n      } catch (e) {\r\n        return null;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;;;;AAQvD,OAAM,MAAOC,WAAW;EAMtBC,YACUC,UAAsB,EACtBC,MAAc;IADd,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IAEd,IAAI,CAACC,kBAAkB,GAAG,IAAIN,eAAe,CAAc,IAAI,CAACO,kBAAkB,EAAE,CAAC;IACrF,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACzD,IAAI,CAACC,sBAAsB,GAAG,IAAIV,eAAe,CAAU,CAAC,CAAC,IAAI,CAACO,kBAAkB,EAAE,CAAC;IACvF,IAAI,CAACI,eAAe,GAAG,IAAI,CAACD,sBAAsB,CAACD,YAAY,EAAE;EACnE;EAEA;;;;EAIA,IAAWG,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACN,kBAAkB,CAACO,KAAK;EACtC;EAEA;;;;;EAKAC,KAAKA,CAACC,YAA0B;IAC9B,OAAO,IAAI,CAACX,UAAU,CAACY,IAAI,CAAe,YAAY,EAAED,YAAY,EAAE,KAAK,CAAC,CACzEE,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC,IAAI,CAACC,UAAU,CAACH,QAAQ,CAACE,IAAI,CAACE,KAAK,EAAEJ,QAAQ,CAACE,IAAI,CAACG,IAAI,CAAC;;IAE5D,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,QAAQA,CAACC,eAAgC;IACvC,OAAO,IAAI,CAACrB,UAAU,CAACY,IAAI,CAAe,eAAe,EAAES,eAAe,EAAE,KAAK,CAAC,CAC/ER,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC,IAAI,CAACC,UAAU,CAACH,QAAQ,CAACE,IAAI,CAACE,KAAK,EAAEJ,QAAQ,CAACE,IAAI,CAACG,IAAI,CAAC;;IAE5D,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAG,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtB,UAAU,CAACuB,GAAG,CAAe,SAAS,CAAC,CAChDV,IAAI,CACHhB,GAAG,CAACiB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC,IAAI,CAACd,kBAAkB,CAACsB,IAAI,CAACV,QAAQ,CAACE,IAAI,CAACG,IAAI,CAAC;;IAEpD,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAM,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,CAACzB,kBAAkB,CAACsB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAClB,sBAAsB,CAACkB,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACvB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;;;;EAIAC,SAASA,CAAA;IACP,MAAMX,KAAK,GAAGQ,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,CAAC,CAACZ,KAAK;EAChB;EAEA;;;;EAIAa,UAAUA,CAAA;IACR,MAAMZ,IAAI,GAAG,IAAI,CAACX,gBAAgB;IAClC,OAAOW,IAAI,EAAEa,IAAI,KAAK,UAAU;EAClC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,MAAMd,IAAI,GAAG,IAAI,CAACX,gBAAgB;IAClC,OAAOW,IAAI,EAAEa,IAAI,KAAK,YAAY;EACpC;EAEA;;;;;EAKQf,UAAUA,CAACC,KAAa,EAAEC,IAAU;IAC1CO,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;IACpCQ,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;IAClD,IAAI,CAACjB,kBAAkB,CAACsB,IAAI,CAACL,IAAI,CAAC;IAClC,IAAI,CAACb,sBAAsB,CAACkB,IAAI,CAAC,IAAI,CAAC;EACxC;EAEA;;;;EAIQrB,kBAAkBA,CAAA;IACxB,MAAMkC,OAAO,GAAGX,YAAY,CAACI,OAAO,CAAC,MAAM,CAAC;IAC5C,IAAIO,OAAO,EAAE;MACX,IAAI;QACF,OAAOF,IAAI,CAACG,KAAK,CAACD,OAAO,CAAC;OAC3B,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;;;IAGf,OAAO,IAAI;EACb;;;uBAvIWzC,WAAW,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX/C,WAAW;MAAAgD,OAAA,EAAXhD,WAAW,CAAAiD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}