.profile-container {
  margin-bottom: 30px;
}

.profile-header {
  margin-bottom: 20px;

  h1 {
    margin-bottom: 8px;
    font-weight: 500;
  }

  p {
    color: #666;
    margin: 0;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.profile-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.profile-card, .account-card {
  margin-bottom: 20px;
}

.profile-form {
  display: flex;
  flex-direction: column;
}

.form-section {
  margin-bottom: 24px;

  h2 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }
}

.form-row {
  display: flex;
  gap: 16px;

  mat-form-field {
    flex: 1;
  }
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.account-info {
  p {
    margin: 8px 0;
    font-size: 16px;
  }
}

mat-spinner {
  display: inline-block;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }
}