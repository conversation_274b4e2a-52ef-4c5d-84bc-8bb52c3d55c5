{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"First name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Last name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Account type is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction RegisterComponent_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Initialize form\n    this.registerForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      role: ['job_seeker', Validators.required],\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required]\n    });\n    // Redirect if already logged in\n    if (this.authService.checkAuth()) {\n      this.router.navigate(['/']);\n    }\n  }\n  // Convenience getter for easy access to form fields\n  get f() {\n    return this.registerForm.controls;\n  }\n  onSubmit() {\n    // Stop here if form is invalid\n    if (this.registerForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    const registerRequest = {\n      email: this.f['email'].value,\n      password: this.f['password'].value,\n      role: this.f['role'].value,\n      firstName: this.f['firstName'].value,\n      lastName: this.f['lastName'].value\n    };\n    this.authService.register(registerRequest).subscribe({\n      next: () => {\n        this.snackBar.open('Registration successful', 'Close', {\n          duration: 3000,\n          panelClass: ['success-snackbar']\n        });\n        this.router.navigate(['/']);\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Registration failed', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 50,\n      vars: 13,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", \"required\", \"\"], [\"matNativeControl\", \"\", \"formControlName\", \"role\", \"required\", \"\"], [\"value\", \"job_seeker\"], [\"value\", \"employer\"], [1, \"button-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"routerLink\", \"/login\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Create an Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(7, \"mat-form-field\", 3)(8, \"mat-label\");\n          i0.ɵɵtext(9, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 4);\n          i0.ɵɵtemplate(11, RegisterComponent_mat_error_11_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵtemplate(12, RegisterComponent_mat_error_12_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-form-field\", 3)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 6);\n          i0.ɵɵelementStart(17, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_17_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, RegisterComponent_mat_error_20_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵtemplate(21, RegisterComponent_mat_error_21_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 3)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 8);\n          i0.ɵɵtemplate(26, RegisterComponent_mat_error_26_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-form-field\", 3)(28, \"mat-label\");\n          i0.ɵɵtext(29, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 9);\n          i0.ɵɵtemplate(31, RegisterComponent_mat_error_31_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-form-field\", 3)(33, \"mat-label\");\n          i0.ɵɵtext(34, \"Account Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"select\", 10)(36, \"option\", 11);\n          i0.ɵɵtext(37, \"Job Seeker\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"option\", 12);\n          i0.ɵɵtext(39, \"Employer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, RegisterComponent_mat_error_40_Template, 2, 0, \"mat-error\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 13)(42, \"button\", 14);\n          i0.ɵɵtemplate(43, RegisterComponent_mat_spinner_43_Template, 1, 0, \"mat-spinner\", 15);\n          i0.ɵɵtemplate(44, RegisterComponent_span_44_Template, 2, 0, \"span\", 5);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"mat-card-actions\")(46, \"p\");\n          i0.ɵɵtext(47, \"Already have an account? \");\n          i0.ɵɵelementStart(48, \"a\", 16);\n          i0.ɵɵtext(49, \"Login\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"required\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"email\"]);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"required\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"minlength\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"firstName\"].errors == null ? null : ctx.f[\"firstName\"].errors[\"required\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"lastName\"].errors == null ? null : ctx.f[\"lastName\"].errors[\"required\"]);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"role\"].errors == null ? null : ctx.f[\"role\"].errors[\"required\"]);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatIcon, i11.MatProgressSpinner],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: calc(100vh - 64px);\\n  padding: 20px;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 100%;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 15px;\\n}\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n  margin-bottom: 10px;\\n}\\n\\nmat-card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 0 16px 16px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-right: 5px;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\na[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9yZWdpc3Rlci9yZWdpc3Rlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxhQUFBO0FBQ0Y7O0FBRUE7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxtQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0Esb0JBQUE7QUFDRjs7QUFFQTtFQUNFLHFCQUFBO0VBQ0EsaUJBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtBQUNGO0FBQ0U7RUFDRSwwQkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLnJlZ2lzdGVyLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2NHB4KTsgLy8gQWRqdXN0IGJhc2VkIG9uIHlvdXIgaGVhZGVyIGhlaWdodFxyXG4gIHBhZGRpbmc6IDIwcHg7XHJcbn1cclxuXHJcbi5yZWdpc3Rlci1jYXJkIHtcclxuICBtYXgtd2lkdGg6IDUwMHB4O1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4uZnVsbC13aWR0aCB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcclxufVxyXG5cclxuLmJ1dHRvbi1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG59XHJcblxyXG5tYXQtY2FyZC1hY3Rpb25zIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDAgMTZweCAxNnB4O1xyXG59XHJcblxyXG5tYXQtc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIG1hcmdpbi1yaWdodDogNXB4O1xyXG59XHJcblxyXG5hIHtcclxuICBjb2xvcjogIzNmNTFiNTtcclxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "loading", "hidePassword", "ngOnInit", "registerForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "role", "firstName", "lastName", "checkAuth", "navigate", "f", "controls", "onSubmit", "invalid", "registerRequest", "value", "register", "subscribe", "next", "open", "duration", "panelClass", "error", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_6_listener", "ɵɵtemplate", "RegisterComponent_mat_error_11_Template", "RegisterComponent_mat_error_12_Template", "RegisterComponent_Template_button_click_17_listener", "RegisterComponent_mat_error_20_Template", "RegisterComponent_mat_error_21_Template", "RegisterComponent_mat_error_26_Template", "RegisterComponent_mat_error_31_Template", "RegisterComponent_mat_error_40_Template", "RegisterComponent_mat_spinner_43_Template", "RegisterComponent_span_44_Template", "ɵɵadvance", "ɵɵproperty", "errors", "ɵɵtextInterpolate"], "sources": ["D:\\findit\\client\\src\\app\\components\\register\\register.component.ts", "D:\\findit\\client\\src\\app\\components\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { RegisterRequest } from '../../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-register',\r\n  templateUrl: './register.component.html',\r\n  styleUrls: ['./register.component.scss']\r\n})\r\nexport class RegisterComponent implements OnInit {\r\n  registerForm!: FormGroup;\r\n  loading = false;\r\n  hidePassword = true;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // Initialize form\r\n    this.registerForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      role: ['job_seeker', Validators.required],\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required]\r\n    });\r\n\r\n    // Redirect if already logged in\r\n    if (this.authService.checkAuth()) {\r\n      this.router.navigate(['/']);\r\n    }\r\n  }\r\n\r\n  // Convenience getter for easy access to form fields\r\n  get f() { return this.registerForm.controls; }\r\n\r\n  onSubmit(): void {\r\n    // Stop here if form is invalid\r\n    if (this.registerForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    const registerRequest: RegisterRequest = {\r\n      email: this.f['email'].value,\r\n      password: this.f['password'].value,\r\n      role: this.f['role'].value,\r\n      firstName: this.f['firstName'].value,\r\n      lastName: this.f['lastName'].value\r\n    };\r\n\r\n    this.authService.register(registerRequest)\r\n      .subscribe({\r\n        next: () => {\r\n          this.snackBar.open('Registration successful', 'Close', {\r\n            duration: 3000,\r\n            panelClass: ['success-snackbar']\r\n          });\r\n          this.router.navigate(['/']);\r\n        },\r\n        error: error => {\r\n          this.snackBar.open(error.error?.message || 'Registration failed', 'Close', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n          this.loading = false;\r\n        }\r\n      });\r\n  }\r\n}\r\n", "<div class=\"register-container\">\r\n    <mat-card class=\"register-card\">\r\n        <mat-card-header>\r\n            <mat-card-title>Create an Account</mat-card-title>\r\n        </mat-card-header>\r\n        <mat-card-content>\r\n            <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\r\n                <!-- Email -->\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>Email</mat-label>\r\n                    <input matInput formControlName=\"email\" placeholder=\"Enter your email\" required>\r\n                    <mat-error *ngIf=\"f['email'].errors?.['required']\">Email is required</mat-error>\r\n                    <mat-error *ngIf=\"f['email'].errors?.['email']\">Please enter a valid email address</mat-error>\r\n                </mat-form-field>\r\n\r\n                <!-- Password -->\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>Password</mat-label>\r\n                    <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\"\r\n                        placeholder=\"Enter your password\" required>\r\n                    <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\r\n                        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                    </button>\r\n                    <mat-error *ngIf=\"f['password'].errors?.['required']\">Password is required</mat-error>\r\n                    <mat-error *ngIf=\"f['password'].errors?.['minlength']\">Password must be at least 6\r\n                        characters</mat-error>\r\n                </mat-form-field>\r\n\r\n                <!-- First Name -->\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>First Name</mat-label>\r\n                    <input matInput formControlName=\"firstName\" placeholder=\"Enter your first name\" required>\r\n                    <mat-error *ngIf=\"f['firstName'].errors?.['required']\">First name is required</mat-error>\r\n                </mat-form-field>\r\n\r\n                <!-- Last Name -->\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>Last Name</mat-label>\r\n                    <input matInput formControlName=\"lastName\" placeholder=\"Enter your last name\" required>\r\n                    <mat-error *ngIf=\"f['lastName'].errors?.['required']\">Last name is required</mat-error>\r\n                </mat-form-field>\r\n\r\n                <!-- Account Type -->\r\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                    <mat-label>Account Type</mat-label>\r\n                    <select matNativeControl formControlName=\"role\" required>\r\n                        <option value=\"job_seeker\">Job Seeker</option>\r\n                        <option value=\"employer\">Employer</option>\r\n                    </select>\r\n                    <mat-error *ngIf=\"f['role'].errors?.['required']\">Account type is required</mat-error>\r\n                </mat-form-field>\r\n\r\n                <div class=\"button-container\">\r\n                    <button mat-raised-button color=\"primary\" type=\"submit\"\r\n                        [disabled]=\"registerForm.invalid || loading\">\r\n                        <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\r\n                        <span *ngIf=\"!loading\">Register</span>\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </mat-card-content>\r\n        <mat-card-actions>\r\n            <p>Already have an account? <a routerLink=\"/login\">Login</a></p>\r\n        </mat-card-actions>\r\n    </mat-card>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICU/CC,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAChFH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAW9FH,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACtFH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,6CACzC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO1BH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOzFH,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUvFH,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMlFH,EAAA,CAAAI,SAAA,sBAAyD;;;;;IACzDJ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD5C9D,OAAM,MAAOE,iBAAiB;EAK5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAPlB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,IAAI;EAOf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACzCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACiB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,IAAI,EAAE,CAAC,YAAY,EAAErB,UAAU,CAACkB,QAAQ,CAAC;MACzCI,SAAS,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACkB,QAAQ,CAAC;MACpCK,QAAQ,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACkB,QAAQ;KACnC,CAAC;IAEF;IACA,IAAI,IAAI,CAACT,WAAW,CAACe,SAAS,EAAE,EAAE;MAChC,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;;EAE/B;EAEA;EACA,IAAIC,CAACA,CAAA;IAAK,OAAO,IAAI,CAACX,YAAY,CAACY,QAAQ;EAAE;EAE7CC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACb,YAAY,CAACc,OAAO,EAAE;MAC7B;;IAGF,IAAI,CAACjB,OAAO,GAAG,IAAI;IAEnB,MAAMkB,eAAe,GAAoB;MACvCb,KAAK,EAAE,IAAI,CAACS,CAAC,CAAC,OAAO,CAAC,CAACK,KAAK;MAC5BZ,QAAQ,EAAE,IAAI,CAACO,CAAC,CAAC,UAAU,CAAC,CAACK,KAAK;MAClCV,IAAI,EAAE,IAAI,CAACK,CAAC,CAAC,MAAM,CAAC,CAACK,KAAK;MAC1BT,SAAS,EAAE,IAAI,CAACI,CAAC,CAAC,WAAW,CAAC,CAACK,KAAK;MACpCR,QAAQ,EAAE,IAAI,CAACG,CAAC,CAAC,UAAU,CAAC,CAACK;KAC9B;IAED,IAAI,CAACtB,WAAW,CAACuB,QAAQ,CAACF,eAAe,CAAC,CACvCG,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UACrDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAAC3B,MAAM,CAACe,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7B,CAAC;MACDa,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC3B,QAAQ,CAACwB,IAAI,CAACG,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;UACzEH,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACzB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;;;uBAhEWN,iBAAiB,EAAAL,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3C,EAAA,CAAAuC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA7C,EAAA,CAAAuC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjB1C,iBAAiB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9BtD,EAAA,CAAAC,cAAA,aAAgC;UAGJD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAEtDH,EAAA,CAAAC,cAAA,uBAAkB;UACmBD,EAAA,CAAAwD,UAAA,sBAAAC,oDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAEpD3B,EAAA,CAAAC,cAAA,wBAAwD;UACzCD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAI,SAAA,gBAAgF;UAChFJ,EAAA,CAAA0D,UAAA,KAAAC,uCAAA,uBAAgF;UAChF3D,EAAA,CAAA0D,UAAA,KAAAE,uCAAA,uBAA8F;UAClG5D,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAI,SAAA,gBAC+C;UAC/CJ,EAAA,CAAAC,cAAA,iBAAuF;UAArDD,EAAA,CAAAwD,UAAA,mBAAAK,oDAAA;YAAA,OAAAN,GAAA,CAAA3C,YAAA,IAAA2C,GAAA,CAAA3C,YAAA;UAAA,EAAsC;UACpEZ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE3EH,EAAA,CAAA0D,UAAA,KAAAI,uCAAA,uBAAsF;UACtF9D,EAAA,CAAA0D,UAAA,KAAAK,uCAAA,uBAC0B;UAC9B/D,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAI,SAAA,gBAAyF;UACzFJ,EAAA,CAAA0D,UAAA,KAAAM,uCAAA,uBAAyF;UAC7FhE,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAI,SAAA,gBAAuF;UACvFJ,EAAA,CAAA0D,UAAA,KAAAO,uCAAA,uBAAuF;UAC3FjE,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAC,cAAA,kBAAyD;UAC1BD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9CH,EAAA,CAAA0D,UAAA,KAAAQ,uCAAA,uBAAsF;UAC1FlE,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,eAA8B;UAGtBD,EAAA,CAAA0D,UAAA,KAAAS,yCAAA,0BAAyD;UACzDnE,EAAA,CAAA0D,UAAA,KAAAU,kCAAA,kBAAsC;UAC1CpE,EAAA,CAAAG,YAAA,EAAS;UAIrBH,EAAA,CAAAC,cAAA,wBAAkB;UACXD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAxDtDH,EAAA,CAAAqE,SAAA,GAA0B;UAA1BrE,EAAA,CAAAsE,UAAA,cAAAf,GAAA,CAAAzC,YAAA,CAA0B;UAKZd,EAAA,CAAAqE,SAAA,GAAqC;UAArCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,UAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,UAAA8C,MAAA,aAAqC;UACrCvE,EAAA,CAAAqE,SAAA,GAAkC;UAAlCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,UAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,UAAA8C,MAAA,UAAkC;UAM9BvE,EAAA,CAAAqE,SAAA,GAA2C;UAA3CrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA3C,YAAA,uBAA2C;UAG7CZ,EAAA,CAAAqE,SAAA,GAAkD;UAAlDrE,EAAA,CAAAwE,iBAAA,CAAAjB,GAAA,CAAA3C,YAAA,mCAAkD;UAEpDZ,EAAA,CAAAqE,SAAA,GAAwC;UAAxCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,aAAwC;UACxCvE,EAAA,CAAAqE,SAAA,GAAyC;UAAzCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,cAAyC;UAQzCvE,EAAA,CAAAqE,SAAA,GAAyC;UAAzCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,cAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,cAAA8C,MAAA,aAAyC;UAOzCvE,EAAA,CAAAqE,SAAA,GAAwC;UAAxCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,aAAA8C,MAAA,aAAwC;UAUxCvE,EAAA,CAAAqE,SAAA,GAAoC;UAApCrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA9B,CAAA,SAAA8C,MAAA,kBAAAhB,GAAA,CAAA9B,CAAA,SAAA8C,MAAA,aAAoC;UAK5CvE,EAAA,CAAAqE,SAAA,GAA4C;UAA5CrE,EAAA,CAAAsE,UAAA,aAAAf,GAAA,CAAAzC,YAAA,CAAAc,OAAA,IAAA2B,GAAA,CAAA5C,OAAA,CAA4C;UAChBX,EAAA,CAAAqE,SAAA,GAAa;UAAbrE,EAAA,CAAAsE,UAAA,SAAAf,GAAA,CAAA5C,OAAA,CAAa;UAClCX,EAAA,CAAAqE,SAAA,GAAc;UAAdrE,EAAA,CAAAsE,UAAA,UAAAf,GAAA,CAAA5C,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}