{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class JobService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  /**\n   * Get all jobs with filtering\n   * @param params Query parameters\n   * @returns Observable of jobs response\n   */\n  getJobs(params) {\n    return this.apiService.get('jobs', params, false);\n  }\n  /**\n   * Get job by ID\n   * @param id Job ID\n   * @returns Observable of job response\n   */\n  getJobById(id) {\n    return this.apiService.get(`jobs/${id}`, null, false);\n  }\n  /**\n   * Create a new job\n   * @param jobRequest Job request\n   * @returns Observable of job response\n   */\n  createJob(jobRequest) {\n    return this.apiService.post('jobs', jobRequest);\n  }\n  /**\n   * Update job\n   * @param id Job ID\n   * @param jobRequest Job request\n   * @returns Observable of job response\n   */\n  updateJob(id, jobRequest) {\n    return this.apiService.put(`jobs/${id}`, jobRequest);\n  }\n  /**\n   * Delete job\n   * @param id Job ID\n   * @returns Observable of response\n   */\n  deleteJob(id) {\n    return this.apiService.delete(`jobs/${id}`);\n  }\n  /**\n   * Get jobs posted by the current employer\n   * @param params Query parameters\n   * @returns Observable of jobs response\n   */\n  getEmployerJobs(params) {\n    return this.apiService.get('jobs/employer/me', params);\n  }\n  static {\n    this.ɵfac = function JobService_Factory(t) {\n      return new (t || JobService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: JobService,\n      factory: JobService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["JobService", "constructor", "apiService", "getJobs", "params", "get", "getJobById", "id", "createJob", "jobRequest", "post", "updateJob", "put", "deleteJob", "delete", "getEmployerJobs", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\services\\job.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ApiService } from './api.service';\r\nimport { Job, JobResponse, JobsResponse, JobRequest } from '../models/job.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JobService {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  /**\r\n   * Get all jobs with filtering\r\n   * @param params Query parameters\r\n   * @returns Observable of jobs response\r\n   */\r\n  getJobs(params?: any): Observable<JobsResponse> {\r\n    return this.apiService.get<JobsResponse>('jobs', params, false);\r\n  }\r\n\r\n  /**\r\n   * Get job by ID\r\n   * @param id Job ID\r\n   * @returns Observable of job response\r\n   */\r\n  getJobById(id: number): Observable<JobResponse> {\r\n    return this.apiService.get<JobResponse>(`jobs/${id}`, null, false);\r\n  }\r\n\r\n  /**\r\n   * Create a new job\r\n   * @param jobRequest Job request\r\n   * @returns Observable of job response\r\n   */\r\n  createJob(jobRequest: JobRequest): Observable<JobResponse> {\r\n    return this.apiService.post<JobResponse>('jobs', jobRequest);\r\n  }\r\n\r\n  /**\r\n   * Update job\r\n   * @param id Job ID\r\n   * @param jobRequest Job request\r\n   * @returns Observable of job response\r\n   */\r\n  updateJob(id: number, jobRequest: JobRequest): Observable<JobResponse> {\r\n    return this.apiService.put<JobResponse>(`jobs/${id}`, jobRequest);\r\n  }\r\n\r\n  /**\r\n   * Delete job\r\n   * @param id Job ID\r\n   * @returns Observable of response\r\n   */\r\n  deleteJob(id: number): Observable<any> {\r\n    return this.apiService.delete<any>(`jobs/${id}`);\r\n  }\r\n\r\n  /**\r\n   * Get jobs posted by the current employer\r\n   * @param params Query parameters\r\n   * @returns Observable of jobs response\r\n   */\r\n  getEmployerJobs(params?: any): Observable<JobsResponse> {\r\n    return this.apiService.get<JobsResponse>('jobs/employer/me', params);\r\n  }\r\n}\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,UAAU;EAErBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9C;;;;;EAKAC,OAAOA,CAACC,MAAY;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,GAAG,CAAe,MAAM,EAAED,MAAM,EAAE,KAAK,CAAC;EACjE;EAEA;;;;;EAKAE,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAACL,UAAU,CAACG,GAAG,CAAc,QAAQE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;EACpE;EAEA;;;;;EAKAC,SAASA,CAACC,UAAsB;IAC9B,OAAO,IAAI,CAACP,UAAU,CAACQ,IAAI,CAAc,MAAM,EAAED,UAAU,CAAC;EAC9D;EAEA;;;;;;EAMAE,SAASA,CAACJ,EAAU,EAAEE,UAAsB;IAC1C,OAAO,IAAI,CAACP,UAAU,CAACU,GAAG,CAAc,QAAQL,EAAE,EAAE,EAAEE,UAAU,CAAC;EACnE;EAEA;;;;;EAKAI,SAASA,CAACN,EAAU;IAClB,OAAO,IAAI,CAACL,UAAU,CAACY,MAAM,CAAM,QAAQP,EAAE,EAAE,CAAC;EAClD;EAEA;;;;;EAKAQ,eAAeA,CAACX,MAAY;IAC1B,OAAO,IAAI,CAACF,UAAU,CAACG,GAAG,CAAe,kBAAkB,EAAED,MAAM,CAAC;EACtE;;;uBAzDWJ,UAAU,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVnB,UAAU;MAAAoB,OAAA,EAAVpB,UAAU,CAAAqB,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}