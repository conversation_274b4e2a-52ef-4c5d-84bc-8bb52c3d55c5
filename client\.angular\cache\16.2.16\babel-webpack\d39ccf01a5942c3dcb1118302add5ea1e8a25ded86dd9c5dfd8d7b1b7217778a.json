{"ast": null, "code": "import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n  if (isFunction(selector)) {\n    return connect(selector, {\n      connector: subjectFactory\n    });\n  }\n  return source => new ConnectableObservable(source, subjectFactory);\n}", "map": {"version": 3, "names": ["ConnectableObservable", "isFunction", "connect", "multicast", "subjectOrSubjectFactory", "selector", "subjectFactory", "connector", "source"], "sources": ["D:/findit/client/node_modules/rxjs/dist/esm/internal/operators/multicast.js"], "sourcesContent": ["import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n    const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n    if (isFunction(selector)) {\n        return connect(selector, {\n            connector: subjectFactory,\n        });\n    }\n    return (source) => new ConnectableObservable(source, subjectFactory);\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,SAASA,CAACC,uBAAuB,EAAEC,QAAQ,EAAE;EACzD,MAAMC,cAAc,GAAGL,UAAU,CAACG,uBAAuB,CAAC,GAAGA,uBAAuB,GAAG,MAAMA,uBAAuB;EACpH,IAAIH,UAAU,CAACI,QAAQ,CAAC,EAAE;IACtB,OAAOH,OAAO,CAACG,QAAQ,EAAE;MACrBE,SAAS,EAAED;IACf,CAAC,CAAC;EACN;EACA,OAAQE,MAAM,IAAK,IAAIR,qBAAqB,CAACQ,MAAM,EAAEF,cAAc,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}