{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// Angular Material Imports\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule, MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { JobListComponent } from './components/job-list/job-list.component';\nimport { JobDetailComponent } from './components/job-detail/job-detail.component';\nimport { JobFormComponent } from './components/job-form/job-form.component';\nimport { JobCreateComponent } from './components/job-create/job-create.component';\nimport { ApplicationListComponent } from './components/application-list/application-list.component';\nimport { ProfileComponent } from './components/profile/profile.component';\nimport { HeaderComponent } from './components/header/header.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,\n        useValue: {\n          appearance: 'outline'\n        }\n      }],\n      imports: [BrowserModule, BrowserAnimationsModule, AppRoutingModule, HttpClientModule, FormsModule, ReactiveFormsModule, MatToolbarModule, MatButtonModule, MatCardModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatTableModule, MatPaginatorModule, MatSortModule, MatIconModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatDialogModule, MatMenuModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, RegisterComponent, JobListComponent, JobDetailComponent, JobFormComponent, JobCreateComponent, ApplicationListComponent, ProfileComponent, HeaderComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, AppRoutingModule, HttpClientModule, FormsModule, ReactiveFormsModule, MatToolbarModule, MatButtonModule, MatCardModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatTableModule, MatPaginatorModule, MatSortModule, MatIconModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatDialogModule, MatMenuModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "HttpClientModule", "FormsModule", "ReactiveFormsModule", "BrowserAnimationsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "MatSelectModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatIconModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatChipsModule", "MatDialogModule", "MatMenuModule", "AppRoutingModule", "AppComponent", "LoginComponent", "RegisterComponent", "JobListComponent", "JobDetailComponent", "JobFormComponent", "JobCreateComponent", "ApplicationListComponent", "ProfileComponent", "HeaderComponent", "AppModule", "bootstrap", "provide", "useValue", "appearance", "imports", "declarations"], "sources": ["D:\\findit\\client\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\n\r\n// Angular Material Imports\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule, MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { LoginComponent } from './components/login/login.component';\r\nimport { RegisterComponent } from './components/register/register.component';\r\nimport { JobListComponent } from './components/job-list/job-list.component';\r\nimport { JobDetailComponent } from './components/job-detail/job-detail.component';\r\nimport { JobFormComponent } from './components/job-form/job-form.component';\r\nimport { JobCreateComponent } from './components/job-create/job-create.component';\r\nimport { ApplicationListComponent } from './components/application-list/application-list.component';\r\nimport { ProfileComponent } from './components/profile/profile.component';\r\nimport { HeaderComponent } from './components/header/header.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    LoginComponent,\r\n    RegisterComponent,\r\n    JobListComponent,\r\n    JobDetailComponent,\r\n    JobFormComponent,\r\n    JobCreateComponent,\r\n    ApplicationListComponent,\r\n    ProfileComponent,\r\n    HeaderComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    BrowserAnimationsModule, // This should come before Angular Material modules\r\n    AppRoutingModule,\r\n    HttpClientModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatToolbarModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatInputModule,\r\n    MatFormFieldModule,\r\n    MatSelectModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSortModule,\r\n    MatIconModule,\r\n    MatSnackBarModule,\r\n    MatProgressSpinnerModule,\r\n    MatChipsModule,\r\n    MatDialogModule,\r\n    MatMenuModule\r\n  ],\r\n  providers: [\r\n    { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: { appearance: 'outline' } }\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,EAAEC,8BAA8B,QAAQ,8BAA8B;AACjG,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;;AA2CtE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRX,YAAY;IAAA;EAAA;;;iBAHb,CACT;QAAEY,OAAO,EAAExB,8BAA8B;QAAEyB,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAS;MAAE,CAAE,CACjF;MAAAC,OAAA,GAxBCrC,aAAa,EACbI,uBAAuB,EACvBiB,gBAAgB,EAChBpB,gBAAgB,EAChBC,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBE,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc,EACdC,eAAe,EACfC,aAAa;IAAA;EAAA;;;2EAOJY,SAAS;IAAAM,YAAA,GAvClBhB,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,wBAAwB,EACxBC,gBAAgB,EAChBC,eAAe;IAAAM,OAAA,GAGfrC,aAAa,EACbI,uBAAuB,EACvBiB,gBAAgB,EAChBpB,gBAAgB,EAChBC,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBE,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc,EACdC,eAAe,EACfC,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}