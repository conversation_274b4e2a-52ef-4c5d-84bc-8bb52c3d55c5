{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ProfileService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  /**\n   * Get user profile\n   * @returns Observable of profile response\n   */\n  getProfile() {\n    return this.apiService.get('profile');\n  }\n  /**\n   * Update user profile\n   * @param profileData Profile update request\n   * @returns Observable of profile response\n   */\n  updateProfile(profileData) {\n    return this.apiService.put('profile', profileData);\n  }\n  static {\n    this.ɵfac = function ProfileService_Factory(t) {\n      return new (t || ProfileService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileService,\n      factory: ProfileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ProfileService", "constructor", "apiService", "getProfile", "get", "updateProfile", "profileData", "put", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\services\\profile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ApiService } from './api.service';\r\nimport { Profile } from '../models/user.model';\r\n\r\nexport interface ProfileResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data: {\r\n    profile: Profile;\r\n  };\r\n}\r\n\r\nexport interface ProfileUpdateRequest {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  resumeUrl?: string;\r\n  companyName?: string;\r\n  companyDescription?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProfileService {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  /**\r\n   * Get user profile\r\n   * @returns Observable of profile response\r\n   */\r\n  getProfile(): Observable<ProfileResponse> {\r\n    return this.apiService.get<ProfileResponse>('profile');\r\n  }\r\n\r\n  /**\r\n   * Update user profile\r\n   * @param profileData Profile update request\r\n   * @returns Observable of profile response\r\n   */\r\n  updateProfile(profileData: ProfileUpdateRequest): Observable<ProfileResponse> {\r\n    return this.apiService.put<ProfileResponse>('profile', profileData);\r\n  }\r\n}\r\n"], "mappings": ";;AA0BA,OAAM,MAAOA,cAAc;EAEzBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9C;;;;EAIAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACD,UAAU,CAACE,GAAG,CAAkB,SAAS,CAAC;EACxD;EAEA;;;;;EAKAC,aAAaA,CAACC,WAAiC;IAC7C,OAAO,IAAI,CAACJ,UAAU,CAACK,GAAG,CAAkB,SAAS,EAAED,WAAW,CAAC;EACrE;;;uBAnBWN,cAAc,EAAAQ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdX,cAAc;MAAAY,OAAA,EAAdZ,cAAc,CAAAa,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}