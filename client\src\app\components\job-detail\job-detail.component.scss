.job-detail-container {
  margin-bottom: 30px;
}

.back-button {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.job-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.job-card, .company-card {
  margin-bottom: 20px;
}

.job-subtitle {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;

  span {
    display: flex;
    align-items: center;

    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }
  }
}

.job-status {
  display: flex;
  align-items: center;
  margin: 16px 0;

  .job-date {
    margin-left: 16px;
    font-size: 14px;
    color: #666;
  }
}

.job-section {
  margin: 24px 0;

  h3 {
    margin-bottom: 12px;
    font-weight: 500;
    color: #333;
  }

  p {
    line-height: 1.6;
    white-space: pre-line;
  }
}

.application-status {
  margin-top: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;

  h3 {
    margin-bottom: 12px;
    font-weight: 500;
  }

  .status-chip {
    display: flex;
    align-items: center;

    span {
      margin-left: 16px;
      font-size: 14px;
      color: #666;
    }
  }
}

mat-card-actions {
  padding: 16px;

  button {
    margin-right: 8px;
  }
}

.action-button {
  margin-left: 8px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #f44336;
  }

  p {
    font-size: 18px;
    margin-bottom: 24px;
  }
}

// Application status colors
.status-applied {
  background-color: #2196f3 !important;
  color: white !important;
}

.status-reviewed {
  background-color: #ff9800 !important;
  color: white !important;
}

.status-interviewed {
  background-color: #9c27b0 !important;
  color: white !important;
}

.status-rejected {
  background-color: #f44336 !important;
  color: white !important;
}

.status-hired {
  background-color: #4caf50 !important;
  color: white !important;
}

@media (max-width: 768px) {
  .job-content {
    grid-template-columns: 1fr;
  }
}