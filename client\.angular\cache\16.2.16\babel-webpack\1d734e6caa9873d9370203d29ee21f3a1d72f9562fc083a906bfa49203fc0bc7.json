{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./components/header/header.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'client';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"container\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-header\");\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.HeaderComponent],\n      styles: [\".container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  min-height: calc(100vh - 64px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLDhCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuY29udGFpbmVyIHtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2NHB4KTsgLy8gQWRqdXN0IGJhc2VkIG9uIGhlYWRlciBoZWlnaHRcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd"], "sources": ["D:\\findit\\client\\src\\app\\app.component.ts", "D:\\findit\\client\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss']\r\n})\r\nexport class AppComponent {\r\n  title = 'client';\r\n}\r\n", "<app-header></app-header>\r\n<div class=\"container\">\r\n    <router-outlet></router-outlet>\r\n</div>"], "mappings": ";;;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,SAAA,iBAAyB;UACzBD,EAAA,CAAAE,cAAA,aAAuB;UACnBF,EAAA,CAAAC,SAAA,oBAA+B;UACnCD,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}