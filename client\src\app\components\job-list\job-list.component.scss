.job-list-container {
  margin-bottom: 30px;
}

.job-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-weight: 500;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  mat-form-field {
    flex: 1;
    min-width: 200px;
  }

  button {
    height: 56px;
    margin-top: -8px;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  color: #666;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 18px;
  }
}

.job-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.job-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  mat-card-content {
    flex-grow: 1;
  }
}

.job-subtitle {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;

  span {
    display: flex;
    align-items: center;

    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }
  }
}

.job-description {
  margin: 16px 0;
  color: #555;
  line-height: 1.5;
}

.job-details {
  margin-top: 16px;
}

mat-card-actions {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

.spacer {
  flex: 1;
}

.job-date {
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .job-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;

    mat-form-field {
      width: 100%;
    }

    button {
      width: 100%;
    }
  }

  .job-cards {
    grid-template-columns: 1fr;
  }
}