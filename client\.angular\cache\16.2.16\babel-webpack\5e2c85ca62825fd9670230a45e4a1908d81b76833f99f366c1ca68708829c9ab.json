{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, EventEmitter, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i6 from '@angular/material/core';\nimport { mixinDisabled, mixinInitialized } from '@angular/material/core';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_div_2_mat_form_field_3_mat_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r6, \" \");\n  }\n}\nfunction MatPaginator_div_2_mat_form_field_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 16)(1, \"mat-select\", 17);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_div_2_mat_form_field_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7._changePageSize($event.value));\n    });\n    i0.ɵɵtemplate(2, MatPaginator_div_2_mat_form_field_3_mat_option_2_Template, 2, 2, \"mat-option\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r3._formFieldAppearance)(\"color\", ctx_r3.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r3.pageSize)(\"disabled\", ctx_r3.disabled)(\"aria-labelledby\", ctx_r3._pageSizeLabelId)(\"panelClass\", ctx_r3.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r3.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.pageSize);\n  }\n}\nfunction MatPaginator_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_div_2_mat_form_field_3_Template, 3, 8, \"mat-form-field\", 14);\n    i0.ɵɵtemplate(4, MatPaginator_div_2_div_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r0._pageSizeLabelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length <= 1);\n  }\n}\nfunction MatPaginator_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r2._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r2._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r2._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2._intl.lastPageLabel);\n  }\n}\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    this.changes = new Subject();\n    /** A label for the page size selector. */\n    this.itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    this.nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    this.previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    this.firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    this.lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    this.getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatorIntl_Factory(t) {\n      return new (t || MatPaginatorIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = mixinDisabled(mixinInitialized(class {}));\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\nclass _MatPaginatorBase extends _MatPaginatorMixinBase {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = coerceNumberProperty(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(coerceNumberProperty(value), 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** Whether to hide the page size selection UI from the user. */\n  get hidePageSize() {\n    return this._hidePageSize;\n  }\n  set hidePageSize(value) {\n    this._hidePageSize = coerceBooleanProperty(value);\n  }\n  /** Whether to show the first/last buttons UI to the user. */\n  get showFirstLastButtons() {\n    return this._showFirstLastButtons;\n  }\n  set showFirstLastButtons(value) {\n    this._showFirstLastButtons = coerceBooleanProperty(value);\n  }\n  constructor(_intl, _changeDetectorRef, defaults) {\n    super();\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._pageIndex = 0;\n    this._length = 0;\n    this._pageSizeOptions = [];\n    this._hidePageSize = false;\n    this._showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    this.selectConfig = {};\n    /** Event emitted when the paginator changes the page size or page index. */\n    this.page = new EventEmitter();\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this._hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this._showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n  }\n  ngOnInit() {\n    this._initialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._markInitialized();\n  }\n  ngOnDestroy() {\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._initialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n  static {\n    this.ɵfac = function _MatPaginatorBase_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatPaginatorBase,\n      inputs: {\n        color: \"color\",\n        pageIndex: \"pageIndex\",\n        length: \"length\",\n        pageSize: \"pageSize\",\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: \"hidePageSize\",\n        showFirstLastButtons: \"showFirstLastButtons\",\n        selectConfig: \"selectConfig\"\n      },\n      outputs: {\n        page: \"page\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatPaginatorBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: MatPaginatorIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }];\n  }, {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    length: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input\n    }],\n    showFirstLastButtons: [{\n      type: Input\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator extends _MatPaginatorBase {\n  constructor(intl, changeDetectorRef, defaults) {\n    super(intl, changeDetectorRef, defaults);\n    /** ID for the DOM node containing the paginator's items per page label. */\n    this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n  static {\n    this.ɵfac = function MatPaginator_Factory(t) {\n      return new (t || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        disabled: \"disabled\"\n      },\n      exportAs: [\"matPaginator\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [\"class\", \"mat-mdc-paginator-page-size\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-page-size-label\", 3, \"id\"], [\"class\", \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\", 4, \"ngIf\"], [\"class\", \"mat-mdc-paginator-page-size-value\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatPaginator_div_2_Template, 5, 4, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatPaginator_button_6_Template, 3, 5, \"button\", 5);\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 7);\n          i0.ɵɵelement(12, \"path\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, MatPaginator_button_13_Template, 3, 5, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hidePageSize);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.MatIconButton, i4.MatFormField, i5.MatSelect, i6.MatOption, i7.MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      inputs: ['disabled'],\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatPaginatorIntl\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nclass MatPaginatorModule {\n  static {\n    this.ɵfac = function MatPaginatorModule_Factory(t) {\n      return new (t || MatPaginatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule],\n      exports: [MatPaginator],\n      declarations: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent, _MatPaginatorBase };", "map": {"version": 3, "names": ["i2", "CommonModule", "i0", "Injectable", "Optional", "SkipSelf", "InjectionToken", "EventEmitter", "Directive", "Input", "Output", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "NgModule", "Subject", "i3", "MatButtonModule", "i5", "MatSelectModule", "i7", "MatTooltipModule", "i6", "mixinDisabled", "mixinInitialized", "coerceNumberProperty", "coerceBooleanProperty", "i4", "MatPaginator_div_2_mat_form_field_3_mat_option_2_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "pageSizeOption_r6", "$implicit", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "MatPaginator_div_2_mat_form_field_3_Template", "_r8", "ɵɵgetCurrentView", "ɵɵlistener", "MatPaginator_div_2_mat_form_field_3_Template_mat_select_selectionChange_1_listener", "$event", "ɵɵrestoreView", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "_changePageSize", "value", "ɵɵtemplate", "ctx_r3", "_formFieldAppearance", "color", "pageSize", "disabled", "_pageSizeLabelId", "selectConfig", "panelClass", "disableOptionCentering", "_displayedPageSizeOptions", "MatPaginator_div_2_div_4_Template", "ctx_r4", "ɵɵtextInterpolate", "MatPaginator_div_2_Template", "ctx_r0", "ɵɵpropertyInterpolate", "_intl", "itemsPerPageLabel", "length", "MatPaginator_button_6_Template", "_r10", "MatPaginator_button_6_Template_button_click_0_listener", "ctx_r9", "firstPage", "ɵɵnamespaceSVG", "ɵɵelement", "ctx_r1", "firstPageLabel", "_previousButtonsDisabled", "ɵɵattribute", "MatPaginator_button_13_Template", "_r12", "ɵɵnamespaceHTML", "MatPaginator_button_13_Template_button_click_0_listener", "ctx_r11", "lastPage", "ctx_r2", "lastPageLabel", "_nextButtonsDisabled", "MatPaginatorIntl", "constructor", "changes", "nextPageLabel", "previousPageLabel", "getRangeLabel", "page", "Math", "max", "startIndex", "endIndex", "min", "ɵfac", "MatPaginatorIntl_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "MAT_PAGINATOR_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_PAGINATOR_INTL_PROVIDER", "provide", "deps", "useFactory", "DEFAULT_PAGE_SIZE", "PageEvent", "MAT_PAGINATOR_DEFAULT_OPTIONS", "_MatPaginatorMixinBase", "_MatPaginatorBase", "pageIndex", "_pageIndex", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_length", "_pageSize", "_updateDisplayedPageSizeOptions", "pageSizeOptions", "_pageSizeOptions", "map", "p", "hidePageSize", "_hidePageSize", "showFirstLastButtons", "_showFirstLastButtons", "defaults", "_intlChanges", "subscribe", "ngOnInit", "_initialized", "_markInitialized", "ngOnDestroy", "unsubscribe", "nextPage", "hasNextPage", "previousPageIndex", "_emitPageEvent", "previousPage", "hasPreviousPage", "getNumberOfPages", "maxPageIndex", "ceil", "floor", "slice", "indexOf", "push", "sort", "a", "b", "emit", "_MatPaginatorBase_Factory", "ɵɵinvalidFactory", "ɵdir", "ɵɵdefineDirective", "inputs", "outputs", "features", "ɵɵInheritDefinitionFeature", "ChangeDetectorRef", "undefined", "nextUniqueId", "MatPaginator", "intl", "changeDetectorRef", "formFieldAppearance", "MatPaginator_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "exportAs", "decls", "vars", "consts", "template", "MatPaginator_Template", "MatPaginator_Template_button_click_7_listener", "MatPaginator_Template_button_click_10_listener", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "MatIconButton", "MatFormField", "MatSelect", "MatOption", "MatTooltip", "styles", "encapsulation", "changeDetection", "selector", "host", "OnPush", "None", "decorators", "MatPaginatorModule", "MatPaginatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports", "declarations"], "sources": ["D:/findit/client/node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, EventEmitter, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i6 from '@angular/material/core';\nimport { mixinDisabled, mixinInitialized } from '@angular/material/core';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n    constructor() {\n        /**\n         * Stream to emit from when labels are changed. Use this to notify components when the labels have\n         * changed after initialization.\n         */\n        this.changes = new Subject();\n        /** A label for the page size selector. */\n        this.itemsPerPageLabel = 'Items per page:';\n        /** A label for the button that increments the current page. */\n        this.nextPageLabel = 'Next page';\n        /** A label for the button that decrements the current page. */\n        this.previousPageLabel = 'Previous page';\n        /** A label for the button that moves to the first page. */\n        this.firstPageLabel = 'First page';\n        /** A label for the button that moves to the last page. */\n        this.lastPageLabel = 'Last page';\n        /** A label for the range of items within the current page and the length of the whole list. */\n        this.getRangeLabel = (page, pageSize, length) => {\n            if (length == 0 || pageSize == 0) {\n                return `0 of ${length}`;\n            }\n            length = Math.max(length, 0);\n            const startIndex = page * pageSize;\n            // If the start index exceeds the list length, do not try and fix the end index to the end.\n            const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n            return `${startIndex + 1} – ${endIndex} of ${length}`;\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorIntl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n    // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n    provide: MatPaginatorIntl,\n    deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n    useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = mixinDisabled(mixinInitialized(class {\n}));\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\nclass _MatPaginatorBase extends _MatPaginatorMixinBase {\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n        return this._pageIndex;\n    }\n    set pageIndex(value) {\n        this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n        return this._length;\n    }\n    set length(value) {\n        this._length = coerceNumberProperty(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n        return this._pageSize;\n    }\n    set pageSize(value) {\n        this._pageSize = Math.max(coerceNumberProperty(value), 0);\n        this._updateDisplayedPageSizeOptions();\n    }\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n        return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n        this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n        this._updateDisplayedPageSizeOptions();\n    }\n    /** Whether to hide the page size selection UI from the user. */\n    get hidePageSize() {\n        return this._hidePageSize;\n    }\n    set hidePageSize(value) {\n        this._hidePageSize = coerceBooleanProperty(value);\n    }\n    /** Whether to show the first/last buttons UI to the user. */\n    get showFirstLastButtons() {\n        return this._showFirstLastButtons;\n    }\n    set showFirstLastButtons(value) {\n        this._showFirstLastButtons = coerceBooleanProperty(value);\n    }\n    constructor(_intl, _changeDetectorRef, defaults) {\n        super();\n        this._intl = _intl;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._pageIndex = 0;\n        this._length = 0;\n        this._pageSizeOptions = [];\n        this._hidePageSize = false;\n        this._showFirstLastButtons = false;\n        /** Used to configure the underlying `MatSelect` inside the paginator. */\n        this.selectConfig = {};\n        /** Event emitted when the paginator changes the page size or page index. */\n        this.page = new EventEmitter();\n        this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n        if (defaults) {\n            const { pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons } = defaults;\n            if (pageSize != null) {\n                this._pageSize = pageSize;\n            }\n            if (pageSizeOptions != null) {\n                this._pageSizeOptions = pageSizeOptions;\n            }\n            if (hidePageSize != null) {\n                this._hidePageSize = hidePageSize;\n            }\n            if (showFirstLastButtons != null) {\n                this._showFirstLastButtons = showFirstLastButtons;\n            }\n        }\n    }\n    ngOnInit() {\n        this._initialized = true;\n        this._updateDisplayedPageSizeOptions();\n        this._markInitialized();\n    }\n    ngOnDestroy() {\n        this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex + 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.pageIndex - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n        // hasPreviousPage being false implies at the start\n        if (!this.hasPreviousPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = 0;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n        // hasNextPage being false implies at the end\n        if (!this.hasNextPage()) {\n            return;\n        }\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = this.getNumberOfPages() - 1;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n        return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n        const maxPageIndex = this.getNumberOfPages() - 1;\n        return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n        if (!this.pageSize) {\n            return 0;\n        }\n        return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n        // Current page needs to be updated to reflect the new page size. Navigate to the page\n        // containing the previous page's first item.\n        const startIndex = this.pageIndex * this.pageSize;\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n        this.pageSize = pageSize;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n        return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n        return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n        if (!this._initialized) {\n            return;\n        }\n        // If no page size is provided, use the first page size option or the default page size.\n        if (!this.pageSize) {\n            this._pageSize =\n                this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n        }\n        this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n        if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n            this._displayedPageSizeOptions.push(this.pageSize);\n        }\n        // Sort the numbers using a number-specific sort function.\n        this._displayedPageSizeOptions.sort((a, b) => a - b);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n        this.page.emit({\n            previousPageIndex,\n            pageIndex: this.pageIndex,\n            pageSize: this.pageSize,\n            length: this.length,\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatPaginatorBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatPaginatorBase, inputs: { color: \"color\", pageIndex: \"pageIndex\", length: \"length\", pageSize: \"pageSize\", pageSizeOptions: \"pageSizeOptions\", hidePageSize: \"hidePageSize\", showFirstLastButtons: \"showFirstLastButtons\", selectConfig: \"selectConfig\" }, outputs: { page: \"page\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatPaginatorBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: MatPaginatorIntl }, { type: i0.ChangeDetectorRef }, { type: undefined }]; }, propDecorators: { color: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input\n            }], length: [{\n                type: Input\n            }], pageSize: [{\n                type: Input\n            }], pageSizeOptions: [{\n                type: Input\n            }], hidePageSize: [{\n                type: Input\n            }], showFirstLastButtons: [{\n                type: Input\n            }], selectConfig: [{\n                type: Input\n            }], page: [{\n                type: Output\n            }] } });\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator extends _MatPaginatorBase {\n    constructor(intl, changeDetectorRef, defaults) {\n        super(intl, changeDetectorRef, defaults);\n        /** ID for the DOM node containing the paginator's items per page label. */\n        this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n        this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginator, deps: [{ token: MatPaginatorIntl }, { token: i0.ChangeDetectorRef }, { token: MAT_PAGINATOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatPaginator, selector: \"mat-paginator\", inputs: { disabled: \"disabled\" }, host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-paginator\" }, exportAs: [\"matPaginator\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i3.MatIconButton, selector: \"button[mat-icon-button]\", inputs: [\"disabled\", \"disableRipple\", \"color\"], exportAs: [\"matButton\"] }, { kind: \"component\", type: i4.MatFormField, selector: \"mat-form-field\", inputs: [\"hideRequiredMarker\", \"color\", \"floatLabel\", \"appearance\", \"subscriptSizing\", \"hintLabel\"], exportAs: [\"matFormField\"] }, { kind: \"component\", type: i5.MatSelect, selector: \"mat-select\", inputs: [\"disabled\", \"disableRipple\", \"tabIndex\", \"panelWidth\", \"hideSingleSelectionIndicator\"], exportAs: [\"matSelect\"] }, { kind: \"component\", type: i6.MatOption, selector: \"mat-option\", exportAs: [\"matOption\"] }, { kind: \"directive\", type: i7.MatTooltip, selector: \"[matTooltip]\", exportAs: [\"matTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginator, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-paginator', exportAs: 'matPaginator', inputs: ['disabled'], host: {\n                        'class': 'mat-mdc-paginator',\n                        'role': 'group',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    <div class=\\\"mat-mdc-paginator-page-size\\\" *ngIf=\\\"!hidePageSize\\\">\\n      <div class=\\\"mat-mdc-paginator-page-size-label\\\" id=\\\"{{_pageSizeLabelId}}\\\">\\n        {{_intl.itemsPerPageLabel}}\\n      </div>\\n\\n      <mat-form-field\\n        *ngIf=\\\"_displayedPageSizeOptions.length > 1\\\"\\n        [appearance]=\\\"_formFieldAppearance!\\\"\\n        [color]=\\\"color\\\"\\n        class=\\\"mat-mdc-paginator-page-size-select\\\">\\n        <mat-select\\n          [value]=\\\"pageSize\\\"\\n          [disabled]=\\\"disabled\\\"\\n          [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n          [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n          [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n          (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n          hideSingleSelectionIndicator>\\n          <mat-option *ngFor=\\\"let pageSizeOption of _displayedPageSizeOptions\\\" [value]=\\\"pageSizeOption\\\">\\n            {{pageSizeOption}}\\n          </mat-option>\\n        </mat-select>\\n      </mat-form-field>\\n\\n      <div\\n        class=\\\"mat-mdc-paginator-page-size-value\\\"\\n        *ngIf=\\\"_displayedPageSizeOptions.length <= 1\\\">{{pageSize}}</div>\\n    </div>\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-first\\\"\\n              (click)=\\\"firstPage()\\\"\\n              [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-last\\\"\\n              (click)=\\\"lastPage()\\\"\\n              [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              *ngIf=\\\"showFirstLastButtons\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n        </svg>\\n      </button>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"] }]\n        }], ctorParameters: function () { return [{ type: MatPaginatorIntl }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n                }] }]; } });\n\nclass MatPaginatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorModule, declarations: [MatPaginator], imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule], exports: [MatPaginator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorModule, providers: [MAT_PAGINATOR_INTL_PROVIDER], imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule],\n                    exports: [MatPaginator],\n                    declarations: [MatPaginator],\n                    providers: [MAT_PAGINATOR_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent, _MatPaginatorBase };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC/L,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,wBAAwB;AACxE,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,EAAE,MAAM,8BAA8B;;AAElD;AACA;AACA;AACA;AAHA,SAAAC,0DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAiCoG5B,EAAE,CAAA8B,cAAA,oBA6Q4sC,CAAC;IA7Q/sC9B,EAAE,CAAA+B,MAAA,EA6QwvC,CAAC;IA7Q3vC/B,EAAE,CAAAgC,YAAA,CA6QqwC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,iBAAA,GAAAJ,GAAA,CAAAK,SAAA;IA7QxwClC,EAAE,CAAAmC,UAAA,UAAAF,iBA6Q2sC,CAAC;IA7Q9sCjC,EAAE,CAAAoC,SAAA,EA6QwvC,CAAC;IA7Q3vCpC,EAAE,CAAAqC,kBAAA,MAAAJ,iBAAA,KA6QwvC,CAAC;EAAA;AAAA;AAAA,SAAAK,6CAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAW,GAAA,GA7Q3vCvC,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAA8B,cAAA,wBA6QsuB,CAAC,oBAAD,CAAC;IA7QzuB9B,EAAE,CAAAyC,UAAA,6BAAAC,mFAAAC,MAAA;MAAF3C,EAAE,CAAA4C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF7C,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA6QuhCF,MAAA,CAAAG,eAAA,CAAAL,MAAA,CAAAM,KAA4B,EAAC;IAAA,CAAC,CAAC;IA7QxjCjD,EAAE,CAAAkD,UAAA,IAAAvB,yDAAA,wBA6QqwC,CAAC;IA7QxwC3B,EAAE,CAAAgC,YAAA,CA6Q4xC,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAuB,MAAA,GA7Q/xCnD,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmC,UAAA,eAAAgB,MAAA,CAAAC,oBA6QopB,CAAC,UAAAD,MAAA,CAAAE,KAAD,CAAC;IA7QvpBrD,EAAE,CAAAoC,SAAA,EA6Q2xB,CAAC;IA7Q9xBpC,EAAE,CAAAmC,UAAA,UAAAgB,MAAA,CAAAG,QA6Q2xB,CAAC,aAAAH,MAAA,CAAAI,QAAD,CAAC,oBAAAJ,MAAA,CAAAK,gBAAD,CAAC,eAAAL,MAAA,CAAAM,YAAA,CAAAC,UAAA,MAAD,CAAC,2BAAAP,MAAA,CAAAM,YAAA,CAAAE,sBAAD,CAAC;IA7Q9xB3D,EAAE,CAAAoC,SAAA,EA6Q8qC,CAAC;IA7QjrCpC,EAAE,CAAAmC,UAAA,YAAAgB,MAAA,CAAAS,yBA6Q8qC,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7QjrC5B,EAAE,CAAA8B,cAAA,aA6Qk7C,CAAC;IA7Qr7C9B,EAAE,CAAA+B,MAAA,EA6Q87C,CAAC;IA7Qj8C/B,EAAE,CAAAgC,YAAA,CA6Qo8C,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAkC,MAAA,GA7Qv8C9D,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAoC,SAAA,EA6Q87C,CAAC;IA7Qj8CpC,EAAE,CAAA+D,iBAAA,CAAAD,MAAA,CAAAR,QA6Q87C,CAAC;EAAA;AAAA;AAAA,SAAAU,4BAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7Qj8C5B,EAAE,CAAA8B,cAAA,aA6Q2Y,CAAC,aAAD,CAAC;IA7Q9Y9B,EAAE,CAAA+B,MAAA,EA6Q6gB,CAAC;IA7QhhB/B,EAAE,CAAAgC,YAAA,CA6QmhB,CAAC;IA7QthBhC,EAAE,CAAAkD,UAAA,IAAAZ,4CAAA,4BA6QqzC,CAAC;IA7QxzCtC,EAAE,CAAAkD,UAAA,IAAAW,iCAAA,iBA6Qo8C,CAAC;IA7Qv8C7D,EAAE,CAAAgC,YAAA,CA6Qg9C,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAqC,MAAA,GA7Qn9CjE,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAoC,SAAA,EA6Q+d,CAAC;IA7QlepC,EAAE,CAAAkE,qBAAA,OAAAD,MAAA,CAAAT,gBA6Q+d,CAAC;IA7QlexD,EAAE,CAAAoC,SAAA,EA6Q6gB,CAAC;IA7QhhBpC,EAAE,CAAAqC,kBAAA,MAAA4B,MAAA,CAAAE,KAAA,CAAAC,iBAAA,KA6Q6gB,CAAC;IA7QhhBpE,EAAE,CAAAoC,SAAA,EA6QkmB,CAAC;IA7QrmBpC,EAAE,CAAAmC,UAAA,SAAA8B,MAAA,CAAAL,yBAAA,CAAAS,MAAA,IA6QkmB,CAAC;IA7QrmBrE,EAAE,CAAAoC,SAAA,EA6Q+6C,CAAC;IA7Ql7CpC,EAAE,CAAAmC,UAAA,SAAA8B,MAAA,CAAAL,yBAAA,CAAAS,MAAA,KA6Q+6C,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2C,IAAA,GA7Ql7CvE,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAA8B,cAAA,gBA6Q2nE,CAAC;IA7Q9nE9B,EAAE,CAAAyC,UAAA,mBAAA+B,uDAAA;MAAFxE,EAAE,CAAA4C,aAAA,CAAA2B,IAAA;MAAA,MAAAE,MAAA,GAAFzE,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA6QqyD0B,MAAA,CAAAC,SAAA,CAAU,EAAC;IAAA,CAAC,CAAC;IA7QpzD1E,EAAE,CAAA2E,cAAA,CA6QoxE,CAAC;IA7QvxE3E,EAAE,CAAA8B,cAAA,YA6QoxE,CAAC;IA7QvxE9B,EAAE,CAAA4E,SAAA,cA6Qw2E,CAAC;IA7Q32E5E,EAAE,CAAAgC,YAAA,CA6Qw3E,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiD,MAAA,GA7Q33E7E,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmC,UAAA,eAAA0C,MAAA,CAAAV,KAAA,CAAAW,cA6Qg6D,CAAC,uBAAAD,MAAA,CAAAE,wBAAA,EAAD,CAAC,8BAAD,CAAC,aAAAF,MAAA,CAAAE,wBAAA,EAAD,CAAC;IA7Qn6D/E,EAAE,CAAAgF,WAAA,eAAAH,MAAA,CAAAV,KAAA,CAAAW,cA6Q22D,CAAC;EAAA;AAAA;AAAA,SAAAG,gCAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsD,IAAA,GA7Q92DlF,EAAE,CAAAwC,gBAAA;IAAFxC,EAAE,CAAA2E,cAAA;IAAF3E,EAAE,CAAAmF,eAAA,CA6QwrI,CAAC;IA7Q3rInF,EAAE,CAAA8B,cAAA,gBA6QwrI,CAAC;IA7Q3rI9B,EAAE,CAAAyC,UAAA,mBAAA2C,wDAAA;MAAFpF,EAAE,CAAA4C,aAAA,CAAAsC,IAAA;MAAA,MAAAG,OAAA,GAAFrF,EAAE,CAAA8C,aAAA;MAAA,OAAF9C,EAAE,CAAA+C,WAAA,CA6Q62HsC,OAAA,CAAAC,QAAA,CAAS,EAAC;IAAA,CAAC,CAAC;IA7Q33HtF,EAAE,CAAA2E,cAAA,CA6Qi1I,CAAC;IA7Qp1I3E,EAAE,CAAA8B,cAAA,YA6Qi1I,CAAC;IA7Qp1I9B,EAAE,CAAA4E,SAAA,cA6Qq6I,CAAC;IA7Qx6I5E,EAAE,CAAAgC,YAAA,CA6Qq7I,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA2D,MAAA,GA7Qx7IvF,EAAE,CAAA8C,aAAA;IAAF9C,EAAE,CAAAmC,UAAA,eAAAoD,MAAA,CAAApB,KAAA,CAAAqB,aA6Qq+H,CAAC,uBAAAD,MAAA,CAAAE,oBAAA,EAAD,CAAC,8BAAD,CAAC,aAAAF,MAAA,CAAAE,oBAAA,EAAD,CAAC;IA7Qx+HzF,EAAE,CAAAgF,WAAA,eAAAO,MAAA,CAAApB,KAAA,CAAAqB,aA6Qi7H,CAAC;EAAA;AAAA;AA1SxhI,MAAME,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI9E,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACsD,iBAAiB,GAAG,iBAAiB;IAC1C;IACA,IAAI,CAACyB,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACC,iBAAiB,GAAG,eAAe;IACxC;IACA,IAAI,CAAChB,cAAc,GAAG,YAAY;IAClC;IACA,IAAI,CAACU,aAAa,GAAG,WAAW;IAChC;IACA,IAAI,CAACO,aAAa,GAAG,CAACC,IAAI,EAAE1C,QAAQ,EAAEe,MAAM,KAAK;MAC7C,IAAIA,MAAM,IAAI,CAAC,IAAIf,QAAQ,IAAI,CAAC,EAAE;QAC9B,OAAQ,QAAOe,MAAO,EAAC;MAC3B;MACAA,MAAM,GAAG4B,IAAI,CAACC,GAAG,CAAC7B,MAAM,EAAE,CAAC,CAAC;MAC5B,MAAM8B,UAAU,GAAGH,IAAI,GAAG1C,QAAQ;MAClC;MACA,MAAM8C,QAAQ,GAAGD,UAAU,GAAG9B,MAAM,GAAG4B,IAAI,CAACI,GAAG,CAACF,UAAU,GAAG7C,QAAQ,EAAEe,MAAM,CAAC,GAAG8B,UAAU,GAAG7C,QAAQ;MACtG,OAAQ,GAAE6C,UAAU,GAAG,CAAE,MAAKC,QAAS,OAAM/B,MAAO,EAAC;IACzD,CAAC;EACL;EACA;IAAS,IAAI,CAACiC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFd,gBAAgB;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAACe,KAAK,kBAD6EzG,EAAE,CAAA0G,kBAAA;MAAAC,KAAA,EACYjB,gBAAgB;MAAAkB,OAAA,EAAhBlB,gBAAgB,CAAAY,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG9G,EAAE,CAAA+G,iBAAA,CAGXrB,gBAAgB,EAAc,CAAC;IAC9GsB,IAAI,EAAE/G,UAAU;IAChBgH,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASK,mCAAmCA,CAACC,UAAU,EAAE;EACrD,OAAOA,UAAU,IAAI,IAAIzB,gBAAgB,CAAC,CAAC;AAC/C;AACA;AACA,MAAM0B,2BAA2B,GAAG;EAChC;EACAC,OAAO,EAAE3B,gBAAgB;EACzB4B,IAAI,EAAE,CAAC,CAAC,IAAIpH,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAEuF,gBAAgB,CAAC,CAAC;EAC1D6B,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA,MAAMM,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;AAEhB;AACA,MAAMC,6BAA6B,GAAG,IAAItH,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA,MAAMuH,sBAAsB,GAAGrG,aAAa,CAACC,gBAAgB,CAAC,MAAM,EACnE,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA,MAAMqG,iBAAiB,SAASD,sBAAsB,CAAC;EACnD;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAAC5E,KAAK,EAAE;IACjB,IAAI,CAAC6E,UAAU,GAAG7B,IAAI,CAACC,GAAG,CAAC1E,oBAAoB,CAACyB,KAAK,CAAC,EAAE,CAAC,CAAC;IAC1D,IAAI,CAAC8E,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI3D,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC4D,OAAO;EACvB;EACA,IAAI5D,MAAMA,CAACpB,KAAK,EAAE;IACd,IAAI,CAACgF,OAAO,GAAGzG,oBAAoB,CAACyB,KAAK,CAAC;IAC1C,IAAI,CAAC8E,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI1E,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC4E,SAAS;EACzB;EACA,IAAI5E,QAAQA,CAACL,KAAK,EAAE;IAChB,IAAI,CAACiF,SAAS,GAAGjC,IAAI,CAACC,GAAG,CAAC1E,oBAAoB,CAACyB,KAAK,CAAC,EAAE,CAAC,CAAC;IACzD,IAAI,CAACkF,+BAA+B,CAAC,CAAC;EAC1C;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACnF,KAAK,EAAE;IACvB,IAAI,CAACoF,gBAAgB,GAAG,CAACpF,KAAK,IAAI,EAAE,EAAEqF,GAAG,CAACC,CAAC,IAAI/G,oBAAoB,CAAC+G,CAAC,CAAC,CAAC;IACvE,IAAI,CAACJ,+BAA+B,CAAC,CAAC;EAC1C;EACA;EACA,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACvF,KAAK,EAAE;IACpB,IAAI,CAACwF,aAAa,GAAGhH,qBAAqB,CAACwB,KAAK,CAAC;EACrD;EACA;EACA,IAAIyF,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACzF,KAAK,EAAE;IAC5B,IAAI,CAAC0F,qBAAqB,GAAGlH,qBAAqB,CAACwB,KAAK,CAAC;EAC7D;EACA0C,WAAWA,CAACxB,KAAK,EAAE4D,kBAAkB,EAAEa,QAAQ,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACzE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4D,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACD,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,OAAO,GAAG,CAAC;IAChB,IAAI,CAACI,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAAClF,YAAY,GAAG,CAAC,CAAC;IACtB;IACA,IAAI,CAACuC,IAAI,GAAG,IAAI3F,YAAY,CAAC,CAAC;IAC9B,IAAI,CAACwI,YAAY,GAAG1E,KAAK,CAACyB,OAAO,CAACkD,SAAS,CAAC,MAAM,IAAI,CAACf,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IACzF,IAAIY,QAAQ,EAAE;MACV,MAAM;QAAEtF,QAAQ;QAAE8E,eAAe;QAAEI,YAAY;QAAEE;MAAqB,CAAC,GAAGE,QAAQ;MAClF,IAAItF,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAAC4E,SAAS,GAAG5E,QAAQ;MAC7B;MACA,IAAI8E,eAAe,IAAI,IAAI,EAAE;QACzB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;MAC3C;MACA,IAAII,YAAY,IAAI,IAAI,EAAE;QACtB,IAAI,CAACC,aAAa,GAAGD,YAAY;MACrC;MACA,IAAIE,oBAAoB,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACC,qBAAqB,GAAGD,oBAAoB;MACrD;IACJ;EACJ;EACAK,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACb,+BAA+B,CAAC,CAAC;IACtC,IAAI,CAACc,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,YAAY,CAACM,WAAW,CAAC,CAAC;EACnC;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAACzB,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC0B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAACzB,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;IACnC,IAAI,CAAC0B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACA5E,SAASA,CAAA,EAAG;IACR;IACA,IAAI,CAAC,IAAI,CAAC+E,eAAe,CAAC,CAAC,EAAE;MACzB;IACJ;IACA,MAAMH,iBAAiB,GAAG,IAAI,CAACzB,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC0B,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAhE,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAAC+D,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAACzB,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG,IAAI,CAAC6B,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAC5C,IAAI,CAACH,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACAG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC5B,SAAS,IAAI,CAAC,IAAI,IAAI,CAACvE,QAAQ,IAAI,CAAC;EACpD;EACA;EACA+F,WAAWA,CAAA,EAAG;IACV,MAAMM,YAAY,GAAG,IAAI,CAACD,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAChD,OAAO,IAAI,CAAC7B,SAAS,GAAG8B,YAAY,IAAI,IAAI,CAACrG,QAAQ,IAAI,CAAC;EAC9D;EACA;EACAoG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACpG,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAO2C,IAAI,CAAC2D,IAAI,CAAC,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACf,QAAQ,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,eAAeA,CAACM,QAAQ,EAAE;IACtB;IACA;IACA,MAAM6C,UAAU,GAAG,IAAI,CAAC0B,SAAS,GAAG,IAAI,CAACvE,QAAQ;IACjD,MAAMgG,iBAAiB,GAAG,IAAI,CAACzB,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG5B,IAAI,CAAC4D,KAAK,CAAC1D,UAAU,GAAG7C,QAAQ,CAAC,IAAI,CAAC;IACvD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACiG,cAAc,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACA7D,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAClC,QAAQ,IAAI,CAAC,IAAI,CAAC8F,WAAW,CAAC,CAAC;EAC/C;EACA;EACAtE,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACxB,QAAQ,IAAI,CAAC,IAAI,CAACkG,eAAe,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACItB,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACa,YAAY,EAAE;MACpB;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAAC1F,QAAQ,EAAE;MAChB,IAAI,CAAC4E,SAAS,GACV,IAAI,CAACE,eAAe,CAAC/D,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC+D,eAAe,CAAC,CAAC,CAAC,GAAGZ,iBAAiB;IACtF;IACA,IAAI,CAAC5D,yBAAyB,GAAG,IAAI,CAACwE,eAAe,CAAC0B,KAAK,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAClG,yBAAyB,CAACmG,OAAO,CAAC,IAAI,CAACzG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACM,yBAAyB,CAACoG,IAAI,CAAC,IAAI,CAAC1G,QAAQ,CAAC;IACtD;IACA;IACA,IAAI,CAACM,yBAAyB,CAACqG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACpD,IAAI,CAACpC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACAuB,cAAcA,CAACD,iBAAiB,EAAE;IAC9B,IAAI,CAACtD,IAAI,CAACoE,IAAI,CAAC;MACXd,iBAAiB;MACjBzB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBvE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBe,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACiC,IAAI,YAAA+D,0BAAA7D,CAAA;MAvO8ExG,EAAE,CAAAsK,gBAAA;IAAA,CAuOmF;EAAE;EACvL;IAAS,IAAI,CAACC,IAAI,kBAxO8EvK,EAAE,CAAAwK,iBAAA;MAAAxD,IAAA,EAwOJY,iBAAiB;MAAA6C,MAAA;QAAApH,KAAA;QAAAwE,SAAA;QAAAxD,MAAA;QAAAf,QAAA;QAAA8E,eAAA;QAAAI,YAAA;QAAAE,oBAAA;QAAAjF,YAAA;MAAA;MAAAiH,OAAA;QAAA1E,IAAA;MAAA;MAAA2E,QAAA,GAxOf3K,EAAE,CAAA4K,0BAAA;IAAA,EAwO0T;EAAE;AACla;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA1OoG9G,EAAE,CAAA+G,iBAAA,CA0OXa,iBAAiB,EAAc,CAAC;IAC/GZ,IAAI,EAAE1G;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE0G,IAAI,EAAEtB;IAAiB,CAAC,EAAE;MAAEsB,IAAI,EAAEhH,EAAE,CAAC6K;IAAkB,CAAC,EAAE;MAAE7D,IAAI,EAAE8D;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEzH,KAAK,EAAE,CAAC;MACjJ2D,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEsH,SAAS,EAAE,CAAC;MACZb,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE8D,MAAM,EAAE,CAAC;MACT2C,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACX0D,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE6H,eAAe,EAAE,CAAC;MAClBpB,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEiI,YAAY,EAAE,CAAC;MACfxB,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEmI,oBAAoB,EAAE,CAAC;MACvB1B,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEkD,YAAY,EAAE,CAAC;MACfuD,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEyF,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAExG;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,IAAIuK,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASpD,iBAAiB,CAAC;EACzCjC,WAAWA,CAACsF,IAAI,EAAEC,iBAAiB,EAAEtC,QAAQ,EAAE;IAC3C,KAAK,CAACqC,IAAI,EAAEC,iBAAiB,EAAEtC,QAAQ,CAAC;IACxC;IACA,IAAI,CAACpF,gBAAgB,GAAI,iCAAgCuH,YAAY,EAAG,EAAC;IACzE,IAAI,CAAC3H,oBAAoB,GAAGwF,QAAQ,EAAEuC,mBAAmB,IAAI,SAAS;EAC1E;EACA;IAAS,IAAI,CAAC7E,IAAI,YAAA8E,qBAAA5E,CAAA;MAAA,YAAAA,CAAA,IAAwFwE,YAAY,EA5QtBhL,EAAE,CAAAqL,iBAAA,CA4QsC3F,gBAAgB,GA5QxD1F,EAAE,CAAAqL,iBAAA,CA4QmErL,EAAE,CAAC6K,iBAAiB,GA5QzF7K,EAAE,CAAAqL,iBAAA,CA4QoG3D,6BAA6B;IAAA,CAA4D;EAAE;EACjS;IAAS,IAAI,CAAC4D,IAAI,kBA7Q8EtL,EAAE,CAAAuL,iBAAA;MAAAvE,IAAA,EA6QJgE,YAAY;MAAAQ,SAAA;MAAAC,SAAA,WAA6F,OAAO;MAAAhB,MAAA;QAAAlH,QAAA;MAAA;MAAAmI,QAAA;MAAAf,QAAA,GA7Q9G3K,EAAE,CAAA4K,0BAAA;MAAAe,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAnK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAA8B,cAAA,YA6QmR,CAAC,YAAD,CAAC;UA7QtR9B,EAAE,CAAAkD,UAAA,IAAAc,2BAAA,gBA6Qg9C,CAAC;UA7Qn9ChE,EAAE,CAAA8B,cAAA,YA6QugD,CAAC,YAAD,CAAC;UA7Q1gD9B,EAAE,CAAA+B,MAAA,EA6QupD,CAAC;UA7Q1pD/B,EAAE,CAAAgC,YAAA,CA6Q6pD,CAAC;UA7QhqDhC,EAAE,CAAAkD,UAAA,IAAAoB,8BAAA,mBA6Qy4E,CAAC;UA7Q54EtE,EAAE,CAAA8B,cAAA,eA6Qm0F,CAAC;UA7Qt0F9B,EAAE,CAAAyC,UAAA,mBAAAuJ,8CAAA;YAAA,OA6QkhFnK,GAAA,CAAA2H,YAAA,CAAa,CAAC;UAAA,CAAC,CAAC;UA7QpiFxJ,EAAE,CAAA2E,cAAA,CA6Q49F,CAAC;UA7Q/9F3E,EAAE,CAAA8B,cAAA,YA6Q49F,CAAC;UA7Q/9F9B,EAAE,CAAA4E,SAAA,aA6QmiG,CAAC;UA7QtiG5E,EAAE,CAAAgC,YAAA,CA6QmjG,CAAC,CAAD,CAAC;UA7QtjGhC,EAAE,CAAAmF,eAAA,CA6Qs+G,CAAC;UA7Qz+GnF,EAAE,CAAA8B,cAAA,gBA6Qs+G,CAAC;UA7Qz+G9B,EAAE,CAAAyC,UAAA,mBAAAwJ,+CAAA;YAAA,OA6QysGpK,GAAA,CAAAuH,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;UA7QvtGpJ,EAAE,CAAA2E,cAAA,CA6Q+nH,CAAC;UA7QloH3E,EAAE,CAAA8B,cAAA,aA6Q+nH,CAAC;UA7QloH9B,EAAE,CAAA4E,SAAA,eA6QusH,CAAC;UA7Q1sH5E,EAAE,CAAAgC,YAAA,CA6QutH,CAAC,CAAD,CAAC;UA7Q1tHhC,EAAE,CAAAkD,UAAA,KAAA+B,+BAAA,oBA6Qs8I,CAAC;UA7Qz8IjF,EAAE,CAAAgC,YAAA,CA6Qk9I,CAAC,CAAD,CAAC,CAAD,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA7Qr9I5B,EAAE,CAAAoC,SAAA,EA6QwY,CAAC;UA7Q3YpC,EAAE,CAAAmC,UAAA,UAAAN,GAAA,CAAA2G,YA6QwY,CAAC;UA7Q3YxI,EAAE,CAAAoC,SAAA,EA6QupD,CAAC;UA7Q1pDpC,EAAE,CAAAqC,kBAAA,MAAAR,GAAA,CAAAsC,KAAA,CAAA4B,aAAA,CAAAlE,GAAA,CAAAgG,SAAA,EAAAhG,GAAA,CAAAyB,QAAA,EAAAzB,GAAA,CAAAwC,MAAA,MA6QupD,CAAC;UA7Q1pDrE,EAAE,CAAAoC,SAAA,EA6QwnE,CAAC;UA7Q3nEpC,EAAE,CAAAmC,UAAA,SAAAN,GAAA,CAAA6G,oBA6QwnE,CAAC;UA7Q3nE1I,EAAE,CAAAoC,SAAA,EA6QspF,CAAC;UA7QzpFpC,EAAE,CAAAmC,UAAA,eAAAN,GAAA,CAAAsC,KAAA,CAAA2B,iBA6QspF,CAAC,uBAAAjE,GAAA,CAAAkD,wBAAA,EAAD,CAAC,8BAAD,CAAC,aAAAlD,GAAA,CAAAkD,wBAAA,EAAD,CAAC;UA7QzpF/E,EAAE,CAAAgF,WAAA,eAAAnD,GAAA,CAAAsC,KAAA,CAAA2B,iBA6Q8lF,CAAC;UA7QjmF9F,EAAE,CAAAoC,SAAA,EA6Qi0G,CAAC;UA7Qp0GpC,EAAE,CAAAmC,UAAA,eAAAN,GAAA,CAAAsC,KAAA,CAAA0B,aA6Qi0G,CAAC,uBAAAhE,GAAA,CAAA4D,oBAAA,EAAD,CAAC,8BAAD,CAAC,aAAA5D,GAAA,CAAA4D,oBAAA,EAAD,CAAC;UA7Qp0GzF,EAAE,CAAAgF,WAAA,eAAAnD,GAAA,CAAAsC,KAAA,CAAA0B,aA6Q6wG,CAAC;UA7QhxG7F,EAAE,CAAAoC,SAAA,EA6QqrI,CAAC;UA7QxrIpC,EAAE,CAAAmC,UAAA,SAAAN,GAAA,CAAA6G,oBA6QqrI,CAAC;QAAA;MAAA;MAAAwD,YAAA,GAA4pEpM,EAAE,CAACqM,OAAO,EAAmHrM,EAAE,CAACsM,IAAI,EAA6FrL,EAAE,CAACsL,aAAa,EAA6I3K,EAAE,CAAC4K,YAAY,EAA4LrL,EAAE,CAACsL,SAAS,EAAiLlL,EAAE,CAACmL,SAAS,EAAgFrL,EAAE,CAACsL,UAAU;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAwJ;EAAE;AACj8O;AACA;EAAA,QAAA9F,SAAA,oBAAAA,SAAA,KA/QoG9G,EAAE,CAAA+G,iBAAA,CA+QXiE,YAAY,EAAc,CAAC;IAC1GhE,IAAI,EAAEvG,SAAS;IACfwG,IAAI,EAAE,CAAC;MAAE4F,QAAQ,EAAE,eAAe;MAAEnB,QAAQ,EAAE,cAAc;MAAEjB,MAAM,EAAE,CAAC,UAAU,CAAC;MAAEqC,IAAI,EAAE;QAC9E,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE;MACZ,CAAC;MAAEF,eAAe,EAAElM,uBAAuB,CAACqM,MAAM;MAAEJ,aAAa,EAAEhM,iBAAiB,CAACqM,IAAI;MAAElB,QAAQ,EAAE,swIAAswI;MAAEY,MAAM,EAAE,CAAC,kzDAAkzD;IAAE,CAAC;EACvrM,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1F,IAAI,EAAEtB;IAAiB,CAAC,EAAE;MAAEsB,IAAI,EAAEhH,EAAE,CAAC6K;IAAkB,CAAC,EAAE;MAAE7D,IAAI,EAAE8D,SAAS;MAAEmC,UAAU,EAAE,CAAC;QAC1HjG,IAAI,EAAE9G;MACV,CAAC,EAAE;QACC8G,IAAI,EAAEpG,MAAM;QACZqG,IAAI,EAAE,CAACS,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMwF,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAAC5G,IAAI,YAAA6G,2BAAA3G,CAAA;MAAA,YAAAA,CAAA,IAAwF0G,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBA9R8EpN,EAAE,CAAAqN,gBAAA;MAAArG,IAAA,EA8RSkG;IAAkB,EAAuI;EAAE;EACtQ;IAAS,IAAI,CAACI,IAAI,kBA/R8EtN,EAAE,CAAAuN,gBAAA;MAAAC,SAAA,EA+RwC,CAACpG,2BAA2B,CAAC;MAAAqG,OAAA,GAAY1N,YAAY,EAAEiB,eAAe,EAAEE,eAAe,EAAEE,gBAAgB;IAAA,EAAI;EAAE;AAC7P;AACA;EAAA,QAAA0F,SAAA,oBAAAA,SAAA,KAjSoG9G,EAAE,CAAA+G,iBAAA,CAiSXmG,kBAAkB,EAAc,CAAC;IAChHlG,IAAI,EAAEnG,QAAQ;IACdoG,IAAI,EAAE,CAAC;MACCwG,OAAO,EAAE,CAAC1N,YAAY,EAAEiB,eAAe,EAAEE,eAAe,EAAEE,gBAAgB,CAAC;MAC3EsM,OAAO,EAAE,CAAC1C,YAAY,CAAC;MACvB2C,YAAY,EAAE,CAAC3C,YAAY,CAAC;MAC5BwC,SAAS,EAAE,CAACpG,2BAA2B;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASM,6BAA6B,EAAEN,2BAA2B,EAAEF,mCAAmC,EAAE8D,YAAY,EAAEtF,gBAAgB,EAAEwH,kBAAkB,EAAEzF,SAAS,EAAEG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}