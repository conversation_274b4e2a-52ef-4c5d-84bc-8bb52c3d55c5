<div class="login-container">
    <mat-card class="login-card">
        <mat-card-header>
            <mat-card-title>Login to FindIt</mat-card-title>
        </mat-card-header>
        <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Email</mat-label>
                    <input matInput formControlName="email" placeholder="Enter your email" required>
                    <mat-error *ngIf="f['email'].errors?.['required']">Email is required</mat-error>
                    <mat-error *ngIf="f['email'].errors?.['email']">Please enter a valid email address</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Password</mat-label>
                    <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password"
                        placeholder="Enter your password" required>
                    <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                    </button>
                    <mat-error *ngIf="f['password'].errors?.['required']">Password is required</mat-error>
                    <mat-error *ngIf="f['password'].errors?.['minlength']">Password must be at least 6
                        characters</mat-error>
                </mat-form-field>

                <div class="button-container">
                    <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || loading">
                        <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
                        <span *ngIf="!loading">Login</span>
                    </button>
                </div>
            </form>
        </mat-card-content>
        <mat-card-actions>
            <p>Don't have an account? <a routerLink="/register">Register</a></p>
        </mat-card-actions>
    </mat-card>
</div>