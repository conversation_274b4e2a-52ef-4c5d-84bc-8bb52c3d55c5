{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/job.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"../job-form/job-form.component\";\nexport class JobCreateComponent {\n  constructor(jobService, authService, router, snackBar) {\n    this.jobService = jobService;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.loading = false;\n  }\n  ngOnInit() {\n    // Check if user is authenticated and is an employer\n    if (!this.authService.checkAuth() || !this.authService.isEmployer()) {\n      this.snackBar.open('Only employers can create jobs', 'Close', {\n        duration: 5000\n      });\n      this.router.navigate(['/jobs']);\n    }\n  }\n  onSubmit(jobRequest) {\n    this.loading = true;\n    this.jobService.createJob(jobRequest).subscribe({\n      next: response => {\n        this.snackBar.open('Job created successfully!', 'Close', {\n          duration: 5000,\n          panelClass: ['success-snackbar']\n        });\n        this.loading = false;\n        // Navigate to the newly created job\n        if (response.data.job.id) {\n          this.router.navigate(['/jobs', response.data.job.id]);\n        } else {\n          this.router.navigate(['/my-jobs']);\n        }\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error creating job', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onCancel() {\n    this.router.navigate(['/my-jobs']);\n  }\n  static {\n    this.ɵfac = function JobCreateComponent_Factory(t) {\n      return new (t || JobCreateComponent)(i0.ɵɵdirectiveInject(i1.JobService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: JobCreateComponent,\n      selectors: [[\"app-job-create\"]],\n      decls: 9,\n      vars: 1,\n      consts: [[1, \"job-create-container\"], [1, \"job-create-header\"], [1, \"job-create-card\"], [3, \"loading\", \"formSubmit\", \"formCancel\"]],\n      template: function JobCreateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Post a New Job\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Fill out the form below to create a new job listing.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-card\", 2)(7, \"mat-card-content\")(8, \"app-job-form\", 3);\n          i0.ɵɵlistener(\"formSubmit\", function JobCreateComponent_Template_app_job_form_formSubmit_8_listener($event) {\n            return ctx.onSubmit($event);\n          })(\"formCancel\", function JobCreateComponent_Template_app_job_form_formCancel_8_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"loading\", ctx.loading);\n        }\n      },\n      dependencies: [i5.MatCard, i5.MatCardContent, i6.JobFormComponent],\n      styles: [\".job-create-container[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.job-create-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.job-create-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n.job-create-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.job-create-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9qb2ItY3JlYXRlL2pvYi1jcmVhdGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxtQkFBQTtBQUNGOztBQUVBO0VBQ0UsbUJBQUE7QUFDRjtBQUNFO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQUNKO0FBRUU7RUFDRSxXQUFBO0VBQ0EsU0FBQTtBQUFKOztBQUlBO0VBQ0UsbUJBQUE7QUFERiIsInNvdXJjZXNDb250ZW50IjpbIi5qb2ItY3JlYXRlLWNvbnRhaW5lciB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxufVxyXG5cclxuLmpvYi1jcmVhdGUtaGVhZGVyIHtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG5cclxuICBoMSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gIH1cclxuXHJcbiAgcCB7XHJcbiAgICBjb2xvcjogIzY2NjtcclxuICAgIG1hcmdpbjogMDtcclxuICB9XHJcbn1cclxuXHJcbi5qb2ItY3JlYXRlLWNhcmQge1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["JobCreateComponent", "constructor", "jobService", "authService", "router", "snackBar", "loading", "ngOnInit", "checkAuth", "isEmployer", "open", "duration", "navigate", "onSubmit", "jobRequest", "createJob", "subscribe", "next", "response", "panelClass", "data", "job", "id", "error", "message", "onCancel", "i0", "ɵɵdirectiveInject", "i1", "JobService", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "JobCreateComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "JobCreateComponent_Template_app_job_form_formSubmit_8_listener", "$event", "JobCreateComponent_Template_app_job_form_formCancel_8_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\findit\\client\\src\\app\\components\\job-create\\job-create.component.ts", "D:\\findit\\client\\src\\app\\components\\job-create\\job-create.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { JobService } from '../../services/job.service';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { JobRequest } from '../../models/job.model';\r\n\r\n@Component({\r\n  selector: 'app-job-create',\r\n  templateUrl: './job-create.component.html',\r\n  styleUrls: ['./job-create.component.scss']\r\n})\r\nexport class JobCreateComponent implements OnInit {\r\n  loading = false;\r\n\r\n  constructor(\r\n    private jobService: JobService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // Check if user is authenticated and is an employer\r\n    if (!this.authService.checkAuth() || !this.authService.isEmployer()) {\r\n      this.snackBar.open('Only employers can create jobs', 'Close', {\r\n        duration: 5000\r\n      });\r\n      this.router.navigate(['/jobs']);\r\n    }\r\n  }\r\n\r\n  onSubmit(jobRequest: JobRequest): void {\r\n    this.loading = true;\r\n\r\n    this.jobService.createJob(jobRequest).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('Job created successfully!', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n        this.loading = false;\r\n\r\n        // Navigate to the newly created job\r\n        if (response.data.job.id) {\r\n          this.router.navigate(['/jobs', response.data.job.id]);\r\n        } else {\r\n          this.router.navigate(['/my-jobs']);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error creating job', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.router.navigate(['/my-jobs']);\r\n  }\r\n}\r\n", "<div class=\"job-create-container\">\r\n    <div class=\"job-create-header\">\r\n        <h1>Post a New Job</h1>\r\n        <p>Fill out the form below to create a new job listing.</p>\r\n    </div>\r\n\r\n    <mat-card class=\"job-create-card\">\r\n        <mat-card-content>\r\n            <app-job-form [loading]=\"loading\" (formSubmit)=\"onSubmit($event)\" (formCancel)=\"onCancel()\">\r\n            </app-job-form>\r\n        </mat-card-content>\r\n    </mat-card>\r\n</div>"], "mappings": ";;;;;;;AAYA,OAAM,MAAOA,kBAAkB;EAG7BC,YACUC,UAAsB,EACtBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IANlB,KAAAC,OAAO,GAAG,KAAK;EAOX;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACJ,WAAW,CAACK,SAAS,EAAE,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,UAAU,EAAE,EAAE;MACnE,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;QAC5DC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;EAEnC;EAEAC,QAAQA,CAACC,UAAsB;IAC7B,IAAI,CAACR,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACJ,UAAU,CAACa,SAAS,CAACD,UAAU,CAAC,CAACE,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACb,QAAQ,CAACK,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE;UACvDC,QAAQ,EAAE,IAAI;UACdQ,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAACb,OAAO,GAAG,KAAK;QAEpB;QACA,IAAIY,QAAQ,CAACE,IAAI,CAACC,GAAG,CAACC,EAAE,EAAE;UACxB,IAAI,CAAClB,MAAM,CAACQ,QAAQ,CAAC,CAAC,OAAO,EAAEM,QAAQ,CAACE,IAAI,CAACC,GAAG,CAACC,EAAE,CAAC,CAAC;SACtD,MAAM;UACL,IAAI,CAAClB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;MAEtC,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAClB,QAAQ,CAACK,IAAI,CAACa,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UACxEb,QAAQ,EAAE,IAAI;UACdQ,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACb,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmB,QAAQA,CAAA;IACN,IAAI,CAACrB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;;;uBAlDWZ,kBAAkB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBnC,kBAAkB;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ/BhB,EAAA,CAAAkB,cAAA,aAAkC;UAEtBlB,EAAA,CAAAmB,MAAA,qBAAc;UAAAnB,EAAA,CAAAoB,YAAA,EAAK;UACvBpB,EAAA,CAAAkB,cAAA,QAAG;UAAAlB,EAAA,CAAAmB,MAAA,2DAAoD;UAAAnB,EAAA,CAAAoB,YAAA,EAAI;UAG/DpB,EAAA,CAAAkB,cAAA,kBAAkC;UAEQlB,EAAA,CAAAqB,UAAA,wBAAAC,+DAAAC,MAAA;YAAA,OAAcN,GAAA,CAAA9B,QAAA,CAAAoC,MAAA,CAAgB;UAAA,EAAC,wBAAAC,+DAAA;YAAA,OAAeP,GAAA,CAAAlB,QAAA,EAAU;UAAA,EAAzB;UACjEC,EAAA,CAAAoB,YAAA,EAAe;;;UADDpB,EAAA,CAAAyB,SAAA,GAAmB;UAAnBzB,EAAA,CAAA0B,UAAA,YAAAT,GAAA,CAAArC,OAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}