.application-list-container {
  margin-bottom: 30px;
}

.application-list-header {
  margin-bottom: 20px;

  h1 {
    margin-bottom: 8px;
    font-weight: 500;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  color: #666;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 18px;
    margin-bottom: 24px;
  }
}

.applications-table-container {
  overflow-x: auto;
}

.applications-table {
  width: 100%;

  th.mat-header-cell {
    font-weight: 500;
    color: #333;
  }

  .mat-row:hover {
    background-color: #f5f5f5;
  }
}

.job-link {
  color: #3f51b5;
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.status-chip {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

// Application status colors
.status-applied {
  background-color: #2196f3;
  color: white;
}

.status-reviewed {
  background-color: #ff9800;
  color: white;
}

.status-interviewed {
  background-color: #9c27b0;
  color: white;
}

.status-rejected {
  background-color: #f44336;
  color: white;
}

.status-hired {
  background-color: #4caf50;
  color: white;
}

@media (max-width: 768px) {
  .applications-table {
    th.mat-header-cell,
    td.mat-cell {
      padding: 8px;
    }
  }
}