{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ApplicationService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  /**\n   * Apply for a job\n   * @param applicationRequest Application request\n   * @returns Observable of application response\n   */\n  applyForJob(applicationRequest) {\n    return this.apiService.post('applications', applicationRequest);\n  }\n  /**\n   * Get applications for a job (employer only)\n   * @param jobId Job ID\n   * @param params Query parameters\n   * @returns Observable of applications response\n   */\n  getJobApplications(jobId, params) {\n    return this.apiService.get(`applications/job/${jobId}`, params);\n  }\n  /**\n   * Update application status (employer only)\n   * @param id Application ID\n   * @param statusRequest Status request\n   * @returns Observable of application response\n   */\n  updateApplicationStatus(id, statusRequest) {\n    return this.apiService.put(`applications/${id}/status`, statusRequest);\n  }\n  /**\n   * Get user's applications (job seeker only)\n   * @param params Query parameters\n   * @returns Observable of applications response\n   */\n  getUserApplications(params) {\n    return this.apiService.get('applications/me', params);\n  }\n  /**\n   * Get application by ID\n   * @param id Application ID\n   * @returns Observable of application response\n   */\n  getApplicationById(id) {\n    return this.apiService.get(`applications/${id}`);\n  }\n  static {\n    this.ɵfac = function ApplicationService_Factory(t) {\n      return new (t || ApplicationService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApplicationService,\n      factory: ApplicationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ApplicationService", "constructor", "apiService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applicationRequest", "post", "getJobApplications", "jobId", "params", "get", "updateApplicationStatus", "id", "statusRequest", "put", "getUserApplications", "getApplicationById", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\services\\application.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ApiService } from './api.service';\r\nimport {\r\n  Application,\r\n  ApplicationResponse,\r\n  ApplicationsResponse,\r\n  ApplicationRequest,\r\n  ApplicationStatusRequest\r\n} from '../models/application.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ApplicationService {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  /**\r\n   * Apply for a job\r\n   * @param applicationRequest Application request\r\n   * @returns Observable of application response\r\n   */\r\n  applyForJob(applicationRequest: ApplicationRequest): Observable<ApplicationResponse> {\r\n    return this.apiService.post<ApplicationResponse>('applications', applicationRequest);\r\n  }\r\n\r\n  /**\r\n   * Get applications for a job (employer only)\r\n   * @param jobId Job ID\r\n   * @param params Query parameters\r\n   * @returns Observable of applications response\r\n   */\r\n  getJobApplications(jobId: number, params?: any): Observable<ApplicationsResponse> {\r\n    return this.apiService.get<ApplicationsResponse>(`applications/job/${jobId}`, params);\r\n  }\r\n\r\n  /**\r\n   * Update application status (employer only)\r\n   * @param id Application ID\r\n   * @param statusRequest Status request\r\n   * @returns Observable of application response\r\n   */\r\n  updateApplicationStatus(id: number, statusRequest: ApplicationStatusRequest): Observable<ApplicationResponse> {\r\n    return this.apiService.put<ApplicationResponse>(`applications/${id}/status`, statusRequest);\r\n  }\r\n\r\n  /**\r\n   * Get user's applications (job seeker only)\r\n   * @param params Query parameters\r\n   * @returns Observable of applications response\r\n   */\r\n  getUserApplications(params?: any): Observable<ApplicationsResponse> {\r\n    return this.apiService.get<ApplicationsResponse>('applications/me', params);\r\n  }\r\n\r\n  /**\r\n   * Get application by ID\r\n   * @param id Application ID\r\n   * @returns Observable of application response\r\n   */\r\n  getApplicationById(id: number): Observable<ApplicationResponse> {\r\n    return this.apiService.get<ApplicationResponse>(`applications/${id}`);\r\n  }\r\n}\r\n"], "mappings": ";;AAcA,OAAM,MAAOA,kBAAkB;EAE7BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9C;;;;;EAKAC,WAAWA,CAACC,kBAAsC;IAChD,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAsB,cAAc,EAAED,kBAAkB,CAAC;EACtF;EAEA;;;;;;EAMAE,kBAAkBA,CAACC,KAAa,EAAEC,MAAY;IAC5C,OAAO,IAAI,CAACN,UAAU,CAACO,GAAG,CAAuB,oBAAoBF,KAAK,EAAE,EAAEC,MAAM,CAAC;EACvF;EAEA;;;;;;EAMAE,uBAAuBA,CAACC,EAAU,EAAEC,aAAuC;IACzE,OAAO,IAAI,CAACV,UAAU,CAACW,GAAG,CAAsB,gBAAgBF,EAAE,SAAS,EAAEC,aAAa,CAAC;EAC7F;EAEA;;;;;EAKAE,mBAAmBA,CAACN,MAAY;IAC9B,OAAO,IAAI,CAACN,UAAU,CAACO,GAAG,CAAuB,iBAAiB,EAAED,MAAM,CAAC;EAC7E;EAEA;;;;;EAKAO,kBAAkBA,CAACJ,EAAU;IAC3B,OAAO,IAAI,CAACT,UAAU,CAACO,GAAG,CAAsB,gBAAgBE,EAAE,EAAE,CAAC;EACvE;;;uBAjDWX,kBAAkB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBnB,kBAAkB;MAAAoB,OAAA,EAAlBpB,kBAAkB,CAAAqB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}