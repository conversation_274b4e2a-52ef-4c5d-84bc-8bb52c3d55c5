{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/profile.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction ProfileComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name cannot exceed 50 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Last name cannot exceed 50 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Phone number cannot exceed 20 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Address cannot exceed 100 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_29_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Resume URL cannot exceed 255 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h2\");\n    i0.ɵɵtext(2, \"Job Seeker Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 23)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"Resume URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 24);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_div_29_mat_error_7_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.profileForm.get(\"resumeUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction ProfileComponent_div_7_div_30_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Company name cannot exceed 100 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_30_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Company description cannot exceed 1000 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h2\");\n    i0.ɵɵtext(2, \"Company Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 23)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"Company Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 25);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_div_30_mat_error_7_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 23)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Company Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"textarea\", 26);\n    i0.ɵɵtemplate(12, ProfileComponent_div_7_div_30_mat_error_12_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.profileForm.get(\"companyName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r7.profileForm.get(\"companyDescription\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction ProfileComponent_div_7_mat_spinner_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction ProfileComponent_div_7_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Profile\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"mat-card\", 7)(2, \"mat-card-content\")(3, \"form\", 8);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_7_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onSubmit());\n    });\n    i0.ɵɵelementStart(4, \"div\", 9)(5, \"h2\");\n    i0.ɵɵtext(6, \"Personal Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"mat-form-field\", 11)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 12);\n    i0.ɵɵtemplate(12, ProfileComponent_div_7_mat_error_12_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 11)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 14);\n    i0.ɵɵtemplate(17, ProfileComponent_div_7_mat_error_17_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"mat-form-field\", 11)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 15);\n    i0.ɵɵtemplate(23, ProfileComponent_div_7_mat_error_23_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 11)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 16);\n    i0.ɵɵtemplate(28, ProfileComponent_div_7_mat_error_28_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(29, ProfileComponent_div_7_div_29_Template, 8, 1, \"div\", 17);\n    i0.ɵɵtemplate(30, ProfileComponent_div_7_div_30_Template, 13, 2, \"div\", 17);\n    i0.ɵɵelementStart(31, \"div\", 18)(32, \"button\", 19);\n    i0.ɵɵtemplate(33, ProfileComponent_div_7_mat_spinner_33_Template, 1, 0, \"mat-spinner\", 20);\n    i0.ɵɵtemplate(34, ProfileComponent_div_7_span_34_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 21)(36, \"mat-card-header\")(37, \"mat-card-title\");\n    i0.ɵɵtext(38, \"Account Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"mat-card-content\")(40, \"div\", 22)(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\")(46, \"strong\");\n    i0.ɵɵtext(47, \"Role:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.profileForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.profileForm.get(\"lastName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.profileForm.get(\"phone\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.profileForm.get(\"address\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJobSeeker());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEmployer());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.profileForm.invalid || ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.saving);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user == null ? null : ctx_r1.user.email, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.user == null ? null : ctx_r1.user.role) === \"job_seeker\" ? \"Job Seeker\" : \"Employer\", \"\");\n  }\n}\nexport class ProfileComponent {\n  constructor(formBuilder, authService, profileService, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.profileService = profileService;\n    this.snackBar = snackBar;\n    this.user = null;\n    this.profile = null;\n    this.loading = false;\n    this.saving = false;\n  }\n  ngOnInit() {\n    this.initForm();\n    this.loadUserProfile();\n  }\n  initForm() {\n    this.profileForm = this.formBuilder.group({\n      // Common fields\n      firstName: ['', [Validators.maxLength(50)]],\n      lastName: ['', [Validators.maxLength(50)]],\n      phone: ['', [Validators.maxLength(20)]],\n      address: ['', [Validators.maxLength(100)]],\n      // Job seeker fields\n      resumeUrl: ['', [Validators.maxLength(255)]],\n      // Employer fields\n      companyName: ['', [Validators.maxLength(100)]],\n      companyDescription: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  loadUserProfile() {\n    this.loading = true;\n    // Get current user from local storage\n    const userJson = localStorage.getItem('user');\n    if (userJson) {\n      this.user = JSON.parse(userJson);\n    }\n    // Get profile\n    this.profileService.getProfile().subscribe({\n      next: response => {\n        this.profile = response.data.profile;\n        this.updateFormWithProfileData();\n        this.loading = false;\n      },\n      error: error => {\n        // If profile doesn't exist yet, that's okay\n        if (error.status !== 404) {\n          this.snackBar.open(error.error?.message || 'Error loading profile', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n        this.loading = false;\n      }\n    });\n  }\n  updateFormWithProfileData() {\n    if (!this.profile) return;\n    this.profileForm.patchValue({\n      firstName: this.profile.firstName || '',\n      lastName: this.profile.lastName || '',\n      phone: this.profile.phone || '',\n      address: this.profile.address || '',\n      resumeUrl: this.profile.resumeUrl || '',\n      companyName: this.profile.companyName || '',\n      companyDescription: this.profile.companyDescription || ''\n    });\n  }\n  onSubmit() {\n    if (this.profileForm.invalid) {\n      return;\n    }\n    this.saving = true;\n    const profileData = {\n      firstName: this.profileForm.get('firstName')?.value,\n      lastName: this.profileForm.get('lastName')?.value,\n      phone: this.profileForm.get('phone')?.value,\n      address: this.profileForm.get('address')?.value\n    };\n    // Add role-specific fields\n    if (this.isJobSeeker()) {\n      profileData.resumeUrl = this.profileForm.get('resumeUrl')?.value;\n    } else if (this.isEmployer()) {\n      profileData.companyName = this.profileForm.get('companyName')?.value;\n      profileData.companyDescription = this.profileForm.get('companyDescription')?.value;\n    }\n    this.profileService.updateProfile(profileData).subscribe({\n      next: response => {\n        this.profile = response.data.profile;\n        this.snackBar.open('Profile updated successfully!', 'Close', {\n          duration: 5000,\n          panelClass: ['success-snackbar']\n        });\n        this.saving = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error updating profile', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.saving = false;\n      }\n    });\n  }\n  isJobSeeker() {\n    return this.user?.role === 'job_seeker';\n  }\n  isEmployer() {\n    return this.user?.role === 'employer';\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ProfileService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"profile-container\"], [1, \"profile-header\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"profile-content\"], [1, \"profile-card\"], [1, \"profile-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"placeholder\", \"Enter your phone number\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\"], [\"class\", \"form-section\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"account-card\"], [1, \"account-info\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"resumeUrl\", \"placeholder\", \"Enter URL to your resume\"], [\"matInput\", \"\", \"formControlName\", \"companyName\", \"placeholder\", \"Enter your company name\"], [\"matInput\", \"\", \"formControlName\", \"companyDescription\", \"placeholder\", \"Enter a description of your company\", \"rows\", \"4\"], [\"diameter\", \"20\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"My Profile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Manage your personal information and preferences.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ProfileComponent_div_6_Template, 2, 0, \"div\", 2);\n          i0.ɵɵtemplate(7, ProfileComponent_div_7_Template, 49, 12, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i10.MatProgressSpinner],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n.profile-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.profile-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 20px;\\n}\\n\\n.profile-card[_ngcontent-%COMP%], .account-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.profile-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.form-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n  color: #333;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 16px;\\n}\\n\\n.account-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .profile-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "ProfileComponent_div_7_div_29_mat_error_7_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r6", "profileForm", "get", "errors", "ProfileComponent_div_7_div_30_mat_error_7_Template", "ProfileComponent_div_7_div_30_mat_error_12_Template", "ctx_r7", "tmp_1_0", "ɵɵlistener", "ProfileComponent_div_7_Template_form_ngSubmit_3_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProfileComponent_div_7_mat_error_12_Template", "ProfileComponent_div_7_mat_error_17_Template", "ProfileComponent_div_7_mat_error_23_Template", "ProfileComponent_div_7_mat_error_28_Template", "ProfileComponent_div_7_div_29_Template", "ProfileComponent_div_7_div_30_Template", "ProfileComponent_div_7_mat_spinner_33_Template", "ProfileComponent_div_7_span_34_Template", "ctx_r1", "tmp_2_0", "tmp_3_0", "tmp_4_0", "isJobSeeker", "isEmployer", "invalid", "saving", "ɵɵtextInterpolate1", "user", "email", "role", "ProfileComponent", "constructor", "formBuilder", "authService", "profileService", "snackBar", "profile", "loading", "ngOnInit", "initForm", "loadUserProfile", "group", "firstName", "max<PERSON><PERSON><PERSON>", "lastName", "phone", "address", "resumeUrl", "companyName", "companyDescription", "userJson", "localStorage", "getItem", "JSON", "parse", "getProfile", "subscribe", "next", "response", "data", "updateFormWithProfileData", "error", "status", "open", "message", "duration", "panelClass", "patchValue", "profileData", "value", "updateProfile", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "ProfileService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_6_Template", "ProfileComponent_div_7_Template"], "sources": ["D:\\findit\\client\\src\\app\\components\\profile\\profile.component.ts", "D:\\findit\\client\\src\\app\\components\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { ProfileService, ProfileUpdateRequest } from '../../services/profile.service';\r\nimport { User, Profile } from '../../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.scss']\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  profileForm!: FormGroup;\r\n  user: User | null = null;\r\n  profile: Profile | null = null;\r\n  loading = false;\r\n  saving = false;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private profileService: ProfileService,\r\n    private snackBar: MatSnackBar\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.initForm();\r\n    this.loadUserProfile();\r\n  }\r\n\r\n  initForm(): void {\r\n    this.profileForm = this.formBuilder.group({\r\n      // Common fields\r\n      firstName: ['', [Validators.maxLength(50)]],\r\n      lastName: ['', [Validators.maxLength(50)]],\r\n      phone: ['', [Validators.maxLength(20)]],\r\n      address: ['', [Validators.maxLength(100)]],\r\n\r\n      // Job seeker fields\r\n      resumeUrl: ['', [Validators.maxLength(255)]],\r\n\r\n      // Employer fields\r\n      companyName: ['', [Validators.maxLength(100)]],\r\n      companyDescription: ['', [Validators.maxLength(1000)]]\r\n    });\r\n  }\r\n\r\n  loadUserProfile(): void {\r\n    this.loading = true;\r\n\r\n    // Get current user from local storage\r\n    const userJson = localStorage.getItem('user');\r\n    if (userJson) {\r\n      this.user = JSON.parse(userJson);\r\n    }\r\n\r\n    // Get profile\r\n    this.profileService.getProfile().subscribe({\r\n      next: (response) => {\r\n        this.profile = response.data.profile;\r\n        this.updateFormWithProfileData();\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        // If profile doesn't exist yet, that's okay\r\n        if (error.status !== 404) {\r\n          this.snackBar.open(error.error?.message || 'Error loading profile', 'Close', {\r\n            duration: 5000,\r\n            panelClass: ['error-snackbar']\r\n          });\r\n        }\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  updateFormWithProfileData(): void {\r\n    if (!this.profile) return;\r\n\r\n    this.profileForm.patchValue({\r\n      firstName: this.profile.firstName || '',\r\n      lastName: this.profile.lastName || '',\r\n      phone: this.profile.phone || '',\r\n      address: this.profile.address || '',\r\n      resumeUrl: this.profile.resumeUrl || '',\r\n      companyName: this.profile.companyName || '',\r\n      companyDescription: this.profile.companyDescription || ''\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.profileForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n\r\n    const profileData: ProfileUpdateRequest = {\r\n      firstName: this.profileForm.get('firstName')?.value,\r\n      lastName: this.profileForm.get('lastName')?.value,\r\n      phone: this.profileForm.get('phone')?.value,\r\n      address: this.profileForm.get('address')?.value\r\n    };\r\n\r\n    // Add role-specific fields\r\n    if (this.isJobSeeker()) {\r\n      profileData.resumeUrl = this.profileForm.get('resumeUrl')?.value;\r\n    } else if (this.isEmployer()) {\r\n      profileData.companyName = this.profileForm.get('companyName')?.value;\r\n      profileData.companyDescription = this.profileForm.get('companyDescription')?.value;\r\n    }\r\n\r\n    this.profileService.updateProfile(profileData).subscribe({\r\n      next: (response) => {\r\n        this.profile = response.data.profile;\r\n        this.snackBar.open('Profile updated successfully!', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n        this.saving = false;\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error updating profile', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        this.saving = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  isJobSeeker(): boolean {\r\n    return this.user?.role === 'job_seeker';\r\n  }\r\n\r\n  isEmployer(): boolean {\r\n    return this.user?.role === 'employer';\r\n  }\r\n}\r\n", "<div class=\"profile-container\">\r\n    <div class=\"profile-header\">\r\n        <h1>My Profile</h1>\r\n        <p>Manage your personal information and preferences.</p>\r\n    </div>\r\n\r\n    <!-- Loading spinner -->\r\n    <div *ngIf=\"loading\" class=\"loading-container\">\r\n        <mat-spinner diameter=\"40\"></mat-spinner>\r\n    </div>\r\n\r\n    <!-- Profile form -->\r\n    <div *ngIf=\"!loading\" class=\"profile-content\">\r\n        <mat-card class=\"profile-card\">\r\n            <mat-card-content>\r\n                <form [formGroup]=\"profileForm\" (ngSubmit)=\"onSubmit()\" class=\"profile-form\">\r\n                    <div class=\"form-section\">\r\n                        <h2>Personal Information</h2>\r\n\r\n                        <div class=\"form-row\">\r\n                            <mat-form-field appearance=\"outline\">\r\n                                <mat-label>First Name</mat-label>\r\n                                <input matInput formControlName=\"firstName\" placeholder=\"Enter your first name\">\r\n                                <mat-error *ngIf=\"profileForm.get('firstName')?.errors?.['maxlength']\">\r\n                                    First name cannot exceed 50 characters\r\n                                </mat-error>\r\n                            </mat-form-field>\r\n\r\n                            <mat-form-field appearance=\"outline\">\r\n                                <mat-label>Last Name</mat-label>\r\n                                <input matInput formControlName=\"lastName\" placeholder=\"Enter your last name\">\r\n                                <mat-error *ngIf=\"profileForm.get('lastName')?.errors?.['maxlength']\">\r\n                                    Last name cannot exceed 50 characters\r\n                                </mat-error>\r\n                            </mat-form-field>\r\n                        </div>\r\n\r\n                        <div class=\"form-row\">\r\n                            <mat-form-field appearance=\"outline\">\r\n                                <mat-label>Phone</mat-label>\r\n                                <input matInput formControlName=\"phone\" placeholder=\"Enter your phone number\">\r\n                                <mat-error *ngIf=\"profileForm.get('phone')?.errors?.['maxlength']\">\r\n                                    Phone number cannot exceed 20 characters\r\n                                </mat-error>\r\n                            </mat-form-field>\r\n\r\n                            <mat-form-field appearance=\"outline\">\r\n                                <mat-label>Address</mat-label>\r\n                                <input matInput formControlName=\"address\" placeholder=\"Enter your address\">\r\n                                <mat-error *ngIf=\"profileForm.get('address')?.errors?.['maxlength']\">\r\n                                    Address cannot exceed 100 characters\r\n                                </mat-error>\r\n                            </mat-form-field>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Job seeker specific fields -->\r\n                    <div *ngIf=\"isJobSeeker()\" class=\"form-section\">\r\n                        <h2>Job Seeker Information</h2>\r\n\r\n                        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                            <mat-label>Resume URL</mat-label>\r\n                            <input matInput formControlName=\"resumeUrl\" placeholder=\"Enter URL to your resume\">\r\n                            <mat-error *ngIf=\"profileForm.get('resumeUrl')?.errors?.['maxlength']\">\r\n                                Resume URL cannot exceed 255 characters\r\n                            </mat-error>\r\n                        </mat-form-field>\r\n                    </div>\r\n\r\n                    <!-- Employer specific fields -->\r\n                    <div *ngIf=\"isEmployer()\" class=\"form-section\">\r\n                        <h2>Company Information</h2>\r\n\r\n                        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                            <mat-label>Company Name</mat-label>\r\n                            <input matInput formControlName=\"companyName\" placeholder=\"Enter your company name\">\r\n                            <mat-error *ngIf=\"profileForm.get('companyName')?.errors?.['maxlength']\">\r\n                                Company name cannot exceed 100 characters\r\n                            </mat-error>\r\n                        </mat-form-field>\r\n\r\n                        <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                            <mat-label>Company Description</mat-label>\r\n                            <textarea matInput formControlName=\"companyDescription\"\r\n                                placeholder=\"Enter a description of your company\" rows=\"4\"></textarea>\r\n                            <mat-error *ngIf=\"profileForm.get('companyDescription')?.errors?.['maxlength']\">\r\n                                Company description cannot exceed 1000 characters\r\n                            </mat-error>\r\n                        </mat-form-field>\r\n                    </div>\r\n\r\n                    <div class=\"form-actions\">\r\n                        <button mat-raised-button color=\"primary\" type=\"submit\"\r\n                            [disabled]=\"profileForm.invalid || saving\">\r\n                            <mat-spinner diameter=\"20\" *ngIf=\"saving\"></mat-spinner>\r\n                            <span *ngIf=\"!saving\">Save Profile</span>\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Account Information Card -->\r\n        <mat-card class=\"account-card\">\r\n            <mat-card-header>\r\n                <mat-card-title>Account Information</mat-card-title>\r\n            </mat-card-header>\r\n            <mat-card-content>\r\n                <div class=\"account-info\">\r\n                    <p><strong>Email:</strong> {{ user?.email }}</p>\r\n                    <p><strong>Role:</strong> {{ user?.role === 'job_seeker' ? 'Job Seeker' : 'Employer' }}</p>\r\n                </div>\r\n            </mat-card-content>\r\n        </mat-card>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;ICM/DC,EAAA,CAAAC,cAAA,aAA+C;IAC3CD,EAAA,CAAAE,SAAA,qBAAyC;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcsBH,EAAA,CAAAC,cAAA,gBAAuE;IACnED,EAAA,CAAAI,MAAA,+CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAsE;IAClED,EAAA,CAAAI,MAAA,8CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAmE;IAC/DD,EAAA,CAAAI,MAAA,iDACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAqE;IACjED,EAAA,CAAAI,MAAA,6CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAYhBH,EAAA,CAAAC,cAAA,gBAAuE;IACnED,EAAA,CAAAI,MAAA,gDACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IARpBH,EAAA,CAAAC,cAAA,aAAgD;IACxCD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE/BH,EAAA,CAAAC,cAAA,yBAAwD;IACzCD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAE,SAAA,gBAAmF;IACnFF,EAAA,CAAAK,UAAA,IAAAC,kDAAA,wBAEY;IAChBN,EAAA,CAAAG,YAAA,EAAiB;;;;;IAHDH,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,WAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,cAAyD;;;;;IAarEb,EAAA,CAAAC,cAAA,gBAAyE;IACrED,EAAA,CAAAI,MAAA,kDACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAOZH,EAAA,CAAAC,cAAA,gBAAgF;IAC5ED,EAAA,CAAAI,MAAA,0DACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAY;;;;;IAjBpBH,EAAA,CAAAC,cAAA,aAA+C;IACvCD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EAAA,CAAAC,cAAA,yBAAwD;IACzCD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAE,SAAA,gBAAoF;IACpFF,EAAA,CAAAK,UAAA,IAAAS,kDAAA,wBAEY;IAChBd,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,yBAAwD;IACzCD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAE,SAAA,oBAC0E;IAC1EF,EAAA,CAAAK,UAAA,KAAAU,mDAAA,wBAEY;IAChBf,EAAA,CAAAG,YAAA,EAAiB;;;;;;IAZDH,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAO,MAAA,CAAAL,WAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,cAA2D;IAS3Db,EAAA,CAAAO,SAAA,GAAkE;IAAlEP,EAAA,CAAAQ,UAAA,UAAAS,OAAA,GAAAD,MAAA,CAAAL,WAAA,CAAAC,GAAA,yCAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,cAAkE;;;;;IAS9Eb,EAAA,CAAAE,SAAA,sBAAwD;;;;;IACxDF,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnFjEH,EAAA,CAAAC,cAAA,aAA8C;IAGFD,EAAA,CAAAkB,UAAA,sBAAAC,yDAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACnDzB,EAAA,CAAAC,cAAA,aAA0B;IAClBD,EAAA,CAAAI,MAAA,2BAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EAAA,CAAAC,cAAA,cAAsB;IAEHD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAE,SAAA,iBAAgF;IAChFF,EAAA,CAAAK,UAAA,KAAAqB,4CAAA,wBAEY;IAChB1B,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,0BAAqC;IACtBD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAE,SAAA,iBAA8E;IAC9EF,EAAA,CAAAK,UAAA,KAAAsB,4CAAA,wBAEY;IAChB3B,EAAA,CAAAG,YAAA,EAAiB;IAGrBH,EAAA,CAAAC,cAAA,eAAsB;IAEHD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAE,SAAA,iBAA8E;IAC9EF,EAAA,CAAAK,UAAA,KAAAuB,4CAAA,wBAEY;IAChB5B,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,0BAAqC;IACtBD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAE,SAAA,iBAA2E;IAC3EF,EAAA,CAAAK,UAAA,KAAAwB,4CAAA,wBAEY;IAChB7B,EAAA,CAAAG,YAAA,EAAiB;IAKzBH,EAAA,CAAAK,UAAA,KAAAyB,sCAAA,kBAUM;IAGN9B,EAAA,CAAAK,UAAA,KAAA0B,sCAAA,mBAmBM;IAEN/B,EAAA,CAAAC,cAAA,eAA0B;IAGlBD,EAAA,CAAAK,UAAA,KAAA2B,8CAAA,0BAAwD;IACxDhC,EAAA,CAAAK,UAAA,KAAA4B,uCAAA,mBAAyC;IAC7CjC,EAAA,CAAAG,YAAA,EAAS;IAOzBH,EAAA,CAAAC,cAAA,oBAA+B;IAEPD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAiB;IAExDH,EAAA,CAAAC,cAAA,wBAAkB;IAECD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,IAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAI,MAAA,IAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;;;IA/FzFH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,cAAA0B,MAAA,CAAAvB,WAAA,CAAyB;IAQHX,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAQ,UAAA,UAAAS,OAAA,GAAAiB,MAAA,CAAAvB,WAAA,CAAAC,GAAA,gCAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,cAAyD;IAQzDb,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAQ,UAAA,UAAA2B,OAAA,GAAAD,MAAA,CAAAvB,WAAA,CAAAC,GAAA,+BAAAuB,OAAA,CAAAtB,MAAA,kBAAAsB,OAAA,CAAAtB,MAAA,cAAwD;IAUxDb,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAA4B,OAAA,GAAAF,MAAA,CAAAvB,WAAA,CAAAC,GAAA,4BAAAwB,OAAA,CAAAvB,MAAA,kBAAAuB,OAAA,CAAAvB,MAAA,cAAqD;IAQrDb,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAQ,UAAA,UAAA6B,OAAA,GAAAH,MAAA,CAAAvB,WAAA,CAAAC,GAAA,8BAAAyB,OAAA,CAAAxB,MAAA,kBAAAwB,OAAA,CAAAxB,MAAA,cAAuD;IAQzEb,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,UAAA,SAAA0B,MAAA,CAAAI,WAAA,GAAmB;IAanBtC,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,SAAA0B,MAAA,CAAAK,UAAA,GAAkB;IAuBhBvC,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAQ,UAAA,aAAA0B,MAAA,CAAAvB,WAAA,CAAA6B,OAAA,IAAAN,MAAA,CAAAO,MAAA,CAA0C;IACdzC,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAQ,UAAA,SAAA0B,MAAA,CAAAO,MAAA,CAAY;IACjCzC,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAQ,UAAA,UAAA0B,MAAA,CAAAO,MAAA,CAAa;IAcDzC,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAA0C,kBAAA,MAAAR,MAAA,CAAAS,IAAA,kBAAAT,MAAA,CAAAS,IAAA,CAAAC,KAAA,KAAiB;IAClB5C,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAA0C,kBAAA,OAAAR,MAAA,CAAAS,IAAA,kBAAAT,MAAA,CAAAS,IAAA,CAAAE,IAAA,mDAA6D;;;ADlG3G,OAAM,MAAOC,gBAAgB;EAO3BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,cAA8B,EAC9BC,QAAqB;IAHrB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAR,IAAI,GAAgB,IAAI;IACxB,KAAAS,OAAO,GAAmB,IAAI;IAC9B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAZ,MAAM,GAAG,KAAK;EAOV;EAEJa,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAD,QAAQA,CAAA;IACN,IAAI,CAAC5C,WAAW,GAAG,IAAI,CAACqC,WAAW,CAACS,KAAK,CAAC;MACxC;MACAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC3D,UAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3CC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1CE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9D,UAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACvCG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAAC4D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAE1C;MACAI,SAAS,EAAE,CAAC,EAAE,EAAE,CAAChE,UAAU,CAAC4D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAE5C;MACAK,WAAW,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAAC4D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9CM,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAAC4D,SAAS,CAAC,IAAI,CAAC,CAAC;KACtD,CAAC;EACJ;EAEAH,eAAeA,CAAA;IACb,IAAI,CAACH,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMa,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC7C,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACvB,IAAI,GAAG0B,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;;IAGlC;IACA,IAAI,CAAChB,cAAc,CAACqB,UAAU,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACtB,OAAO,GAAGsB,QAAQ,CAACC,IAAI,CAACvB,OAAO;QACpC,IAAI,CAACwB,yBAAyB,EAAE;QAChC,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UACxB,IAAI,CAAC3B,QAAQ,CAAC4B,IAAI,CAACF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,uBAAuB,EAAE,OAAO,EAAE;YAC3EC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;QAEJ,IAAI,CAAC7B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAuB,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACxB,OAAO,EAAE;IAEnB,IAAI,CAACzC,WAAW,CAACwE,UAAU,CAAC;MAC1BzB,SAAS,EAAE,IAAI,CAACN,OAAO,CAACM,SAAS,IAAI,EAAE;MACvCE,QAAQ,EAAE,IAAI,CAACR,OAAO,CAACQ,QAAQ,IAAI,EAAE;MACrCC,KAAK,EAAE,IAAI,CAACT,OAAO,CAACS,KAAK,IAAI,EAAE;MAC/BC,OAAO,EAAE,IAAI,CAACV,OAAO,CAACU,OAAO,IAAI,EAAE;MACnCC,SAAS,EAAE,IAAI,CAACX,OAAO,CAACW,SAAS,IAAI,EAAE;MACvCC,WAAW,EAAE,IAAI,CAACZ,OAAO,CAACY,WAAW,IAAI,EAAE;MAC3CC,kBAAkB,EAAE,IAAI,CAACb,OAAO,CAACa,kBAAkB,IAAI;KACxD,CAAC;EACJ;EAEAxC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACd,WAAW,CAAC6B,OAAO,EAAE;MAC5B;;IAGF,IAAI,CAACC,MAAM,GAAG,IAAI;IAElB,MAAM2C,WAAW,GAAyB;MACxC1B,SAAS,EAAE,IAAI,CAAC/C,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEyE,KAAK;MACnDzB,QAAQ,EAAE,IAAI,CAACjD,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEyE,KAAK;MACjDxB,KAAK,EAAE,IAAI,CAAClD,WAAW,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEyE,KAAK;MAC3CvB,OAAO,EAAE,IAAI,CAACnD,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEyE;KAC3C;IAED;IACA,IAAI,IAAI,CAAC/C,WAAW,EAAE,EAAE;MACtB8C,WAAW,CAACrB,SAAS,GAAG,IAAI,CAACpD,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEyE,KAAK;KACjE,MAAM,IAAI,IAAI,CAAC9C,UAAU,EAAE,EAAE;MAC5B6C,WAAW,CAACpB,WAAW,GAAG,IAAI,CAACrD,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEyE,KAAK;MACpED,WAAW,CAACnB,kBAAkB,GAAG,IAAI,CAACtD,WAAW,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEyE,KAAK;;IAGpF,IAAI,CAACnC,cAAc,CAACoC,aAAa,CAACF,WAAW,CAAC,CAACZ,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACtB,OAAO,GAAGsB,QAAQ,CAACC,IAAI,CAACvB,OAAO;QACpC,IAAI,CAACD,QAAQ,CAAC4B,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAC3DE,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAACzC,MAAM,GAAG,KAAK;MACrB,CAAC;MACDoC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1B,QAAQ,CAAC4B,IAAI,CAACF,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,wBAAwB,EAAE,OAAO,EAAE;UAC5EC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACzC,MAAM,GAAG,KAAK;MACrB;KACD,CAAC;EACJ;EAEAH,WAAWA,CAAA;IACT,OAAO,IAAI,CAACK,IAAI,EAAEE,IAAI,KAAK,YAAY;EACzC;EAEAN,UAAUA,CAAA;IACR,OAAO,IAAI,CAACI,IAAI,EAAEE,IAAI,KAAK,UAAU;EACvC;;;uBA9HWC,gBAAgB,EAAA9C,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3F,EAAA,CAAAuF,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7F,EAAA,CAAAuF,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBjD,gBAAgB;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ7BtG,EAAA,CAAAC,cAAA,aAA+B;UAEnBD,EAAA,CAAAI,MAAA,iBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAI,MAAA,wDAAiD;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI5DH,EAAA,CAAAK,UAAA,IAAAmG,+BAAA,iBAEM;UAGNxG,EAAA,CAAAK,UAAA,IAAAoG,+BAAA,mBAsGM;UACVzG,EAAA,CAAAG,YAAA,EAAM;;;UA5GIH,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAQ,UAAA,SAAA+F,GAAA,CAAAlD,OAAA,CAAa;UAKbrD,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAQ,UAAA,UAAA+F,GAAA,CAAAlD,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}