{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nfunction HeaderComponent_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵtext(2, \"My Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 10);\n    i0.ɵɵtext(4, \"Post Job\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HeaderComponent_ng_container_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 11);\n    i0.ɵɵtext(2, \"My Applications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HeaderComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, HeaderComponent_ng_container_6_ng_container_1_Template, 5, 0, \"ng-container\", 6);\n    i0.ɵɵtemplate(2, HeaderComponent_ng_container_6_ng_container_2_Template, 3, 0, \"ng-container\", 6);\n    i0.ɵɵelementStart(3, \"a\", 7);\n    i0.ɵɵtext(4, \"Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵtext(6, \"Logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEmployer());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isJobSeeker());\n  }\n}\nfunction HeaderComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 13);\n    i0.ɵɵtext(3, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HeaderComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.isAuthenticated$ = this.authService.isAuthenticated;\n    this.currentUser$ = this.authService.currentUser;\n  }\n  logout() {\n    this.authService.logout();\n  }\n  isEmployer() {\n    return this.authService.isEmployer();\n  }\n  isJobSeeker() {\n    return this.authService.isJobSeeker();\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      decls: 10,\n      vars: 4,\n      consts: [[\"color\", \"primary\"], [\"mat-button\", \"\", \"routerLink\", \"/\", 1, \"logo\"], [1, \"spacer\"], [\"mat-button\", \"\", \"routerLink\", \"/jobs\", \"routerLinkActive\", \"active\"], [4, \"ngIf\", \"ngIfElse\"], [\"unauthenticatedLinks\", \"\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"routerLink\", \"/profile\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-button\", \"\", \"routerLink\", \"/my-jobs\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/create-job\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/my-applications\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/login\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/register\", \"routerLinkActive\", \"active\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 0)(1, \"a\", 1);\n          i0.ɵɵtext(2, \"FindIt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"span\", 2);\n          i0.ɵɵelementStart(4, \"a\", 3);\n          i0.ɵɵtext(5, \"Browse Jobs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, HeaderComponent_ng_container_6_Template, 7, 2, \"ng-container\", 4);\n          i0.ɵɵpipe(7, \"async\");\n          i0.ɵɵtemplate(8, HeaderComponent_ng_template_8_Template, 4, 0, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(9);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(7, 2, ctx.isAuthenticated$))(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatToolbar, i5.MatAnchor, i5.MatButton, i3.AsyncPipe],\n      styles: [\".spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  text-decoration: none;\\n  color: white;\\n}\\n\\n.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9oZWFkZXIvaGVhZGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtBQUNGOztBQUVBO0VBQ0UsaUJBQUE7RUFDQSxpQkFBQTtFQUNBLHFCQUFBO0VBQ0EsWUFBQTtBQUNGOztBQUVBO0VBQ0UsMENBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5zcGFjZXIge1xyXG4gIGZsZXg6IDEgMSBhdXRvO1xyXG59XHJcblxyXG4ubG9nbyB7XHJcbiAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLmFjdGl2ZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵtemplate", "HeaderComponent_ng_container_6_ng_container_1_Template", "HeaderComponent_ng_container_6_ng_container_2_Template", "ɵɵlistener", "HeaderComponent_ng_container_6_Template_button_click_5_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "isEmployer", "isJobSeeker", "HeaderComponent", "constructor", "authService", "router", "ngOnInit", "isAuthenticated$", "isAuthenticated", "currentUser$", "currentUser", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "ɵɵelement", "HeaderComponent_ng_container_6_Template", "HeaderComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵpipeBind1", "_r1"], "sources": ["D:\\findit\\client\\src\\app\\components\\header\\header.component.ts", "D:\\findit\\client\\src\\app\\components\\header\\header.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { User } from '../../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-header',\r\n  templateUrl: './header.component.html',\r\n  styleUrls: ['./header.component.scss']\r\n})\r\nexport class HeaderComponent implements OnInit {\r\n  isAuthenticated$!: Observable<boolean>;\r\n  currentUser$!: Observable<User | null>;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.isAuthenticated$ = this.authService.isAuthenticated;\r\n    this.currentUser$ = this.authService.currentUser;\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout();\r\n  }\r\n\r\n  isEmployer(): boolean {\r\n    return this.authService.isEmployer();\r\n  }\r\n\r\n  isJobSeeker(): boolean {\r\n    return this.authService.isJobSeeker();\r\n  }\r\n}\r\n", "<mat-toolbar color=\"primary\">\r\n    <a mat-button routerLink=\"/\" class=\"logo\">FindIt</a>\r\n    <span class=\"spacer\"></span>\r\n\r\n    <!-- Public links -->\r\n    <a mat-button routerLink=\"/jobs\" routerLinkActive=\"active\">Browse Jobs</a>\r\n\r\n    <!-- Links for authenticated users -->\r\n    <ng-container *ngIf=\"isAuthenticated$ | async; else unauthenticatedLinks\">\r\n        <!-- Employer links -->\r\n        <ng-container *ngIf=\"isEmployer()\">\r\n            <a mat-button routerLink=\"/my-jobs\" routerLinkActive=\"active\">My Jobs</a>\r\n            <a mat-button routerLink=\"/create-job\" routerLinkActive=\"active\">Post Job</a>\r\n        </ng-container>\r\n\r\n        <!-- Job seeker links -->\r\n        <ng-container *ngIf=\"isJobSeeker()\">\r\n            <a mat-button routerLink=\"/my-applications\" routerLinkActive=\"active\">My Applications</a>\r\n        </ng-container>\r\n\r\n        <!-- Common authenticated links -->\r\n        <a mat-button routerLink=\"/profile\" routerLinkActive=\"active\">Profile</a>\r\n        <button mat-button (click)=\"logout()\">Logout</button>\r\n    </ng-container>\r\n\r\n    <!-- Links for unauthenticated users -->\r\n    <ng-template #unauthenticatedLinks>\r\n        <a mat-button routerLink=\"/login\" routerLinkActive=\"active\">Login</a>\r\n        <a mat-button routerLink=\"/register\" routerLinkActive=\"active\">Register</a>\r\n    </ng-template>\r\n</mat-toolbar>"], "mappings": ";;;;;;;;ICUQA,EAAA,CAAAC,uBAAA,GAAmC;IAC/BD,EAAA,CAAAE,cAAA,WAA8D;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzEJ,EAAA,CAAAE,cAAA,YAAiE;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACjFJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IAGfL,EAAA,CAAAC,uBAAA,GAAoC;IAChCD,EAAA,CAAAE,cAAA,YAAsE;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC7FJ,EAAA,CAAAK,qBAAA,EAAe;;;;;;IAVnBL,EAAA,CAAAC,uBAAA,GAA0E;IAEtED,EAAA,CAAAM,UAAA,IAAAC,sDAAA,0BAGe;IAGfP,EAAA,CAAAM,UAAA,IAAAE,sDAAA,0BAEe;IAGfR,EAAA,CAAAE,cAAA,WAA8D;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzEJ,EAAA,CAAAE,cAAA,gBAAsC;IAAnBF,EAAA,CAAAS,UAAA,mBAAAC,gEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAAChB,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACzDJ,EAAA,CAAAK,qBAAA,EAAe;;;;IAbIL,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,UAAA,SAAAC,MAAA,CAAAC,UAAA,GAAkB;IAMlBpB,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,SAAAC,MAAA,CAAAE,WAAA,GAAmB;;;;;IAWlCrB,EAAA,CAAAE,cAAA,YAA4D;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrEJ,EAAA,CAAAE,cAAA,YAA+D;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADjBnF,OAAM,MAAOkB,eAAe;EAI1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACZ;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACH,WAAW,CAACI,eAAe;IACxD,IAAI,CAACC,YAAY,GAAG,IAAI,CAACL,WAAW,CAACM,WAAW;EAClD;EAEAd,MAAMA,CAAA;IACJ,IAAI,CAACQ,WAAW,CAACR,MAAM,EAAE;EAC3B;EAEAI,UAAUA,CAAA;IACR,OAAO,IAAI,CAACI,WAAW,CAACJ,UAAU,EAAE;EACtC;EAEAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACG,WAAW,CAACH,WAAW,EAAE;EACvC;;;uBAxBWC,eAAe,EAAAtB,EAAA,CAAA+B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjC,EAAA,CAAA+B,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfb,eAAe;MAAAc,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5B1C,EAAA,CAAAE,cAAA,qBAA6B;UACiBF,EAAA,CAAAG,MAAA,aAAM;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpDJ,EAAA,CAAA4C,SAAA,cAA4B;UAG5B5C,EAAA,CAAAE,cAAA,WAA2D;UAAAF,EAAA,CAAAG,MAAA,kBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG1EJ,EAAA,CAAAM,UAAA,IAAAuC,uCAAA,0BAee;;UAGf7C,EAAA,CAAAM,UAAA,IAAAwC,sCAAA,gCAAA9C,EAAA,CAAA+C,sBAAA,CAGc;UAClB/C,EAAA,CAAAI,YAAA,EAAc;;;;UAtBKJ,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,SAAAlB,EAAA,CAAAgD,WAAA,OAAAL,GAAA,CAAAhB,gBAAA,EAAgC,aAAAsB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}