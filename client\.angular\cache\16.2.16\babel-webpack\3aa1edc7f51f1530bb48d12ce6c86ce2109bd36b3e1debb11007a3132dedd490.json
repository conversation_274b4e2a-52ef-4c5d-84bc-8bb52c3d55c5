{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nfunction JobFormComponent_mat_error_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job title cannot exceed 100 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Location is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Location cannot exceed 100 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r13.label, \" \");\n  }\n}\nfunction JobFormComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job type is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Salary range cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job description is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job description cannot exceed 5000 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Requirements cannot exceed 2000 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_form_field_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 1)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 16)(4, \"option\", 17);\n    i0.ɵɵtext(5, \"Active\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 18);\n    i0.ɵɵtext(7, \"Closed\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction JobFormComponent_mat_spinner_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 19);\n  }\n}\nfunction JobFormComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((ctx_r12.job == null ? null : ctx_r12.job.id) ? \"Update Job\" : \"Create Job\");\n  }\n}\nexport class JobFormComponent {\n  constructor(formBuilder) {\n    this.formBuilder = formBuilder;\n    this.job = null;\n    this.loading = false;\n    this.formSubmit = new EventEmitter();\n    this.formCancel = new EventEmitter();\n    this.jobTypes = [{\n      value: 'full-time',\n      label: 'Full Time'\n    }, {\n      value: 'part-time',\n      label: 'Part Time'\n    }, {\n      value: 'contract',\n      label: 'Contract'\n    }, {\n      value: 'internship',\n      label: 'Internship'\n    }, {\n      value: 'remote',\n      label: 'Remote'\n    }];\n  }\n  ngOnInit() {\n    this.initForm();\n    console.log('ngOnInit job:', this.job);\n  }\n  ngOnChanges(changes) {\n    // If the job input changes and the form is already initialized\n    if (changes['job'] && this.jobForm) {\n      console.log('ngOnChanges job:', this.job);\n      this.updateForm();\n    }\n  }\n  initForm() {\n    this.jobForm = this.formBuilder.group({\n      title: [this.job?.title || '', [Validators.required, Validators.maxLength(100)]],\n      description: [this.job?.description || '', [Validators.required, Validators.maxLength(5000)]],\n      location: [this.job?.location || '', [Validators.required, Validators.maxLength(100)]],\n      salaryRange: [this.job?.salaryRange || '', Validators.maxLength(50)],\n      jobType: [this.job?.jobType || 'full-time', Validators.required],\n      requirements: [this.job?.requirements || '', Validators.maxLength(2000)],\n      status: [this.job?.status || 'active']\n    });\n  }\n  updateForm() {\n    if (this.job) {\n      this.jobForm.patchValue({\n        title: this.job.title || '',\n        description: this.job.description || '',\n        location: this.job.location || '',\n        salaryRange: this.job.salaryRange || '',\n        jobType: this.job.jobType || 'full-time',\n        requirements: this.job.requirements || '',\n        status: this.job.status || 'active'\n      });\n    }\n  }\n  onSubmit() {\n    if (this.jobForm.invalid) {\n      // Mark all fields as touched to trigger validation messages\n      Object.keys(this.jobForm.controls).forEach(key => {\n        const control = this.jobForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n    const jobRequest = this.jobForm.value;\n    this.formSubmit.emit(jobRequest);\n  }\n  onCancel() {\n    this.formCancel.emit();\n  }\n  // Convenience getter for easy access to form fields\n  get f() {\n    return this.jobForm.controls;\n  }\n  static {\n    this.ɵfac = function JobFormComponent_Factory(t) {\n      return new (t || JobFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: JobFormComponent,\n      selectors: [[\"app-job-form\"]],\n      inputs: {\n        job: \"job\",\n        loading: \"loading\"\n      },\n      outputs: {\n        formSubmit: \"formSubmit\",\n        formCancel: \"formCancel\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 42,\n      vars: 16,\n      consts: [[1, \"job-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"title\", \"placeholder\", \"Enter job title\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"location\", \"placeholder\", \"Enter job location (city, state, remote)\", \"required\", \"\"], [\"matNativeControl\", \"\", \"formControlName\", \"jobType\", \"required\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"salaryRange\", \"placeholder\", \"e.g. $50,000 - $70,000\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"placeholder\", \"Enter detailed job description\", \"rows\", \"6\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"requirements\", \"placeholder\", \"Enter job requirements\", \"rows\", \"4\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [3, \"value\"], [\"matNativeControl\", \"\", \"formControlName\", \"status\"], [\"value\", \"active\"], [\"value\", \"closed\"], [\"diameter\", \"20\"]],\n      template: function JobFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function JobFormComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(1, \"mat-form-field\", 1)(2, \"mat-label\");\n          i0.ɵɵtext(3, \"Job Title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"input\", 2);\n          i0.ɵɵtemplate(5, JobFormComponent_mat_error_5_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵtemplate(6, JobFormComponent_mat_error_6_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-form-field\", 1)(8, \"mat-label\");\n          i0.ɵɵtext(9, \"Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 4);\n          i0.ɵɵtemplate(11, JobFormComponent_mat_error_11_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵtemplate(12, JobFormComponent_mat_error_12_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-form-field\", 1)(14, \"mat-label\");\n          i0.ɵɵtext(15, \"Job Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"select\", 5);\n          i0.ɵɵtemplate(17, JobFormComponent_option_17_Template, 2, 2, \"option\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, JobFormComponent_mat_error_18_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 1)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Salary Range (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 7);\n          i0.ɵɵtemplate(23, JobFormComponent_mat_error_23_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 1)(25, \"mat-label\");\n          i0.ɵɵtext(26, \"Job Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"textarea\", 8);\n          i0.ɵɵtemplate(28, JobFormComponent_mat_error_28_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵtemplate(29, JobFormComponent_mat_error_29_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 1)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Requirements (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"textarea\", 9);\n          i0.ɵɵtemplate(34, JobFormComponent_mat_error_34_Template, 2, 0, \"mat-error\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, JobFormComponent_mat_form_field_35_Template, 8, 0, \"mat-form-field\", 10);\n          i0.ɵɵelementStart(36, \"div\", 11)(37, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function JobFormComponent_Template_button_click_37_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(38, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 13);\n          i0.ɵɵtemplate(40, JobFormComponent_mat_spinner_40_Template, 1, 0, \"mat-spinner\", 14);\n          i0.ɵɵtemplate(41, JobFormComponent_span_41_Template, 2, 1, \"span\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.jobForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"title\"].errors == null ? null : ctx.f[\"title\"].errors[\"required\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"title\"].errors == null ? null : ctx.f[\"title\"].errors[\"maxlength\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"location\"].errors == null ? null : ctx.f[\"location\"].errors[\"required\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"location\"].errors == null ? null : ctx.f[\"location\"].errors[\"maxlength\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.jobTypes);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"jobType\"].errors == null ? null : ctx.f[\"jobType\"].errors[\"required\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"salaryRange\"].errors == null ? null : ctx.f[\"salaryRange\"].errors[\"maxlength\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"description\"].errors == null ? null : ctx.f[\"description\"].errors[\"required\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"description\"].errors == null ? null : ctx.f[\"description\"].errors[\"maxlength\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.f[\"requirements\"].errors == null ? null : ctx.f[\"requirements\"].errors[\"maxlength\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.job == null ? null : ctx.job.id);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i4.MatInput, i5.MatFormField, i5.MatLabel, i5.MatError, i6.MatProgressSpinner],\n      styles: [\".job-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\nmat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n  }\\n  .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9qb2ItZm9ybS9qb2ItZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSx5QkFBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UscUJBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0U7SUFDRSw4QkFBQTtFQUNGO0VBQ0U7SUFDRSxXQUFBO0lBQ0Esa0JBQUE7RUFDSjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmpvYi1mb3JtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLmZ1bGwtd2lkdGgge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcbn1cclxuXHJcbi5mb3JtLWFjdGlvbnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcclxuICBnYXA6IDE2cHg7XHJcbiAgbWFyZ2luLXRvcDogMTZweDtcclxufVxyXG5cclxubWF0LXNwaW5uZXIge1xyXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICBtYXJnaW4tcmlnaHQ6IDhweDtcclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmZvcm0tYWN0aW9ucyB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uLXJldmVyc2U7XHJcblxyXG4gICAgYnV0dG9uIHtcclxuICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r13", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r12", "job", "id", "JobFormComponent", "constructor", "formBuilder", "loading", "formSubmit", "formCancel", "jobTypes", "ngOnInit", "initForm", "console", "log", "ngOnChanges", "changes", "jobForm", "updateForm", "group", "title", "required", "max<PERSON><PERSON><PERSON>", "description", "location", "salaryRange", "jobType", "requirements", "status", "patchValue", "onSubmit", "invalid", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "jobRequest", "emit", "onCancel", "f", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "JobFormComponent_Template", "rf", "ctx", "ɵɵlistener", "JobFormComponent_Template_form_ngSubmit_0_listener", "ɵɵtemplate", "JobFormComponent_mat_error_5_Template", "JobFormComponent_mat_error_6_Template", "JobFormComponent_mat_error_11_Template", "JobFormComponent_mat_error_12_Template", "JobFormComponent_option_17_Template", "JobFormComponent_mat_error_18_Template", "JobFormComponent_mat_error_23_Template", "JobFormComponent_mat_error_28_Template", "JobFormComponent_mat_error_29_Template", "JobFormComponent_mat_error_34_Template", "JobFormComponent_mat_form_field_35_Template", "JobFormComponent_Template_button_click_37_listener", "JobFormComponent_mat_spinner_40_Template", "JobFormComponent_span_41_Template", "errors"], "sources": ["D:\\findit\\client\\src\\app\\components\\job-form\\job-form.component.ts", "D:\\findit\\client\\src\\app\\components\\job-form\\job-form.component.html"], "sourcesContent": ["import { Component, OnInit, OnChanges, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Job, JobRequest } from '../../models/job.model';\r\n\r\n@Component({\r\n  selector: 'app-job-form',\r\n  templateUrl: './job-form.component.html',\r\n  styleUrls: ['./job-form.component.scss']\r\n})\r\nexport class JobFormComponent implements OnInit, OnChanges {\r\n  @Input() job: Job | null = null;\r\n  @Input() loading = false;\r\n  @Output() formSubmit = new EventEmitter<JobRequest>();\r\n  @Output() formCancel = new EventEmitter<void>();\r\n\r\n  jobForm!: FormGroup;\r\n\r\n  jobTypes = [\r\n    { value: 'full-time', label: 'Full Time' },\r\n    { value: 'part-time', label: 'Part Time' },\r\n    { value: 'contract', label: 'Contract' },\r\n    { value: 'internship', label: 'Internship' },\r\n    { value: 'remote', label: 'Remote' }\r\n  ];\r\n\r\n  constructor(private formBuilder: FormBuilder) { }\r\n\r\n  ngOnInit(): void {\r\n    this.initForm();\r\n    console.log('ngOnInit job:', this.job);\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    // If the job input changes and the form is already initialized\r\n    if (changes['job'] && this.jobForm) {\r\n      console.log('ngOnChanges job:', this.job);\r\n      this.updateForm();\r\n    }\r\n  }\r\n\r\n  initForm(): void {\r\n    this.jobForm = this.formBuilder.group({\r\n      title: [this.job?.title || '', [Validators.required, Validators.maxLength(100)]],\r\n      description: [this.job?.description || '', [Validators.required, Validators.maxLength(5000)]],\r\n      location: [this.job?.location || '', [Validators.required, Validators.maxLength(100)]],\r\n      salaryRange: [this.job?.salaryRange || '', Validators.maxLength(50)],\r\n      jobType: [this.job?.jobType || 'full-time', Validators.required],\r\n      requirements: [this.job?.requirements || '', Validators.maxLength(2000)],\r\n      status: [this.job?.status || 'active']\r\n    });\r\n  }\r\n\r\n  updateForm(): void {\r\n    if (this.job) {\r\n      this.jobForm.patchValue({\r\n        title: this.job.title || '',\r\n        description: this.job.description || '',\r\n        location: this.job.location || '',\r\n        salaryRange: this.job.salaryRange || '',\r\n        jobType: this.job.jobType || 'full-time',\r\n        requirements: this.job.requirements || '',\r\n        status: this.job.status || 'active'\r\n      });\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.jobForm.invalid) {\r\n      // Mark all fields as touched to trigger validation messages\r\n      Object.keys(this.jobForm.controls).forEach(key => {\r\n        const control = this.jobForm.get(key);\r\n        control?.markAsTouched();\r\n      });\r\n      return;\r\n    }\r\n\r\n    const jobRequest: JobRequest = this.jobForm.value;\r\n    this.formSubmit.emit(jobRequest);\r\n  }\r\n\r\n  onCancel(): void {\r\n    this.formCancel.emit();\r\n  }\r\n\r\n  // Convenience getter for easy access to form fields\r\n  get f() { return this.jobForm.controls; }\r\n}\r\n", "<form [formGroup]=\"jobForm\" (ngSubmit)=\"onSubmit()\" class=\"job-form\">\r\n    <!-- Title -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Job Title</mat-label>\r\n        <input matInput formControlName=\"title\" placeholder=\"Enter job title\" required>\r\n        <mat-error *ngIf=\"f['title'].errors?.['required']\">Job title is required</mat-error>\r\n        <mat-error *ngIf=\"f['title'].errors?.['maxlength']\">Job title cannot exceed 100 characters</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Location -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Location</mat-label>\r\n        <input matInput formControlName=\"location\" placeholder=\"Enter job location (city, state, remote)\" required>\r\n        <mat-error *ngIf=\"f['location'].errors?.['required']\">Location is required</mat-error>\r\n        <mat-error *ngIf=\"f['location'].errors?.['maxlength']\">Location cannot exceed 100 characters</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Job Type -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Job Type</mat-label>\r\n        <select matNativeControl formControlName=\"jobType\" required>\r\n            <option *ngFor=\"let type of jobTypes\" [value]=\"type.value\">\r\n                {{ type.label }}\r\n            </option>\r\n        </select>\r\n        <mat-error *ngIf=\"f['jobType'].errors?.['required']\">Job type is required</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Salary Range -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Salary Range (Optional)</mat-label>\r\n        <input matInput formControlName=\"salaryRange\" placeholder=\"e.g. $50,000 - $70,000\">\r\n        <mat-error *ngIf=\"f['salaryRange'].errors?.['maxlength']\">Salary range cannot exceed 50 characters</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Description -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Job Description</mat-label>\r\n        <textarea matInput formControlName=\"description\" placeholder=\"Enter detailed job description\" rows=\"6\"\r\n            required></textarea>\r\n        <mat-error *ngIf=\"f['description'].errors?.['required']\">Job description is required</mat-error>\r\n        <mat-error *ngIf=\"f['description'].errors?.['maxlength']\">Job description cannot exceed 5000\r\n            characters</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Requirements -->\r\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Requirements (Optional)</mat-label>\r\n        <textarea matInput formControlName=\"requirements\" placeholder=\"Enter job requirements\" rows=\"4\"></textarea>\r\n        <mat-error *ngIf=\"f['requirements'].errors?.['maxlength']\">Requirements cannot exceed 2000\r\n            characters</mat-error>\r\n    </mat-form-field>\r\n\r\n    <!-- Status (only for editing) -->\r\n    <mat-form-field *ngIf=\"job?.id\" appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>Status</mat-label>\r\n        <select matNativeControl formControlName=\"status\">\r\n            <option value=\"active\">Active</option>\r\n            <option value=\"closed\">Closed</option>\r\n        </select>\r\n    </mat-form-field>\r\n\r\n    <!-- Form Actions -->\r\n    <div class=\"form-actions\">\r\n        <button mat-stroked-button type=\"button\" (click)=\"onCancel()\" [disabled]=\"loading\">Cancel</button>\r\n        <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading\">\r\n            <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\r\n            <span *ngIf=\"!loading\">{{ job?.id ? 'Update Job' : 'Create Job' }}</span>\r\n        </button>\r\n    </div>\r\n</form>"], "mappings": "AAAA,SAAsDA,YAAY,QAAuB,eAAe;AACxG,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICI3DC,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACpFH,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOtGH,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACtFH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOpGH,EAAA,CAAAC,cAAA,iBAA2D;IACvDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,KAAA,CAAoB;IACtDN,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,QAAA,CAAAI,KAAA,MACJ;;;;;IAEJT,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAOrFH,EAAA,CAAAC,cAAA,gBAA0D;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQ9GH,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAChGH,EAAA,CAAAC,cAAA,gBAA0D;IAAAD,EAAA,CAAAE,MAAA,oDAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAO1BH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAE,MAAA,iDAC7C;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAI9BH,EAAA,CAAAC,cAAA,wBAAwE;IACzDD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAC,cAAA,iBAAkD;IACvBD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,iBAAuB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAQtCH,EAAA,CAAAU,SAAA,sBAAyD;;;;;IACzDV,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlDH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAW,iBAAA,EAAAC,OAAA,CAAAC,GAAA,kBAAAD,OAAA,CAAAC,GAAA,CAAAC,EAAA,gCAA2C;;;AD1D9E,OAAM,MAAOC,gBAAgB;EAgB3BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAftB,KAAAJ,GAAG,GAAe,IAAI;IACtB,KAAAK,OAAO,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,IAAIrB,YAAY,EAAc;IAC3C,KAAAsB,UAAU,GAAG,IAAItB,YAAY,EAAQ;IAI/C,KAAAuB,QAAQ,GAAG,CACT;MAAEf,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEH,KAAK,EAAE,YAAY;MAAEG,KAAK,EAAE;IAAY,CAAE,EAC5C;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAQ,CAAE,CACrC;EAE+C;EAEhDa,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACfC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACZ,GAAG,CAAC;EACxC;EAEAa,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAIA,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,EAAE;MAClCJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACZ,GAAG,CAAC;MACzC,IAAI,CAACgB,UAAU,EAAE;;EAErB;EAEAN,QAAQA,CAAA;IACN,IAAI,CAACK,OAAO,GAAG,IAAI,CAACX,WAAW,CAACa,KAAK,CAAC;MACpCC,KAAK,EAAE,CAAC,IAAI,CAAClB,GAAG,EAAEkB,KAAK,IAAI,EAAE,EAAE,CAAChC,UAAU,CAACiC,QAAQ,EAAEjC,UAAU,CAACkC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAChFC,WAAW,EAAE,CAAC,IAAI,CAACrB,GAAG,EAAEqB,WAAW,IAAI,EAAE,EAAE,CAACnC,UAAU,CAACiC,QAAQ,EAAEjC,UAAU,CAACkC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7FE,QAAQ,EAAE,CAAC,IAAI,CAACtB,GAAG,EAAEsB,QAAQ,IAAI,EAAE,EAAE,CAACpC,UAAU,CAACiC,QAAQ,EAAEjC,UAAU,CAACkC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACtFG,WAAW,EAAE,CAAC,IAAI,CAACvB,GAAG,EAAEuB,WAAW,IAAI,EAAE,EAAErC,UAAU,CAACkC,SAAS,CAAC,EAAE,CAAC,CAAC;MACpEI,OAAO,EAAE,CAAC,IAAI,CAACxB,GAAG,EAAEwB,OAAO,IAAI,WAAW,EAAEtC,UAAU,CAACiC,QAAQ,CAAC;MAChEM,YAAY,EAAE,CAAC,IAAI,CAACzB,GAAG,EAAEyB,YAAY,IAAI,EAAE,EAAEvC,UAAU,CAACkC,SAAS,CAAC,IAAI,CAAC,CAAC;MACxEM,MAAM,EAAE,CAAC,IAAI,CAAC1B,GAAG,EAAE0B,MAAM,IAAI,QAAQ;KACtC,CAAC;EACJ;EAEAV,UAAUA,CAAA;IACR,IAAI,IAAI,CAAChB,GAAG,EAAE;MACZ,IAAI,CAACe,OAAO,CAACY,UAAU,CAAC;QACtBT,KAAK,EAAE,IAAI,CAAClB,GAAG,CAACkB,KAAK,IAAI,EAAE;QAC3BG,WAAW,EAAE,IAAI,CAACrB,GAAG,CAACqB,WAAW,IAAI,EAAE;QACvCC,QAAQ,EAAE,IAAI,CAACtB,GAAG,CAACsB,QAAQ,IAAI,EAAE;QACjCC,WAAW,EAAE,IAAI,CAACvB,GAAG,CAACuB,WAAW,IAAI,EAAE;QACvCC,OAAO,EAAE,IAAI,CAACxB,GAAG,CAACwB,OAAO,IAAI,WAAW;QACxCC,YAAY,EAAE,IAAI,CAACzB,GAAG,CAACyB,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAE,IAAI,CAAC1B,GAAG,CAAC0B,MAAM,IAAI;OAC5B,CAAC;;EAEN;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACb,OAAO,CAACc,OAAO,EAAE;MACxB;MACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QAC/C,MAAMC,OAAO,GAAG,IAAI,CAACpB,OAAO,CAACqB,GAAG,CAACF,GAAG,CAAC;QACrCC,OAAO,EAAEE,aAAa,EAAE;MAC1B,CAAC,CAAC;MACF;;IAGF,MAAMC,UAAU,GAAe,IAAI,CAACvB,OAAO,CAACtB,KAAK;IACjD,IAAI,CAACa,UAAU,CAACiC,IAAI,CAACD,UAAU,CAAC;EAClC;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACjC,UAAU,CAACgC,IAAI,EAAE;EACxB;EAEA;EACA,IAAIE,CAACA,CAAA;IAAK,OAAO,IAAI,CAAC1B,OAAO,CAACiB,QAAQ;EAAE;;;uBA5E7B9B,gBAAgB,EAAAf,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB1C,gBAAgB;MAAA2C,SAAA;MAAAC,MAAA;QAAA9C,GAAA;QAAAK,OAAA;MAAA;MAAA0C,OAAA;QAAAzC,UAAA;QAAAC,UAAA;MAAA;MAAAyC,QAAA,GAAA7D,EAAA,CAAA8D,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT7BpE,EAAA,CAAAC,cAAA,cAAqE;UAAzCD,EAAA,CAAAsE,UAAA,sBAAAC,mDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAE/CzC,EAAA,CAAAC,cAAA,wBAAwD;UACzCD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAU,SAAA,eAA+E;UAC/EV,EAAA,CAAAwE,UAAA,IAAAC,qCAAA,uBAAoF;UACpFzE,EAAA,CAAAwE,UAAA,IAAAE,qCAAA,uBAAsG;UAC1G1E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,wBAAwD;UACzCD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAU,SAAA,gBAA2G;UAC3GV,EAAA,CAAAwE,UAAA,KAAAG,sCAAA,uBAAsF;UACtF3E,EAAA,CAAAwE,UAAA,KAAAI,sCAAA,uBAAwG;UAC5G5E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAA4D;UACxDD,EAAA,CAAAwE,UAAA,KAAAK,mCAAA,oBAES;UACb7E,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAwE,UAAA,KAAAM,sCAAA,uBAAqF;UACzF9E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9CH,EAAA,CAAAU,SAAA,gBAAmF;UACnFV,EAAA,CAAAwE,UAAA,KAAAO,sCAAA,uBAA8G;UAClH/E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAU,SAAA,mBACwB;UACxBV,EAAA,CAAAwE,UAAA,KAAAQ,sCAAA,uBAAgG;UAChGhF,EAAA,CAAAwE,UAAA,KAAAS,sCAAA,uBAC0B;UAC9BjF,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,yBAAwD;UACzCD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9CH,EAAA,CAAAU,SAAA,mBAA2G;UAC3GV,EAAA,CAAAwE,UAAA,KAAAU,sCAAA,uBAC0B;UAC9BlF,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAwE,UAAA,KAAAW,2CAAA,6BAMiB;UAGjBnF,EAAA,CAAAC,cAAA,eAA0B;UACmBD,EAAA,CAAAsE,UAAA,mBAAAc,mDAAA;YAAA,OAASf,GAAA,CAAAhB,QAAA,EAAU;UAAA,EAAC;UAAsBrD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClGH,EAAA,CAAAC,cAAA,kBAA6E;UACzED,EAAA,CAAAwE,UAAA,KAAAa,wCAAA,0BAAyD;UACzDrF,EAAA,CAAAwE,UAAA,KAAAc,iCAAA,kBAAyE;UAC7EtF,EAAA,CAAAG,YAAA,EAAS;;;UApEXH,EAAA,CAAAI,UAAA,cAAAiE,GAAA,CAAAzC,OAAA,CAAqB;UAKP5B,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,UAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,UAAAiC,MAAA,aAAqC;UACrCvF,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,UAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,UAAAiC,MAAA,cAAsC;UAOtCvF,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,aAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,aAAAiC,MAAA,aAAwC;UACxCvF,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,aAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,aAAAiC,MAAA,cAAyC;UAOxBvF,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAAiE,GAAA,CAAAhD,QAAA,CAAW;UAI5BrB,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,YAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,YAAAiC,MAAA,aAAuC;UAOvCvF,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,cAA4C;UAQ5CvF,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,aAA2C;UAC3CvF,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,gBAAAiC,MAAA,cAA4C;UAQ5CvF,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAf,CAAA,iBAAAiC,MAAA,kBAAAlB,GAAA,CAAAf,CAAA,iBAAAiC,MAAA,cAA6C;UAK5CvF,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAxD,GAAA,kBAAAwD,GAAA,CAAAxD,GAAA,CAAAC,EAAA,CAAa;UAUoCd,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAI,UAAA,aAAAiE,GAAA,CAAAnD,OAAA,CAAoB;UAC1BlB,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAI,UAAA,aAAAiE,GAAA,CAAAnD,OAAA,CAAoB;UAC5ClB,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAnD,OAAA,CAAa;UAClClB,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,UAAAiE,GAAA,CAAAnD,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}