{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, isDataSource, _DisposeViewRepeaterStrategy } from '@angular/cdk/collections';\nexport { DataSource } from '@angular/cdk/collections';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Input, ContentChild, Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, EmbeddedViewRef, EventEmitter, NgZone, Attribute, SkipSelf, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { Subject, from, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]]];\nconst _c1 = [\"caption\", \"colgroup, col\"];\nfunction CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r1.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.dataAccessor(data_r2, ctx_r1.name), \" \");\n  }\n}\nfunction mixinHasStickyInput(base) {\n  return class extends base {\n    /** Whether sticky positioning should be applied. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(v) {\n      const prevValue = this._sticky;\n      this._sticky = coerceBooleanProperty(v);\n      this._hasStickyChanged = prevValue !== this._sticky;\n    }\n    /** Whether the sticky value has changed since this was last called. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this._hasStickyChanged = false;\n      return hasStickyChanged;\n    }\n    /** Resets the dirty check for cases where the sticky state has been used without checking. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    constructor(...args) {\n      super(...args);\n      this._sticky = false;\n      /** Whether the sticky input has changed since it was last checked. */\n      this._hasStickyChanged = false;\n    }\n  };\n}\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n  constructor( /** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkCellDef_Factory(t) {\n      return new (t || CdkCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCellDef,\n      selectors: [[\"\", \"cdkCellDef\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellDef]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n  constructor( /** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkHeaderCellDef_Factory(t) {\n      return new (t || CdkHeaderCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderCellDef,\n      selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderCellDef]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n  constructor( /** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkFooterCellDef_Factory(t) {\n      return new (t || CdkFooterCellDef)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterCellDef,\n      selectors: [[\"\", \"cdkFooterCellDef\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterCellDef]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n// Boilerplate for applying mixins to CdkColumnDef.\n/** @docs-private */\nclass CdkColumnDefBase {}\nconst _CdkColumnDefBase = mixinHasStickyInput(CdkColumnDefBase);\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef extends _CdkColumnDefBase {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  get stickyEnd() {\n    return this._stickyEnd;\n  }\n  set stickyEnd(v) {\n    const prevValue = this._stickyEnd;\n    this._stickyEnd = coerceBooleanProperty(v);\n    this._hasStickyChanged = prevValue !== this._stickyEnd;\n  }\n  constructor(_table) {\n    super();\n    this._table = _table;\n    this._stickyEnd = false;\n  }\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setNameInput(value) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n  static {\n    this.ɵfac = function CdkColumnDef_Factory(t) {\n      return new (t || CdkColumnDef)(i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkColumnDef,\n      selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n      contentQueries: function CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n        }\n      },\n      inputs: {\n        sticky: \"sticky\",\n        name: [\"cdkColumnDef\", \"name\"],\n        stickyEnd: \"stickyEnd\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkColumnDef]',\n      inputs: ['sticky'],\n      providers: [{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_TABLE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input,\n      args: ['cdkColumnDef']\n    }],\n    stickyEnd: [{\n      type: Input,\n      args: ['stickyEnd']\n    }],\n    cell: [{\n      type: ContentChild,\n      args: [CdkCellDef]\n    }],\n    headerCell: [{\n      type: ContentChild,\n      args: [CdkHeaderCellDef]\n    }],\n    footerCell: [{\n      type: ContentChild,\n      args: [CdkFooterCellDef]\n    }]\n  });\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n  }\n  static {\n    this.ɵfac = function CdkHeaderCell_Factory(t) {\n      return new (t || CdkHeaderCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderCell,\n      selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n      hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-header-cell, th[cdk-header-cell]',\n      host: {\n        'class': 'cdk-header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], function () {\n    return [{\n      type: CdkColumnDef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n    if (columnDef._table?._elementRef.nativeElement.nodeType === 1) {\n      const tableRole = columnDef._table._elementRef.nativeElement.getAttribute('role');\n      const role = tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static {\n    this.ɵfac = function CdkFooterCell_Factory(t) {\n      return new (t || CdkFooterCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterCell,\n      selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n      hostAttrs: [1, \"cdk-footer-cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n      host: {\n        'class': 'cdk-footer-cell'\n      }\n    }]\n  }], function () {\n    return [{\n      type: CdkColumnDef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    super(columnDef, elementRef);\n    if (columnDef._table?._elementRef.nativeElement.nodeType === 1) {\n      const tableRole = columnDef._table._elementRef.nativeElement.getAttribute('role');\n      const role = tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static {\n    this.ɵfac = function CdkCell_Factory(t) {\n      return new (t || CdkCell)(i0.ɵɵdirectiveInject(CdkColumnDef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCell,\n      selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n      hostAttrs: [1, \"cdk-cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-cell, td[cdk-cell]',\n      host: {\n        'class': 'cdk-cell'\n      }\n    }]\n  }], function () {\n    return [{\n      type: CdkColumnDef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n\n/**\n * @docs-private\n */\nclass _Schedule {\n  constructor() {\n    this.tasks = [];\n    this.endTasks = [];\n  }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n  constructor(_ngZone) {\n    this._ngZone = _ngZone;\n    this._currentSchedule = null;\n    this._destroyed = new Subject();\n  }\n  /**\n   * Schedules the specified task to run at the end of the current VM turn.\n   */\n  schedule(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.tasks.push(task);\n  }\n  /**\n   * Schedules the specified task to run after other scheduled tasks at the end of the current\n   * VM turn.\n   */\n  scheduleEnd(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.endTasks.push(task);\n  }\n  /** Prevent any further tasks from running. */\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  _createScheduleIfNeeded() {\n    if (this._currentSchedule) {\n      return;\n    }\n    this._currentSchedule = new _Schedule();\n    this._getScheduleObservable().pipe(takeUntil(this._destroyed)).subscribe(() => {\n      while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n        const schedule = this._currentSchedule;\n        // Capture new tasks scheduled by the current set of tasks.\n        this._currentSchedule = new _Schedule();\n        for (const task of schedule.tasks) {\n          task();\n        }\n        for (const task of schedule.endTasks) {\n          task();\n        }\n      }\n      this._currentSchedule = null;\n    });\n  }\n  _getScheduleObservable() {\n    // Use onStable when in the context of an ongoing change detection cycle so that we\n    // do not accidentally trigger additional cycles.\n    return this._ngZone.isStable ? from(Promise.resolve(undefined)) : this._ngZone.onStable.pipe(take(1));\n  }\n  static {\n    this.ɵfac = function _CoalescedStyleScheduler_Factory(t) {\n      return new (t || _CoalescedStyleScheduler)(i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _CoalescedStyleScheduler,\n      factory: _CoalescedStyleScheduler.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CoalescedStyleScheduler, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n  constructor( /** @docs-private */template, _differs) {\n    this.template = template;\n    this._differs = _differs;\n  }\n  ngOnChanges(changes) {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = changes['columns'] && changes['columns'].currentValue || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff() {\n    return this._columnsDiffer.diff(this.columns);\n  }\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column) {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n  static {\n    this.ɵfac = function BaseRowDef_Factory(t) {\n      return new (t || BaseRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BaseRowDef,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseRowDef, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }];\n  }, null);\n})();\n// Boilerplate for applying mixins to CdkHeaderRowDef.\n/** @docs-private */\nclass CdkHeaderRowDefBase extends BaseRowDef {}\nconst _CdkHeaderRowDefBase = mixinHasStickyInput(CdkHeaderRowDefBase);\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends _CdkHeaderRowDefBase {\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  static {\n    this.ɵfac = function CdkHeaderRowDef_Factory(t) {\n      return new (t || CdkHeaderRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkHeaderRowDef,\n      selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n      inputs: {\n        columns: [\"cdkHeaderRowDef\", \"columns\"],\n        sticky: [\"cdkHeaderRowDefSticky\", \"sticky\"]\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderRowDef]',\n      inputs: ['columns: cdkHeaderRowDef', 'sticky: cdkHeaderRowDefSticky']\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_TABLE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n// Boilerplate for applying mixins to CdkFooterRowDef.\n/** @docs-private */\nclass CdkFooterRowDefBase extends BaseRowDef {}\nconst _CdkFooterRowDefBase = mixinHasStickyInput(CdkFooterRowDefBase);\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends _CdkFooterRowDefBase {\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  static {\n    this.ɵfac = function CdkFooterRowDef_Factory(t) {\n      return new (t || CdkFooterRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFooterRowDef,\n      selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n      inputs: {\n        columns: [\"cdkFooterRowDef\", \"columns\"],\n        sticky: [\"cdkFooterRowDefSticky\", \"sticky\"]\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterRowDef]',\n      inputs: ['columns: cdkFooterRowDef', 'sticky: cdkFooterRowDefSticky']\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_TABLE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n  // TODO(andrewseguin): Add an input for providing a switch function to determine\n  //   if this template should be used.\n  constructor(template, _differs, _table) {\n    super(template, _differs);\n    this._table = _table;\n  }\n  static {\n    this.ɵfac = function CdkRowDef_Factory(t) {\n      return new (t || CdkRowDef)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(CDK_TABLE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkRowDef,\n      selectors: [[\"\", \"cdkRowDef\", \"\"]],\n      inputs: {\n        columns: [\"cdkRowDefColumns\", \"columns\"],\n        when: [\"cdkRowDefWhen\", \"when\"]\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkRowDef]',\n      inputs: ['columns: cdkRowDefColumns', 'when: cdkRowDefWhen']\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_TABLE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static {\n    this.mostRecentCellOutlet = null;\n  }\n  constructor(_viewContainer) {\n    this._viewContainer = _viewContainer;\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n  static {\n    this.ɵfac = function CdkCellOutlet_Factory(t) {\n      return new (t || CdkCellOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCellOutlet,\n      selectors: [[\"\", \"cdkCellOutlet\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellOutlet]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }];\n  }, null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n  static {\n    this.ɵfac = function CdkHeaderRow_Factory(t) {\n      return new (t || CdkHeaderRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkHeaderRow,\n      selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkHeaderRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-header-row, tr[cdk-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n  static {\n    this.ɵfac = function CdkFooterRow_Factory(t) {\n      return new (t || CdkFooterRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkFooterRow,\n      selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkFooterRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-footer-row, tr[cdk-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-footer-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n  static {\n    this.ɵfac = function CdkRow_Factory(t) {\n      return new (t || CdkRow)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkRow,\n      selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function CdkRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-row, tr[cdk-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._contentClassName = 'cdk-no-data-row';\n  }\n  static {\n    this.ɵfac = function CdkNoDataRow_Factory(t) {\n      return new (t || CdkNoDataRow)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkNoDataRow,\n      selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkNoDataRow]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener) {\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this.direction = direction;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this._positionListener = _positionListener;\n    this._cachedCellWidths = [];\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row);\n      for (let i = 0; i < row.children.length; i++) {\n        elementsToClear.push(row.children[i]);\n      }\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      for (const element of elementsToClear) {\n        this._removeStickyStyle(element, stickyDirections);\n      }\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true) {\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: []\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: []\n        });\n      }\n      return;\n    }\n    const firstRow = rows[0];\n    const numCells = firstRow.children.length;\n    const cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n    const startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n    const endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n    const lastStickyStart = stickyStartStates.lastIndexOf(true);\n    const firstStickyEnd = stickyEndStates.indexOf(true);\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      const isRtl = this.direction === 'rtl';\n      const start = isRtl ? 'right' : 'left';\n      const end = isRtl ? 'left' : 'right';\n      for (const row of rows) {\n        for (let i = 0; i < numCells; i++) {\n          const cell = row.children[i];\n          if (stickyStartStates[i]) {\n            this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n          }\n          if (stickyEndStates[i]) {\n            this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n          }\n        }\n      }\n      if (this._positionListener) {\n        this._positionListener.stickyColumnsUpdated({\n          sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n        });\n        this._positionListener.stickyEndColumnsUpdated({\n          sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n        });\n      }\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n    // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n    // sticky states need to be reversed as well.\n    const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n    const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n    // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n    const stickyOffsets = [];\n    const stickyCellHeights = [];\n    const elementsToStick = [];\n    for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n      if (!states[rowIndex]) {\n        continue;\n      }\n      stickyOffsets[rowIndex] = stickyOffset;\n      const row = rows[rowIndex];\n      elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n      const height = row.getBoundingClientRect().height;\n      stickyOffset += height;\n      stickyCellHeights[rowIndex] = height;\n    }\n    const borderedRowIndex = states.lastIndexOf(true);\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n        if (!states[rowIndex]) {\n          continue;\n        }\n        const offset = stickyOffsets[rowIndex];\n        const isBorderedRowIndex = rowIndex === borderedRowIndex;\n        for (const element of elementsToStick[rowIndex]) {\n          this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n        }\n      }\n      if (position === 'top') {\n        this._positionListener?.stickyHeaderRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      } else {\n        this._positionListener?.stickyFooterRowsUpdated({\n          sizes: stickyCellHeights,\n          offsets: stickyOffsets,\n          elements: elementsToStick\n        });\n      }\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    const tfoot = tableElement.querySelector('tfoot');\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    this._coalescedStyleScheduler.schedule(() => {\n      if (stickyStates.some(state => !state)) {\n        this._removeStickyStyle(tfoot, ['bottom']);\n      } else {\n        this._addStickyStyle(tfoot, 'bottom', 0, false);\n      }\n    });\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      let cell = firstRowCells[i];\n      cellWidths.push(cell.getBoundingClientRect().width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n  static {\n    this.ɵfac = function CdkRecycleRows_Factory(t) {\n      return new (t || CdkRecycleRows)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkRecycleRows,\n      selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function DataRowOutlet_Factory(t) {\n      return new (t || DataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataRowOutlet,\n      selectors: [[\"\", \"rowOutlet\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[rowOutlet]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function HeaderRowOutlet_Factory(t) {\n      return new (t || HeaderRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: HeaderRowOutlet,\n      selectors: [[\"\", \"headerRowOutlet\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HeaderRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[headerRowOutlet]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function FooterRowOutlet_Factory(t) {\n      return new (t || FooterRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FooterRowOutlet,\n      selectors: [[\"\", \"footerRowOutlet\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[footerRowOutlet]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n  constructor(viewContainer, elementRef) {\n    this.viewContainer = viewContainer;\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function NoDataRowOutlet_Factory(t) {\n      return new (t || NoDataRowOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NoDataRowOutlet,\n      selectors: [[\"\", \"noDataRowOutlet\", \"\"]]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoDataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[noDataRowOutlet]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE =\n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"></ng-content>\n  <ng-content select=\"colgroup, col\"></ng-content>\n  <ng-container headerRowOutlet></ng-container>\n  <ng-container rowOutlet></ng-container>\n  <ng-container noDataRowOutlet></ng-container>\n  <ng-container footerRowOutlet></ng-container>\n`;\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nclass RowViewRef extends EmbeddedViewRef {}\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  get trackBy() {\n    return this._trackByFn;\n  }\n  set trackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  get multiTemplateDataRows() {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(v) {\n    this._multiTemplateDataRows = coerceBooleanProperty(v);\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  get fixedLayout() {\n    return this._fixedLayout;\n  }\n  set fixedLayout(v) {\n    this._fixedLayout = coerceBooleanProperty(v);\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  constructor(_differs, _changeDetectorRef, _elementRef, role, _dir, _document, _platform, _viewRepeater, _coalescedStyleScheduler, _viewportRuler,\n  /**\n   * @deprecated `_stickyPositioningListener` parameter to become required.\n   * @breaking-change 13.0.0\n   */\n  _stickyPositioningListener,\n  /**\n   * @deprecated `_ngZone` parameter to become required.\n   * @breaking-change 14.0.0\n   */\n  _ngZone) {\n    this._differs = _differs;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    this._platform = _platform;\n    this._viewRepeater = _viewRepeater;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._viewportRuler = _viewportRuler;\n    this._stickyPositioningListener = _stickyPositioningListener;\n    this._ngZone = _ngZone;\n    /** Subject that emits when the component has been destroyed. */\n    this._onDestroy = new Subject();\n    /**\n     * Map of all the user's defined columns (header, data, and footer cell template) identified by\n     * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n     * any custom column definitions added to `_customColumnDefs`.\n     */\n    this._columnDefsByName = new Map();\n    /**\n     * Column definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * column definitions as *its* content child.\n     */\n    this._customColumnDefs = new Set();\n    /**\n     * Data row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in data rows as *its* content child.\n     */\n    this._customRowDefs = new Set();\n    /**\n     * Header row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in header rows as *its* content child.\n     */\n    this._customHeaderRowDefs = new Set();\n    /**\n     * Footer row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n     * built-in footer row as *its* content child.\n     */\n    this._customFooterRowDefs = new Set();\n    /**\n     * Whether the header row definition has been changed. Triggers an update to the header row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    this._headerRowDefChanged = true;\n    /**\n     * Whether the footer row definition has been changed. Triggers an update to the footer row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    this._footerRowDefChanged = true;\n    /**\n     * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n     * change.\n     */\n    this._stickyColumnStylesNeedReset = true;\n    /**\n     * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n     * `false`, cached values will be used instead. This is only applicable to tables with\n     * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n     */\n    this._forceRecalculateCellWidths = true;\n    /**\n     * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n     * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n     * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n     * and row template matches, which allows the `IterableDiffer` to check rows by reference\n     * and understand which rows are added/moved/removed.\n     *\n     * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n     * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n     * contains an array of created pairs. The array is necessary to handle cases where the data\n     * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n     * stored.\n     */\n    this._cachedRenderRowsMap = new Map();\n    /**\n     * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n     * table subclasses.\n     */\n    this.stickyCssClass = 'cdk-table-sticky';\n    /**\n     * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n     * the position is set in a selector associated with the value of stickyCssClass. May be\n     * overridden by table subclasses\n     */\n    this.needsPositionStickyOnElement = true;\n    /** Whether the no data row is currently showing anything. */\n    this._isShowingNoDataRow = false;\n    this._multiTemplateDataRows = false;\n    this._fixedLayout = false;\n    /**\n     * Emits when the table completes rendering a set of data rows based on the latest data from the\n     * data source, even if the set of rows is empty.\n     */\n    this.contentChanged = new EventEmitter();\n    // TODO(andrewseguin): Remove max value as the end index\n    //   and instead calculate the view on init and scroll.\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     *\n     * @docs-private\n     */\n    this.viewChange = new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    });\n    if (!role) {\n      this._elementRef.nativeElement.setAttribute('role', 'table');\n    }\n    this._document = _document;\n    this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n  }\n  ngOnInit() {\n    this._setupStickyStyler();\n    if (this._isNativeHtmlTable) {\n      this._applyNativeTableSections();\n    }\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this._forceRecalculateCellWidths = true;\n    });\n  }\n  ngAfterContentChecked() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingRowDefsError();\n    }\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n    this._checkStickyStates();\n  }\n  ngOnDestroy() {\n    [this._rowOutlet.viewContainer, this._headerRowOutlet.viewContainer, this._footerRowOutlet.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n      def.clear();\n    });\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n    this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n      if (change.operation === 1 /* _ViewRepeaterOperation.INSERTED */ && change.context) {\n        this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n      }\n    });\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange(record => {\n      const rowView = viewContainer.get(record.currentIndex);\n      rowView.context.$implicit = record.item.data;\n    });\n    this._updateNoDataRow();\n    // Allow the new row data to render before measuring it.\n    // @breaking-change 14.0.0 Remove undefined check once _ngZone is required.\n    if (this._ngZone && NgZone.isInAngularZone()) {\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._onDestroy)).subscribe(() => {\n        this.updateStickyColumnStyles();\n      });\n    } else {\n      this.updateStickyColumnStyles();\n    }\n    this.contentChanged.next();\n  }\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef) {\n    this._customRowDefs.add(rowDef);\n  }\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef) {\n    this._customRowDefs.delete(rowDef);\n  }\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow) {\n    this._customNoDataRow = noDataRow;\n  }\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const tableElement = this._elementRef.nativeElement;\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    const thead = tableElement.querySelector('thead');\n    if (thead) {\n      thead.style.display = headerRows.length ? '' : 'none';\n    }\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles() {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    const tableElement = this._elementRef.nativeElement;\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    const tfoot = tableElement.querySelector('tfoot');\n    if (tfoot) {\n      tfoot.style.display = footerRows.length ? '' : 'none';\n    }\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n      this._stickyColumnStylesNeedReset = false;\n    }\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  _getAllRenderRows() {\n    const renderRows = [];\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n        const cache = this._cachedRenderRowsMap.get(renderRow.data);\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef).push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n    return renderRows;\n  }\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  _getRenderRowsForData(data, dataIndex, cache) {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift();\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {\n          data,\n          rowDef,\n          dataIndex\n        };\n      }\n    });\n  }\n  /** Update the map containing the content's column definitions. */\n  _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n    const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n  /** Update the list of all available row definitions that can be used. */\n  _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n    this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  _renderUpdatedColumns() {\n    const columnsDiffReducer = (acc, def) => acc || !!def.getColumnsDiff();\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    this._data = [];\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      this._rowOutlet.viewContainer.clear();\n    }\n    this._dataSource = dataSource;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = of(this.dataSource);\n    }\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n    this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this._data = data || [];\n      this.renderRows();\n    });\n  }\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  _addStickyColumnStyles(rows, rowDef) {\n    const columnDefs = Array.from(rowDef.columns || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n  }\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet) {\n    const renderedRows = [];\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i);\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n    return renderedRows;\n  }\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data, dataIndex) {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n    let rowDefs = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n    return rowDefs;\n  }\n  _getEmbeddedViewArgs(renderRow, index) {\n    const rowDef = renderRow.rowDef;\n    const context = {\n      $implicit: renderRow.data\n    };\n    return {\n      templateRef: rowDef.template,\n      context,\n      index\n    };\n  }\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  _renderRow(outlet, rowDef, index, context = {}) {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n  _renderCellTemplateForItem(rowDef, context) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex);\n      const context = viewRef.context;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n  /** Gets the column definitions for the provided row def. */\n  _getCellTemplates(rowDef) {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n      return rowDef.extractCellTemplate(column);\n    });\n  }\n  /** Adds native table sections (e.g. tbody) and moves the row outlets into them. */\n  _applyNativeTableSections() {\n    const documentFragment = this._document.createDocumentFragment();\n    const sections = [{\n      tag: 'thead',\n      outlets: [this._headerRowOutlet]\n    }, {\n      tag: 'tbody',\n      outlets: [this._rowOutlet, this._noDataRowOutlet]\n    }, {\n      tag: 'tfoot',\n      outlets: [this._footerRowOutlet]\n    }];\n    for (const section of sections) {\n      const element = this._document.createElement(section.tag);\n      element.setAttribute('role', 'rowgroup');\n      for (const outlet of section.outlets) {\n        element.appendChild(outlet.elementRef.nativeElement);\n      }\n      documentFragment.appendChild(element);\n    }\n    // Use a DocumentFragment so we don't hit the DOM on each iteration.\n    this._elementRef.nativeElement.appendChild(documentFragment);\n  }\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  _checkStickyStates() {\n    const stickyCheckReducer = (acc, d) => {\n      return acc || d.hasStickyChanged();\n    };\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  _setupStickyStyler() {\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener);\n    (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n      this._stickyStyler.direction = value;\n      this.updateStickyColumnStyles();\n    });\n  }\n  /** Filters definitions that belong to this table from a QueryList. */\n  _getOwnDefs(items) {\n    return items.filter(item => !item._table || item._table === this);\n  }\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n    if (!noDataRow) {\n      return;\n    }\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n    const container = this._noDataRowOutlet.viewContainer;\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode = view.rootNodes[0];\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n    this._isShowingNoDataRow = shouldShow;\n    this._changeDetectorRef.markForCheck();\n  }\n  static {\n    this.ɵfac = function CdkTable_Factory(t) {\n      return new (t || CdkTable)(i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵinjectAttribute('role'), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(_COALESCED_STYLE_SCHEDULER), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(STICKY_POSITIONING_LISTENER, 12), i0.ɵɵdirectiveInject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTable,\n      selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n      contentQueries: function CdkTable_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n        }\n      },\n      viewQuery: function CdkTable_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(DataRowOutlet, 7);\n          i0.ɵɵviewQuery(HeaderRowOutlet, 7);\n          i0.ɵɵviewQuery(FooterRowOutlet, 7);\n          i0.ɵɵviewQuery(NoDataRowOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rowOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headerRowOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._footerRowOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRowOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"cdk-table\"],\n      hostVars: 2,\n      hostBindings: function CdkTable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n        }\n      },\n      inputs: {\n        trackBy: \"trackBy\",\n        dataSource: \"dataSource\",\n        multiTemplateDataRows: \"multiTemplateDataRows\",\n        fixedLayout: \"fixedLayout\"\n      },\n      outputs: {\n        contentChanged: \"contentChanged\"\n      },\n      exportAs: [\"cdkTable\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }])],\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 0,\n      consts: [[\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n      template: function CdkTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵelementContainer(2, 0)(3, 1)(4, 2)(5, 3);\n        }\n      },\n      dependencies: [DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, NoDataRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTable, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-table, table[cdk-table]',\n      exportAs: 'cdkTable',\n      template: CDK_TABLE_TEMPLATE,\n      host: {\n        'class': 'cdk-table',\n        '[class.cdk-table-fixed-layout]': 'fixedLayout',\n        'ngSkipHydration': ''\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.IterableDiffers\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['role']\n      }]\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i2.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [_VIEW_REPEATER_STRATEGY]\n      }]\n    }, {\n      type: _CoalescedStyleScheduler,\n      decorators: [{\n        type: Inject,\n        args: [_COALESCED_STYLE_SCHEDULER]\n      }]\n    }, {\n      type: i3.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [STICKY_POSITIONING_LISTENER]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    trackBy: [{\n      type: Input\n    }],\n    dataSource: [{\n      type: Input\n    }],\n    multiTemplateDataRows: [{\n      type: Input\n    }],\n    fixedLayout: [{\n      type: Input\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    _rowOutlet: [{\n      type: ViewChild,\n      args: [DataRowOutlet, {\n        static: true\n      }]\n    }],\n    _headerRowOutlet: [{\n      type: ViewChild,\n      args: [HeaderRowOutlet, {\n        static: true\n      }]\n    }],\n    _footerRowOutlet: [{\n      type: ViewChild,\n      args: [FooterRowOutlet, {\n        static: true\n      }]\n    }],\n    _noDataRowOutlet: [{\n      type: ViewChild,\n      args: [NoDataRowOutlet, {\n        static: true\n      }]\n    }],\n    _contentColumnDefs: [{\n      type: ContentChildren,\n      args: [CdkColumnDef, {\n        descendants: true\n      }]\n    }],\n    _contentRowDefs: [{\n      type: ContentChildren,\n      args: [CdkRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentHeaderRowDefs: [{\n      type: ContentChildren,\n      args: [CdkHeaderRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentFooterRowDefs: [{\n      type: ContentChildren,\n      args: [CdkFooterRowDef, {\n        descendants: true\n      }]\n    }],\n    _noDataRow: [{\n      type: ContentChild,\n      args: [CdkNoDataRow]\n    }]\n  });\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n  /** Column name that should be used to reference this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._name = name;\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  constructor(\n  // `CdkTextColumn` is always requiring a table, but we just assert it manually\n  // for better error reporting.\n  // tslint:disable-next-line: lightweight-tokens\n  _table, _options) {\n    this._table = _table;\n    this._options = _options;\n    /** Alignment of the cell values. */\n    this.justify = 'start';\n    this._options = _options || {};\n  }\n  ngOnInit() {\n    this._syncColumnDefName();\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n    if (!this.dataAccessor) {\n      this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n    }\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n    return name[0].toUpperCase() + name.slice(1);\n  }\n  /** Synchronizes the column definition name with the text column name. */\n  _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n  static {\n    this.ɵfac = function CdkTextColumn_Factory(t) {\n      return new (t || CdkTextColumn)(i0.ɵɵdirectiveInject(CdkTable, 8), i0.ɵɵdirectiveInject(TEXT_COLUMN_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkTextColumn,\n      selectors: [[\"cdk-text-column\"]],\n      viewQuery: function CdkTextColumn_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkColumnDef, 7);\n          i0.ɵɵviewQuery(CdkCellDef, 7);\n          i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n        }\n      },\n      inputs: {\n        name: \"name\",\n        headerText: \"headerText\",\n        dataAccessor: \"dataAccessor\",\n        justify: \"justify\"\n      },\n      decls: 3,\n      vars: 0,\n      consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n      template: function CdkTextColumn_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, CdkTextColumn_th_1_Template, 2, 3, \"th\", 1);\n          i0.ɵɵtemplate(2, CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n          i0.ɵɵelementContainerEnd();\n        }\n      },\n      dependencies: [CdkCellDef, CdkHeaderCellDef, CdkColumnDef, CdkCell, CdkHeaderCell],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-text-column',\n      template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default\n    }]\n  }], function () {\n    return [{\n      type: CdkTable,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [TEXT_COLUMN_OPTIONS]\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    headerText: [{\n      type: Input\n    }],\n    dataAccessor: [{\n      type: Input\n    }],\n    justify: [{\n      type: Input\n    }],\n    columnDef: [{\n      type: ViewChild,\n      args: [CdkColumnDef, {\n        static: true\n      }]\n    }],\n    cell: [{\n      type: ViewChild,\n      args: [CdkCellDef, {\n        static: true\n      }]\n    }],\n    headerCell: [{\n      type: ViewChild,\n      args: [CdkHeaderCellDef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nclass CdkTableModule {\n  static {\n    this.ɵfac = function CdkTableModule_Factory(t) {\n      return new (t || CdkTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkTableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ScrollingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTableModule, [{\n    type: NgModule,\n    args: [{\n      exports: EXPORTED_DECLARATIONS,\n      declarations: EXPORTED_DECLARATIONS,\n      imports: [ScrollingModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };", "map": {"version": 3, "names": ["i1", "coerceBooleanProperty", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "isDataSource", "_DisposeViewRepeaterStrategy", "DataSource", "i2", "i3", "ScrollingModule", "DOCUMENT", "i0", "InjectionToken", "Directive", "Inject", "Optional", "Input", "ContentChild", "Injectable", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "EmbeddedViewRef", "EventEmitter", "NgZone", "Attribute", "SkipSelf", "Output", "ViewChild", "ContentChildren", "NgModule", "Subject", "from", "BehaviorSubject", "isObservable", "of", "takeUntil", "take", "_c0", "_c1", "CdkTextColumn_th_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "CdkTextColumn_td_2_Template", "data_r2", "$implicit", "ctx_r1", "dataAccessor", "name", "mixinHasStickyInput", "base", "sticky", "_sticky", "v", "prevValue", "_hasStickyChanged", "hasStickyChanged", "resetStickyChanged", "constructor", "args", "CDK_TABLE", "TEXT_COLUMN_OPTIONS", "CdkCellDef", "template", "ɵfac", "CdkCellDef_Factory", "t", "ɵɵdirectiveInject", "TemplateRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "selector", "CdkHeaderCellDef", "CdkHeaderCellDef_Factory", "CdkFooterCellDef", "CdkFooterCellDef_Factory", "CdkColumnDefBase", "_CdkColumnDefBase", "CdkColumnDef", "_name", "_setNameInput", "stickyEnd", "_stickyEnd", "_table", "_updateColumnCssClassName", "_columnCssClassName", "cssClassFriendlyName", "value", "replace", "CdkColumnDef_Factory", "contentQueries", "CdkColumnDef_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "cell", "first", "headerCell", "<PERSON><PERSON><PERSON><PERSON>", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "providers", "undefined", "decorators", "BaseCdkCell", "columnDef", "elementRef", "nativeElement", "classList", "add", "CdkHeaderCell", "CdkHeaderCell_Factory", "ElementRef", "hostAttrs", "host", "CdkFooterCell", "_elementRef", "nodeType", "tableRole", "getAttribute", "role", "setAttribute", "CdkFooterCell_Factory", "CdkCell", "CdkCell_Factory", "_Schedule", "tasks", "endTasks", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "_ngZone", "_currentSchedule", "_destroyed", "schedule", "task", "_createScheduleIfNeeded", "push", "scheduleEnd", "ngOnDestroy", "next", "complete", "_getScheduleObservable", "pipe", "subscribe", "length", "isStable", "Promise", "resolve", "onStable", "_CoalescedStyleScheduler_Factory", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "CDK_ROW_TEMPLATE", "BaseRowDef", "_differs", "ngOnChanges", "changes", "_<PERSON><PERSON><PERSON><PERSON>", "columns", "currentValue", "find", "create", "diff", "getColumnsDiff", "extractCellTemplate", "column", "CdkHeaderRowDef", "CdkFooterRowDef", "BaseRowDef_Factory", "Iterable<PERSON><PERSON><PERSON>", "ɵɵNgOnChangesFeature", "CdkHeaderRowDefBase", "_CdkHeaderRowDefBase", "CdkHeaderRowDef_Factory", "CdkFooterRowDefBase", "_CdkFooterRowDefBase", "CdkFooterRowDef_Factory", "CdkRowDef", "CdkRowDef_Factory", "when", "CdkCellOutlet", "mostRecentCellOutlet", "_viewContainer", "CdkCellOutlet_Factory", "ViewContainerRef", "CdkHeaderRow", "CdkHeaderRow_Factory", "ɵcmp", "ɵɵdefineComponent", "decls", "vars", "consts", "CdkHeaderRow_Template", "ɵɵelementContainer", "dependencies", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "CdkFooterRow", "CdkFooterRow_Factory", "CdkFooterRow_Template", "CdkRow", "CdkRow_Factory", "CdkRow_Template", "CdkNoDataRow", "templateRef", "_contentClassName", "CdkNoDataRow_Factory", "STICKY_DIRECTIONS", "<PERSON>y<PERSON><PERSON><PERSON>", "_isNativeHtmlTable", "_stickCellCss", "direction", "_coalescedStyleScheduler", "_isBrowser", "_needsPositionStickyOnElement", "_positionListener", "_cachedCellWidths", "_borderCellCss", "clearStickyPositioning", "rows", "stickyDirections", "elementsToClear", "row", "ELEMENT_NODE", "i", "children", "element", "_removeStickyStyle", "updateStickyColumns", "stickyStartStates", "stickyEndStates", "recalculateCellWidths", "some", "state", "stickyColumnsUpdated", "sizes", "stickyEndColumnsUpdated", "firstRow", "num<PERSON>ells", "cellWidths", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "startPositions", "_getStickyStartColumnPositions", "endPositions", "_getStickyEndColumnPositions", "lastStickyStart", "lastIndexOf", "firstStickyEnd", "indexOf", "isRtl", "start", "end", "_addStickyStyle", "slice", "map", "width", "index", "reverse", "stickRows", "rowsToStick", "stickyStates", "position", "states", "stickyOffsets", "stickyCellHeights", "elementsToStick", "rowIndex", "stickyOffset", "Array", "height", "getBoundingClientRect", "borderedRowIndex", "offset", "isBorderedRowIndex", "stickyHeaderRowsUpdated", "offsets", "elements", "stickyFooterRowsUpdated", "updateStickyFooterContainer", "tableElement", "tfoot", "querySelector", "dir", "style", "remove", "hasDirection", "zIndex", "_getCalculatedZIndex", "dir<PERSON><PERSON><PERSON>", "isBorderElement", "cssText", "zIndexIncrements", "top", "bottom", "left", "right", "firstRowCells", "widths", "positions", "nextPosition", "getTableUnknownColumnError", "id", "Error", "getTableDuplicateColumnNameError", "getTableMultipleDefaultRowDefsError", "getTableMissingMatchingRowDefError", "data", "JSON", "stringify", "getTableMissingRowDefsError", "getTableUnknownDataSourceError", "getTableTextColumnMissingParentTableError", "getTableTextColumnMissingNameError", "STICKY_POSITIONING_LISTENER", "CdkRecycleRows", "CdkRecycleRows_Factory", "useClass", "DataRowOutlet", "viewContainer", "DataRowOutlet_Factory", "HeaderRowOutlet", "HeaderRowOutlet_Factory", "FooterRowOutlet", "FooterRowOutlet_Factory", "NoDataRowOutlet", "NoDataRowOutlet_Factory", "CDK_TABLE_TEMPLATE", "RowViewRef", "CdkTable", "trackBy", "_trackByFn", "fn", "console", "warn", "dataSource", "_dataSource", "_switchDataSource", "multiTemplateDataRows", "_multiTemplateDataRows", "_rowOutlet", "_forceRenderDataRows", "updateStickyColumnStyles", "fixedLayout", "_fixedLayout", "_forceRecalculateCellWidths", "_stickyColumnStylesNeedReset", "_changeDetectorRef", "_dir", "_document", "_platform", "_view<PERSON><PERSON><PERSON>er", "_viewportRuler", "_stickyPositioningListener", "_onD<PERSON>roy", "_columnDefsByName", "Map", "_customColumnDefs", "Set", "_customRowDefs", "_customHeaderRowDefs", "_customFooterRowDefs", "_headerRowDefChanged", "_footerRowDefChanged", "_cachedRenderRowsMap", "stickyCssClass", "needsPositionStickyOnElement", "_isShowingNoDataRow", "contentChanged", "viewChange", "Number", "MAX_VALUE", "nodeName", "ngOnInit", "_setupStickyStyler", "_applyNativeTableSections", "_data<PERSON><PERSON>er", "_i", "dataRow", "dataIndex", "change", "ngAfterContentChecked", "_cacheRowDefs", "_cacheColumnDefs", "_headerRowDefs", "_footerRowDefs", "_rowDefs", "columnsChanged", "_renderUpdatedColumns", "rowDefsChanged", "_forceRenderHeaderRows", "_forceRenderFooterRows", "_renderChangeSubscription", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "_checkStickyStates", "_headerRowOutlet", "_footerRowOutlet", "for<PERSON>ach", "def", "clear", "_defaultRowDef", "disconnect", "renderRows", "_renderRows", "_getAllRenderRows", "_updateNoDataRow", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "item", "operation", "context", "_renderCellTemplateForItem", "rowDef", "_updateRowIndexContext", "forEachIdentityChange", "row<PERSON>iew", "get", "isInAngularZone", "addColumnDef", "removeColumnDef", "delete", "addRowDef", "removeRowDef", "addHeaderRowDef", "headerRowDef", "removeHeaderRowDef", "addFooterRowDef", "footerRowDef", "removeFooterRowDef", "setNoDataRow", "noDataRow", "_customNoDataRow", "updateStickyHeaderRowStyles", "headerRows", "_getRenderedRows", "thead", "display", "_sticky<PERSON><PERSON><PERSON>", "updateStickyFooterRowStyles", "footerRows", "dataRows", "headerRow", "_addStickyColumnStyles", "footerRow", "values", "prevCachedRenderRows", "_data", "renderRowsForData", "_getRenderRowsForData", "has", "set", "WeakMap", "j", "renderRow", "cache", "rowDefs", "_getRowDefs", "cachedRenderRows", "shift", "columnDefs", "mergeArrayAndSet", "_getOwnDefs", "_contentColumnDefs", "_contentHeaderRowDefs", "_contentFooterRowDefs", "_contentRowDefs", "defaultRowDefs", "filter", "columnsDiffReducer", "acc", "dataColumnsChanged", "reduce", "headerColumnsChanged", "footerColumnsChanged", "unsubscribe", "dataStream", "connect", "isArray", "_renderRow", "columnName", "rowOutlet", "renderedRows", "viewRef", "rootNodes", "outlet", "view", "createEmbeddedView", "cellTemplate", "_getCellTemplates", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIndex", "count", "last", "even", "odd", "columnId", "documentFragment", "createDocumentFragment", "sections", "tag", "outlets", "_noDataRowOutlet", "section", "createElement", "append<PERSON><PERSON><PERSON>", "stickyCheckReducer", "d", "<PERSON><PERSON><PERSON><PERSON>", "items", "_noDataRow", "shouldShow", "container", "rootNode", "CdkTable_Factory", "ChangeDetectorRef", "ɵɵinjectAttribute", "Directionality", "Platform", "ViewportRuler", "CdkTable_ContentQueries", "viewQuery", "CdkTable_Query", "ɵɵviewQuery", "hostVars", "hostBindings", "CdkTable_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "useValue", "ngContentSelectors", "CdkTable_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "static", "descendants", "array", "concat", "CdkTextColumn", "_syncColumnDefName", "_options", "_createDefaultHeaderText", "defaultDataAccessor", "defaultHeaderTextTransform", "toUpperCase", "CdkTextColumn_Factory", "CdkTextColumn_Query", "CdkTextColumn_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "EXPORTED_DECLARATIONS", "CdkTableModule", "CdkTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/findit/client/node_modules/@angular/cdk/fesm2022/table.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, isDataSource, _DisposeViewRepeaterStrategy } from '@angular/cdk/collections';\nexport { DataSource } from '@angular/cdk/collections';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, Input, ContentChild, Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, EmbeddedViewRef, EventEmitter, NgZone, Attribute, SkipSelf, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { Subject, from, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n */\nfunction mixinHasStickyInput(base) {\n    return class extends base {\n        /** Whether sticky positioning should be applied. */\n        get sticky() {\n            return this._sticky;\n        }\n        set sticky(v) {\n            const prevValue = this._sticky;\n            this._sticky = coerceBooleanProperty(v);\n            this._hasStickyChanged = prevValue !== this._sticky;\n        }\n        /** Whether the sticky value has changed since this was last called. */\n        hasStickyChanged() {\n            const hasStickyChanged = this._hasStickyChanged;\n            this._hasStickyChanged = false;\n            return hasStickyChanged;\n        }\n        /** Resets the dirty check for cases where the sticky state has been used without checking. */\n        resetStickyChanged() {\n            this._hasStickyChanged = false;\n        }\n        constructor(...args) {\n            super(...args);\n            this._sticky = false;\n            /** Whether the sticky input has changed since it was last checked. */\n            this._hasStickyChanged = false;\n        }\n    };\n}\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkCellDef, selector: \"[cdkCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCellDef, decorators: [{\n            type: Directive,\n            args: [{ selector: '[cdkCellDef]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkHeaderCellDef, selector: \"[cdkHeaderCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{ selector: '[cdkHeaderCellDef]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterCellDef, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFooterCellDef, selector: \"[cdkFooterCellDef]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{ selector: '[cdkFooterCellDef]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// Boilerplate for applying mixins to CdkColumnDef.\n/** @docs-private */\nclass CdkColumnDefBase {\n}\nconst _CdkColumnDefBase = mixinHasStickyInput(CdkColumnDefBase);\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef extends _CdkColumnDefBase {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /**\n     * Whether this column should be sticky positioned on the end of the row. Should make sure\n     * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n     * has been changed.\n     */\n    get stickyEnd() {\n        return this._stickyEnd;\n    }\n    set stickyEnd(v) {\n        const prevValue = this._stickyEnd;\n        this._stickyEnd = coerceBooleanProperty(v);\n        this._hasStickyChanged = prevValue !== this._stickyEnd;\n    }\n    constructor(_table) {\n        super();\n        this._table = _table;\n        this._stickyEnd = false;\n    }\n    /**\n     * Overridable method that sets the css classes that will be added to every cell in this\n     * column.\n     * In the future, columnCssClassName will change from type string[] to string and this\n     * will set a single string value.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setNameInput(value) {\n        // If the directive is set without a name (updated programmatically), then this setter will\n        // trigger with an empty string and should not overwrite the programmatically set value.\n        if (value) {\n            this._name = value;\n            this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n            this._updateColumnCssClassName();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkColumnDef, deps: [{ token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkColumnDef, selector: \"[cdkColumnDef]\", inputs: { sticky: \"sticky\", name: [\"cdkColumnDef\", \"name\"], stickyEnd: \"stickyEnd\" }, providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }], queries: [{ propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true }, { propertyName: \"footerCell\", first: true, predicate: CdkFooterCellDef, descendants: true }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkColumnDef]',\n                    inputs: ['sticky'],\n                    providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { name: [{\n                type: Input,\n                args: ['cdkColumnDef']\n            }], stickyEnd: [{\n                type: Input,\n                args: ['stickyEnd']\n            }], cell: [{\n                type: ContentChild,\n                args: [CdkCellDef]\n            }], headerCell: [{\n                type: ContentChild,\n                args: [CdkHeaderCellDef]\n            }], footerCell: [{\n                type: ContentChild,\n                args: [CdkFooterCellDef]\n            }] } });\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n    }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkHeaderCell, selector: \"cdk-header-cell, th[cdk-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"cdk-header-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-header-cell, th[cdk-header-cell]',\n                    host: {\n                        'class': 'cdk-header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: CdkColumnDef }, { type: i0.ElementRef }]; } });\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n        if (columnDef._table?._elementRef.nativeElement.nodeType === 1) {\n            const tableRole = columnDef._table._elementRef.nativeElement.getAttribute('role');\n            const role = tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFooterCell, selector: \"cdk-footer-cell, td[cdk-footer-cell]\", host: { classAttribute: \"cdk-footer-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n                    host: {\n                        'class': 'cdk-footer-cell',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: CdkColumnDef }, { type: i0.ElementRef }]; } });\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        super(columnDef, elementRef);\n        if (columnDef._table?._elementRef.nativeElement.nodeType === 1) {\n            const tableRole = columnDef._table._elementRef.nativeElement.getAttribute('role');\n            const role = tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCell, deps: [{ token: CdkColumnDef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkCell, selector: \"cdk-cell, td[cdk-cell]\", host: { classAttribute: \"cdk-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-cell, td[cdk-cell]',\n                    host: {\n                        'class': 'cdk-cell',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: CdkColumnDef }, { type: i0.ElementRef }]; } });\n\n/**\n * @docs-private\n */\nclass _Schedule {\n    constructor() {\n        this.tasks = [];\n        this.endTasks = [];\n    }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n    constructor(_ngZone) {\n        this._ngZone = _ngZone;\n        this._currentSchedule = null;\n        this._destroyed = new Subject();\n    }\n    /**\n     * Schedules the specified task to run at the end of the current VM turn.\n     */\n    schedule(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.tasks.push(task);\n    }\n    /**\n     * Schedules the specified task to run after other scheduled tasks at the end of the current\n     * VM turn.\n     */\n    scheduleEnd(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.endTasks.push(task);\n    }\n    /** Prevent any further tasks from running. */\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    _createScheduleIfNeeded() {\n        if (this._currentSchedule) {\n            return;\n        }\n        this._currentSchedule = new _Schedule();\n        this._getScheduleObservable()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n                const schedule = this._currentSchedule;\n                // Capture new tasks scheduled by the current set of tasks.\n                this._currentSchedule = new _Schedule();\n                for (const task of schedule.tasks) {\n                    task();\n                }\n                for (const task of schedule.endTasks) {\n                    task();\n                }\n            }\n            this._currentSchedule = null;\n        });\n    }\n    _getScheduleObservable() {\n        // Use onStable when in the context of an ongoing change detection cycle so that we\n        // do not accidentally trigger additional cycles.\n        return this._ngZone.isStable\n            ? from(Promise.resolve(undefined))\n            : this._ngZone.onStable.pipe(take(1));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _CoalescedStyleScheduler, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _CoalescedStyleScheduler }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _CoalescedStyleScheduler, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.NgZone }]; } });\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n    constructor(\n    /** @docs-private */ template, _differs) {\n        this.template = template;\n        this._differs = _differs;\n    }\n    ngOnChanges(changes) {\n        // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n        // of the columns property or an empty array if none is provided.\n        if (!this._columnsDiffer) {\n            const columns = (changes['columns'] && changes['columns'].currentValue) || [];\n            this._columnsDiffer = this._differs.find(columns).create();\n            this._columnsDiffer.diff(columns);\n        }\n    }\n    /**\n     * Returns the difference between the current columns and the columns from the last diff, or null\n     * if there is no difference.\n     */\n    getColumnsDiff() {\n        return this._columnsDiffer.diff(this.columns);\n    }\n    /** Gets this row def's relevant cell template from the provided column def. */\n    extractCellTemplate(column) {\n        if (this instanceof CdkHeaderRowDef) {\n            return column.headerCell.template;\n        }\n        if (this instanceof CdkFooterRowDef) {\n            return column.footerCell.template;\n        }\n        else {\n            return column.cell.template;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BaseRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: BaseRowDef, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BaseRowDef, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }]; } });\n// Boilerplate for applying mixins to CdkHeaderRowDef.\n/** @docs-private */\nclass CdkHeaderRowDefBase extends BaseRowDef {\n}\nconst _CdkHeaderRowDefBase = mixinHasStickyInput(CdkHeaderRowDefBase);\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends _CdkHeaderRowDefBase {\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkHeaderRowDef, selector: \"[cdkHeaderRowDef]\", inputs: { columns: [\"cdkHeaderRowDef\", \"columns\"], sticky: [\"cdkHeaderRowDefSticky\", \"sticky\"] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderRowDef]',\n                    inputs: ['columns: cdkHeaderRowDef', 'sticky: cdkHeaderRowDefSticky'],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }]; } });\n// Boilerplate for applying mixins to CdkFooterRowDef.\n/** @docs-private */\nclass CdkFooterRowDefBase extends BaseRowDef {\n}\nconst _CdkFooterRowDefBase = mixinHasStickyInput(CdkFooterRowDefBase);\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends _CdkFooterRowDefBase {\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFooterRowDef, selector: \"[cdkFooterRowDef]\", inputs: { columns: [\"cdkFooterRowDef\", \"columns\"], sticky: [\"cdkFooterRowDefSticky\", \"sticky\"] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterRowDef]',\n                    inputs: ['columns: cdkFooterRowDef', 'sticky: cdkFooterRowDefSticky'],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }]; } });\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    constructor(template, _differs, _table) {\n        super(template, _differs);\n        this._table = _table;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRowDef, deps: [{ token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: CDK_TABLE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkRowDef, selector: \"[cdkRowDef]\", inputs: { columns: [\"cdkRowDefColumns\", \"columns\"], when: [\"cdkRowDefWhen\", \"when\"] }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkRowDef]',\n                    inputs: ['columns: cdkRowDefColumns', 'when: cdkRowDefWhen'],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_TABLE]\n                }, {\n                    type: Optional\n                }] }]; } });\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n    /**\n     * Static property containing the latest constructed instance of this class.\n     * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n     * createEmbeddedView. After one of these components are created, this property will provide\n     * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n     * construct the cells with the provided context.\n     */\n    static { this.mostRecentCellOutlet = null; }\n    constructor(_viewContainer) {\n        this._viewContainer = _viewContainer;\n        CdkCellOutlet.mostRecentCellOutlet = this;\n    }\n    ngOnDestroy() {\n        // If this was the last outlet being rendered in the view, remove the reference\n        // from the static property after it has been destroyed to avoid leaking memory.\n        if (CdkCellOutlet.mostRecentCellOutlet === this) {\n            CdkCellOutlet.mostRecentCellOutlet = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCellOutlet, deps: [{ token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkCellOutlet, decorators: [{\n            type: Directive,\n            args: [{ selector: '[cdkCellOutlet]' }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }]; } });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkHeaderRow, selector: \"cdk-header-row, tr[cdk-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-header-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-header-row, tr[cdk-header-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFooterRow, selector: \"cdk-footer-row, tr[cdk-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-footer-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-footer-row, tr[cdk-footer-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-footer-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRow, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkRow, selector: \"cdk-row, tr[cdk-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-row, tr[cdk-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._contentClassName = 'cdk-no-data-row';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkNoDataRow, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkNoDataRow, selector: \"ng-template[cdkNoDataRow]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkNoDataRow]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n    /**\n     * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n     *     that uses the native `<table>` element.\n     * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n     *     sticky positioning applied.\n     * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n     *     by reversing left/right positions.\n     * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n     * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n     *     using inline styles. If false, it is assumed that position: sticky is included in\n     *     the component stylesheet for _stickCellCss.\n     * @param _positionListener A listener that is notified of changes to sticky rows/columns\n     *     and their dimensions.\n     */\n    constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener) {\n        this._isNativeHtmlTable = _isNativeHtmlTable;\n        this._stickCellCss = _stickCellCss;\n        this.direction = direction;\n        this._coalescedStyleScheduler = _coalescedStyleScheduler;\n        this._isBrowser = _isBrowser;\n        this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n        this._positionListener = _positionListener;\n        this._cachedCellWidths = [];\n        this._borderCellCss = {\n            'top': `${_stickCellCss}-border-elem-top`,\n            'bottom': `${_stickCellCss}-border-elem-bottom`,\n            'left': `${_stickCellCss}-border-elem-left`,\n            'right': `${_stickCellCss}-border-elem-right`,\n        };\n    }\n    /**\n     * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n     * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n     * @param rows The list of rows that should be cleared from sticking in the provided directions\n     * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n     */\n    clearStickyPositioning(rows, stickyDirections) {\n        const elementsToClear = [];\n        for (const row of rows) {\n            // If the row isn't an element (e.g. if it's an `ng-container`),\n            // it won't have inline styles or `children` so we skip it.\n            if (row.nodeType !== row.ELEMENT_NODE) {\n                continue;\n            }\n            elementsToClear.push(row);\n            for (let i = 0; i < row.children.length; i++) {\n                elementsToClear.push(row.children[i]);\n            }\n        }\n        // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            for (const element of elementsToClear) {\n                this._removeStickyStyle(element, stickyDirections);\n            }\n        });\n    }\n    /**\n     * Applies sticky left and right positions to the cells of each row according to the sticky\n     * states of the rendered column definitions.\n     * @param rows The rows that should have its set of cells stuck according to the sticky states.\n     * @param stickyStartStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the start of the row.\n     * @param stickyEndStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the end of the row.\n     * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n     *     column cell. If `false` cached widths will be used instead.\n     */\n    updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true) {\n        if (!rows.length ||\n            !this._isBrowser ||\n            !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n            if (this._positionListener) {\n                this._positionListener.stickyColumnsUpdated({ sizes: [] });\n                this._positionListener.stickyEndColumnsUpdated({ sizes: [] });\n            }\n            return;\n        }\n        const firstRow = rows[0];\n        const numCells = firstRow.children.length;\n        const cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n        const startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n        const endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n        const lastStickyStart = stickyStartStates.lastIndexOf(true);\n        const firstStickyEnd = stickyEndStates.indexOf(true);\n        // Coalesce with sticky row updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            const isRtl = this.direction === 'rtl';\n            const start = isRtl ? 'right' : 'left';\n            const end = isRtl ? 'left' : 'right';\n            for (const row of rows) {\n                for (let i = 0; i < numCells; i++) {\n                    const cell = row.children[i];\n                    if (stickyStartStates[i]) {\n                        this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n                    }\n                    if (stickyEndStates[i]) {\n                        this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n                    }\n                }\n            }\n            if (this._positionListener) {\n                this._positionListener.stickyColumnsUpdated({\n                    sizes: lastStickyStart === -1\n                        ? []\n                        : cellWidths\n                            .slice(0, lastStickyStart + 1)\n                            .map((width, index) => (stickyStartStates[index] ? width : null)),\n                });\n                this._positionListener.stickyEndColumnsUpdated({\n                    sizes: firstStickyEnd === -1\n                        ? []\n                        : cellWidths\n                            .slice(firstStickyEnd)\n                            .map((width, index) => (stickyEndStates[index + firstStickyEnd] ? width : null))\n                            .reverse(),\n                });\n            }\n        });\n    }\n    /**\n     * Applies sticky positioning to the row's cells if using the native table layout, and to the\n     * row itself otherwise.\n     * @param rowsToStick The list of rows that should be stuck according to their corresponding\n     *     sticky state and to the provided top or bottom position.\n     * @param stickyStates A list of boolean states where each state represents whether the row\n     *     should be stuck in the particular top or bottom position.\n     * @param position The position direction in which the row should be stuck if that row should be\n     *     sticky.\n     *\n     */\n    stickRows(rowsToStick, stickyStates, position) {\n        // Since we can't measure the rows on the server, we can't stick the rows properly.\n        if (!this._isBrowser) {\n            return;\n        }\n        // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n        // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n        // sticky states need to be reversed as well.\n        const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n        const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n        // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n        const stickyOffsets = [];\n        const stickyCellHeights = [];\n        const elementsToStick = [];\n        for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n            if (!states[rowIndex]) {\n                continue;\n            }\n            stickyOffsets[rowIndex] = stickyOffset;\n            const row = rows[rowIndex];\n            elementsToStick[rowIndex] = this._isNativeHtmlTable\n                ? Array.from(row.children)\n                : [row];\n            const height = row.getBoundingClientRect().height;\n            stickyOffset += height;\n            stickyCellHeights[rowIndex] = height;\n        }\n        const borderedRowIndex = states.lastIndexOf(true);\n        // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n        // (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n                if (!states[rowIndex]) {\n                    continue;\n                }\n                const offset = stickyOffsets[rowIndex];\n                const isBorderedRowIndex = rowIndex === borderedRowIndex;\n                for (const element of elementsToStick[rowIndex]) {\n                    this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n                }\n            }\n            if (position === 'top') {\n                this._positionListener?.stickyHeaderRowsUpdated({\n                    sizes: stickyCellHeights,\n                    offsets: stickyOffsets,\n                    elements: elementsToStick,\n                });\n            }\n            else {\n                this._positionListener?.stickyFooterRowsUpdated({\n                    sizes: stickyCellHeights,\n                    offsets: stickyOffsets,\n                    elements: elementsToStick,\n                });\n            }\n        });\n    }\n    /**\n     * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n     * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n     * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n     * the tfoot element.\n     */\n    updateStickyFooterContainer(tableElement, stickyStates) {\n        if (!this._isNativeHtmlTable) {\n            return;\n        }\n        const tfoot = tableElement.querySelector('tfoot');\n        // Coalesce with other sticky updates (and potentially other changes like column resize).\n        this._coalescedStyleScheduler.schedule(() => {\n            if (stickyStates.some(state => !state)) {\n                this._removeStickyStyle(tfoot, ['bottom']);\n            }\n            else {\n                this._addStickyStyle(tfoot, 'bottom', 0, false);\n            }\n        });\n    }\n    /**\n     * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n     * the zIndex, removing each of the provided sticky directions, and removing the\n     * sticky position if there are no more directions.\n     */\n    _removeStickyStyle(element, stickyDirections) {\n        for (const dir of stickyDirections) {\n            element.style[dir] = '';\n            element.classList.remove(this._borderCellCss[dir]);\n        }\n        // If the element no longer has any more sticky directions, remove sticky positioning and\n        // the sticky CSS class.\n        // Short-circuit checking element.style[dir] for stickyDirections as they\n        // were already removed above.\n        const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n        if (hasDirection) {\n            element.style.zIndex = this._getCalculatedZIndex(element);\n        }\n        else {\n            // When not hasDirection, _getCalculatedZIndex will always return ''.\n            element.style.zIndex = '';\n            if (this._needsPositionStickyOnElement) {\n                element.style.position = '';\n            }\n            element.classList.remove(this._stickCellCss);\n        }\n    }\n    /**\n     * Adds the sticky styling to the element by adding the sticky style class, changing position\n     * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n     * direction and value.\n     */\n    _addStickyStyle(element, dir, dirValue, isBorderElement) {\n        element.classList.add(this._stickCellCss);\n        if (isBorderElement) {\n            element.classList.add(this._borderCellCss[dir]);\n        }\n        element.style[dir] = `${dirValue}px`;\n        element.style.zIndex = this._getCalculatedZIndex(element);\n        if (this._needsPositionStickyOnElement) {\n            element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n        }\n    }\n    /**\n     * Calculate what the z-index should be for the element, depending on what directions (top,\n     * bottom, left, right) have been set. It should be true that elements with a top direction\n     * should have the highest index since these are elements like a table header. If any of those\n     * elements are also sticky in another direction, then they should appear above other elements\n     * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n     * (e.g. footer rows) should then be next in the ordering such that they are below the header\n     * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n     * should minimally increment so that they are above non-sticky elements but below top and bottom\n     * elements.\n     */\n    _getCalculatedZIndex(element) {\n        const zIndexIncrements = {\n            top: 100,\n            bottom: 10,\n            left: 1,\n            right: 1,\n        };\n        let zIndex = 0;\n        // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n        // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n        // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n        for (const dir of STICKY_DIRECTIONS) {\n            if (element.style[dir]) {\n                zIndex += zIndexIncrements[dir];\n            }\n        }\n        return zIndex ? `${zIndex}` : '';\n    }\n    /** Gets the widths for each cell in the provided row. */\n    _getCellWidths(row, recalculateCellWidths = true) {\n        if (!recalculateCellWidths && this._cachedCellWidths.length) {\n            return this._cachedCellWidths;\n        }\n        const cellWidths = [];\n        const firstRowCells = row.children;\n        for (let i = 0; i < firstRowCells.length; i++) {\n            let cell = firstRowCells[i];\n            cellWidths.push(cell.getBoundingClientRect().width);\n        }\n        this._cachedCellWidths = cellWidths;\n        return cellWidths;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyStartColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = 0; i < widths.length; i++) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyEndColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = widths.length; i > 0; i--) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n    return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n    return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n    return Error(`Could not find a matching row definition for the` +\n        `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n    return Error('Missing definitions for header, footer, and row; ' +\n        'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n    return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n    return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n    return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkRecycleRows, selector: \"cdk-table[recycleRows], table[cdk-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DataRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: DataRowOutlet, selector: \"[rowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{ selector: '[rowOutlet]' }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }]; } });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: HeaderRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: HeaderRowOutlet, decorators: [{\n            type: Directive,\n            args: [{ selector: '[headerRowOutlet]' }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }]; } });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FooterRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FooterRowOutlet, decorators: [{\n            type: Directive,\n            args: [{ selector: '[footerRowOutlet]' }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }]; } });\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n    constructor(viewContainer, elementRef) {\n        this.viewContainer = viewContainer;\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NoDataRowOutlet, deps: [{ token: i0.ViewContainerRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NoDataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{ selector: '[noDataRowOutlet]' }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.ElementRef }]; } });\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE = \n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"></ng-content>\n  <ng-content select=\"colgroup, col\"></ng-content>\n  <ng-container headerRowOutlet></ng-container>\n  <ng-container rowOutlet></ng-container>\n  <ng-container noDataRowOutlet></ng-container>\n  <ng-container footerRowOutlet></ng-container>\n`;\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nclass RowViewRef extends EmbeddedViewRef {\n}\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n     * relative to the function to know if a row should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    get trackBy() {\n        return this._trackByFn;\n    }\n    set trackBy(fn) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n            console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n        }\n        this._trackByFn = fn;\n    }\n    /**\n     * The table's source of data, which can be provided in three ways (in order of complexity):\n     *   - Simple data array (each object represents one table row)\n     *   - Stream that emits a data array each time the array changes\n     *   - `DataSource` object that implements the connect/disconnect interface.\n     *\n     * If a data array is provided, the table must be notified when the array's objects are\n     * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n     * render the diff since the last table render. If the data array reference is changed, the table\n     * will automatically trigger an update to the rows.\n     *\n     * When providing an Observable stream, the table will trigger an update automatically when the\n     * stream emits a new array of data.\n     *\n     * Finally, when providing a `DataSource` object, the table will use the Observable stream\n     * provided by the connect function and trigger updates when that stream emits new data array\n     * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n     * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n     * subscriptions registered during the connect process).\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    /**\n     * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n     * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n     * dataobject will render the first row that evaluates its when predicate to true, in the order\n     * defined in the table, or otherwise the default row which does not have a when predicate.\n     */\n    get multiTemplateDataRows() {\n        return this._multiTemplateDataRows;\n    }\n    set multiTemplateDataRows(v) {\n        this._multiTemplateDataRows = coerceBooleanProperty(v);\n        // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n        // this setter will be invoked before the row outlet has been defined hence the null check.\n        if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n            this._forceRenderDataRows();\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n     * and optimize rendering sticky styles for native tables. No-op for flex tables.\n     */\n    get fixedLayout() {\n        return this._fixedLayout;\n    }\n    set fixedLayout(v) {\n        this._fixedLayout = coerceBooleanProperty(v);\n        // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n        this._forceRecalculateCellWidths = true;\n        this._stickyColumnStylesNeedReset = true;\n    }\n    constructor(_differs, _changeDetectorRef, _elementRef, role, _dir, _document, _platform, _viewRepeater, _coalescedStyleScheduler, _viewportRuler, \n    /**\n     * @deprecated `_stickyPositioningListener` parameter to become required.\n     * @breaking-change 13.0.0\n     */\n    _stickyPositioningListener, \n    /**\n     * @deprecated `_ngZone` parameter to become required.\n     * @breaking-change 14.0.0\n     */\n    _ngZone) {\n        this._differs = _differs;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        this._platform = _platform;\n        this._viewRepeater = _viewRepeater;\n        this._coalescedStyleScheduler = _coalescedStyleScheduler;\n        this._viewportRuler = _viewportRuler;\n        this._stickyPositioningListener = _stickyPositioningListener;\n        this._ngZone = _ngZone;\n        /** Subject that emits when the component has been destroyed. */\n        this._onDestroy = new Subject();\n        /**\n         * Map of all the user's defined columns (header, data, and footer cell template) identified by\n         * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n         * any custom column definitions added to `_customColumnDefs`.\n         */\n        this._columnDefsByName = new Map();\n        /**\n         * Column definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * column definitions as *its* content child.\n         */\n        this._customColumnDefs = new Set();\n        /**\n         * Data row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * built-in data rows as *its* content child.\n         */\n        this._customRowDefs = new Set();\n        /**\n         * Header row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n         * built-in header rows as *its* content child.\n         */\n        this._customHeaderRowDefs = new Set();\n        /**\n         * Footer row definitions that were defined outside of the direct content children of the table.\n         * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n         * built-in footer row as *its* content child.\n         */\n        this._customFooterRowDefs = new Set();\n        /**\n         * Whether the header row definition has been changed. Triggers an update to the header row after\n         * content is checked. Initialized as true so that the table renders the initial set of rows.\n         */\n        this._headerRowDefChanged = true;\n        /**\n         * Whether the footer row definition has been changed. Triggers an update to the footer row after\n         * content is checked. Initialized as true so that the table renders the initial set of rows.\n         */\n        this._footerRowDefChanged = true;\n        /**\n         * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n         * change.\n         */\n        this._stickyColumnStylesNeedReset = true;\n        /**\n         * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n         * `false`, cached values will be used instead. This is only applicable to tables with\n         * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n         */\n        this._forceRecalculateCellWidths = true;\n        /**\n         * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n         * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n         * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n         * and row template matches, which allows the `IterableDiffer` to check rows by reference\n         * and understand which rows are added/moved/removed.\n         *\n         * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n         * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n         * contains an array of created pairs. The array is necessary to handle cases where the data\n         * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n         * stored.\n         */\n        this._cachedRenderRowsMap = new Map();\n        /**\n         * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n         * table subclasses.\n         */\n        this.stickyCssClass = 'cdk-table-sticky';\n        /**\n         * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n         * the position is set in a selector associated with the value of stickyCssClass. May be\n         * overridden by table subclasses\n         */\n        this.needsPositionStickyOnElement = true;\n        /** Whether the no data row is currently showing anything. */\n        this._isShowingNoDataRow = false;\n        this._multiTemplateDataRows = false;\n        this._fixedLayout = false;\n        /**\n         * Emits when the table completes rendering a set of data rows based on the latest data from the\n         * data source, even if the set of rows is empty.\n         */\n        this.contentChanged = new EventEmitter();\n        // TODO(andrewseguin): Remove max value as the end index\n        //   and instead calculate the view on init and scroll.\n        /**\n         * Stream containing the latest information on what rows are being displayed on screen.\n         * Can be used by the data source to as a heuristic of what data should be provided.\n         *\n         * @docs-private\n         */\n        this.viewChange = new BehaviorSubject({\n            start: 0,\n            end: Number.MAX_VALUE,\n        });\n        if (!role) {\n            this._elementRef.nativeElement.setAttribute('role', 'table');\n        }\n        this._document = _document;\n        this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n    }\n    ngOnInit() {\n        this._setupStickyStyler();\n        if (this._isNativeHtmlTable) {\n            this._applyNativeTableSections();\n        }\n        // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n        // the user has provided a custom trackBy, return the result of that function as evaluated\n        // with the values of the `RenderRow`'s data and index.\n        this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n            return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n        });\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(() => {\n            this._forceRecalculateCellWidths = true;\n        });\n    }\n    ngAfterContentChecked() {\n        // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n        this._cacheRowDefs();\n        this._cacheColumnDefs();\n        // Make sure that the user has at least added header, footer, or data row def.\n        if (!this._headerRowDefs.length &&\n            !this._footerRowDefs.length &&\n            !this._rowDefs.length &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingRowDefsError();\n        }\n        // Render updates if the list of columns have been changed for the header, row, or footer defs.\n        const columnsChanged = this._renderUpdatedColumns();\n        const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n        // Ensure sticky column styles are reset if set to `true` elsewhere.\n        this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n        this._forceRecalculateCellWidths = rowDefsChanged;\n        // If the header row definition has been changed, trigger a render to the header row.\n        if (this._headerRowDefChanged) {\n            this._forceRenderHeaderRows();\n            this._headerRowDefChanged = false;\n        }\n        // If the footer row definition has been changed, trigger a render to the footer row.\n        if (this._footerRowDefChanged) {\n            this._forceRenderFooterRows();\n            this._footerRowDefChanged = false;\n        }\n        // If there is a data source and row definitions, connect to the data source unless a\n        // connection has already been made.\n        if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n            this._observeRenderChanges();\n        }\n        else if (this._stickyColumnStylesNeedReset) {\n            // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n            // called when it row data arrives. Otherwise, we need to call it proactively.\n            this.updateStickyColumnStyles();\n        }\n        this._checkStickyStates();\n    }\n    ngOnDestroy() {\n        [\n            this._rowOutlet.viewContainer,\n            this._headerRowOutlet.viewContainer,\n            this._footerRowOutlet.viewContainer,\n            this._cachedRenderRowsMap,\n            this._customColumnDefs,\n            this._customRowDefs,\n            this._customHeaderRowDefs,\n            this._customFooterRowDefs,\n            this._columnDefsByName,\n        ].forEach(def => {\n            def.clear();\n        });\n        this._headerRowDefs = [];\n        this._footerRowDefs = [];\n        this._defaultRowDef = null;\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n    }\n    /**\n     * Renders rows based on the table's latest set of data, which was either provided directly as an\n     * input or retrieved through an Observable stream (directly or from a DataSource).\n     * Checks for differences in the data since the last diff to perform only the necessary\n     * changes (add/remove/move rows).\n     *\n     * If the table's data source is a DataSource or Observable, this will be invoked automatically\n     * each time the provided Observable stream emits a new data array. Otherwise if your data is\n     * an array, this function will need to be called to render any changes.\n     */\n    renderRows() {\n        this._renderRows = this._getAllRenderRows();\n        const changes = this._dataDiffer.diff(this._renderRows);\n        if (!changes) {\n            this._updateNoDataRow();\n            this.contentChanged.next();\n            return;\n        }\n        const viewContainer = this._rowOutlet.viewContainer;\n        this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, (change) => {\n            if (change.operation === 1 /* _ViewRepeaterOperation.INSERTED */ && change.context) {\n                this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n            }\n        });\n        // Update the meta context of a row's context data (index, count, first, last, ...)\n        this._updateRowIndexContext();\n        // Update rows that did not get added/removed/moved but may have had their identity changed,\n        // e.g. if trackBy matched data on some property but the actual data reference changed.\n        changes.forEachIdentityChange((record) => {\n            const rowView = viewContainer.get(record.currentIndex);\n            rowView.context.$implicit = record.item.data;\n        });\n        this._updateNoDataRow();\n        // Allow the new row data to render before measuring it.\n        // @breaking-change 14.0.0 Remove undefined check once _ngZone is required.\n        if (this._ngZone && NgZone.isInAngularZone()) {\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._onDestroy)).subscribe(() => {\n                this.updateStickyColumnStyles();\n            });\n        }\n        else {\n            this.updateStickyColumnStyles();\n        }\n        this.contentChanged.next();\n    }\n    /** Adds a column definition that was not included as part of the content children. */\n    addColumnDef(columnDef) {\n        this._customColumnDefs.add(columnDef);\n    }\n    /** Removes a column definition that was not included as part of the content children. */\n    removeColumnDef(columnDef) {\n        this._customColumnDefs.delete(columnDef);\n    }\n    /** Adds a row definition that was not included as part of the content children. */\n    addRowDef(rowDef) {\n        this._customRowDefs.add(rowDef);\n    }\n    /** Removes a row definition that was not included as part of the content children. */\n    removeRowDef(rowDef) {\n        this._customRowDefs.delete(rowDef);\n    }\n    /** Adds a header row definition that was not included as part of the content children. */\n    addHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.add(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Removes a header row definition that was not included as part of the content children. */\n    removeHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.delete(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Adds a footer row definition that was not included as part of the content children. */\n    addFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.add(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Removes a footer row definition that was not included as part of the content children. */\n    removeFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.delete(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Sets a no data row definition that was not included as a part of the content children. */\n    setNoDataRow(noDataRow) {\n        this._customNoDataRow = noDataRow;\n    }\n    /**\n     * Updates the header sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n     * automatically called when the header row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyHeaderRowStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        const tableElement = this._elementRef.nativeElement;\n        // Hide the thead element if there are no header rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        const thead = tableElement.querySelector('thead');\n        if (thead) {\n            thead.style.display = headerRows.length ? '' : 'none';\n        }\n        const stickyStates = this._headerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n        this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._headerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n     * automatically called when the footer row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyFooterRowStyles() {\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        const tableElement = this._elementRef.nativeElement;\n        // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        const tfoot = tableElement.querySelector('tfoot');\n        if (tfoot) {\n            tfoot.style.display = footerRows.length ? '' : 'none';\n        }\n        const stickyStates = this._footerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n        this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n        this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._footerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the column sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the left and right. Then sticky styles are added for the left and right according\n     * to the column definitions for each cell in each row. This is automatically called when\n     * the data source provides a new set of data or when a column definition changes its sticky\n     * input. May be called manually for cases where the cell content changes outside of these events.\n     */\n    updateStickyColumnStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        const dataRows = this._getRenderedRows(this._rowOutlet);\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n        // In a table using a fixed layout, row content won't affect column width, so sticky styles\n        // don't need to be cleared unless either the sticky column config changes or one of the row\n        // defs change.\n        if ((this._isNativeHtmlTable && !this._fixedLayout) || this._stickyColumnStylesNeedReset) {\n            // Clear the left and right positioning from all columns in the table across all rows since\n            // sticky columns span across all table sections (header, data, footer)\n            this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n            this._stickyColumnStylesNeedReset = false;\n        }\n        // Update the sticky styles for each header row depending on the def's sticky state\n        headerRows.forEach((headerRow, i) => {\n            this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n        });\n        // Update the sticky styles for each data row depending on its def's sticky state\n        this._rowDefs.forEach(rowDef => {\n            // Collect all the rows rendered with this row definition.\n            const rows = [];\n            for (let i = 0; i < dataRows.length; i++) {\n                if (this._renderRows[i].rowDef === rowDef) {\n                    rows.push(dataRows[i]);\n                }\n            }\n            this._addStickyColumnStyles(rows, rowDef);\n        });\n        // Update the sticky styles for each footer row depending on the def's sticky state\n        footerRows.forEach((footerRow, i) => {\n            this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n        });\n        // Reset the dirty state of the sticky input change since it has been used.\n        Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Get the list of RenderRow objects to render according to the current list of data and defined\n     * row definitions. If the previous list already contained a particular pair, it should be reused\n     * so that the differ equates their references.\n     */\n    _getAllRenderRows() {\n        const renderRows = [];\n        // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n        // new cache while unused ones can be picked up by garbage collection.\n        const prevCachedRenderRows = this._cachedRenderRowsMap;\n        this._cachedRenderRowsMap = new Map();\n        // For each data object, get the list of rows that should be rendered, represented by the\n        // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n        for (let i = 0; i < this._data.length; i++) {\n            let data = this._data[i];\n            const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n            if (!this._cachedRenderRowsMap.has(data)) {\n                this._cachedRenderRowsMap.set(data, new WeakMap());\n            }\n            for (let j = 0; j < renderRowsForData.length; j++) {\n                let renderRow = renderRowsForData[j];\n                const cache = this._cachedRenderRowsMap.get(renderRow.data);\n                if (cache.has(renderRow.rowDef)) {\n                    cache.get(renderRow.rowDef).push(renderRow);\n                }\n                else {\n                    cache.set(renderRow.rowDef, [renderRow]);\n                }\n                renderRows.push(renderRow);\n            }\n        }\n        return renderRows;\n    }\n    /**\n     * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n     * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n     * `(T, CdkRowDef)` pair.\n     */\n    _getRenderRowsForData(data, dataIndex, cache) {\n        const rowDefs = this._getRowDefs(data, dataIndex);\n        return rowDefs.map(rowDef => {\n            const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n            if (cachedRenderRows.length) {\n                const dataRow = cachedRenderRows.shift();\n                dataRow.dataIndex = dataIndex;\n                return dataRow;\n            }\n            else {\n                return { data, rowDef, dataIndex };\n            }\n        });\n    }\n    /** Update the map containing the content's column definitions. */\n    _cacheColumnDefs() {\n        this._columnDefsByName.clear();\n        const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n        columnDefs.forEach(columnDef => {\n            if (this._columnDefsByName.has(columnDef.name) &&\n                (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableDuplicateColumnNameError(columnDef.name);\n            }\n            this._columnDefsByName.set(columnDef.name, columnDef);\n        });\n    }\n    /** Update the list of all available row definitions that can be used. */\n    _cacheRowDefs() {\n        this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n        this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n        this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n        // After all row definitions are determined, find the row definition to be considered default.\n        const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n        if (!this.multiTemplateDataRows &&\n            defaultRowDefs.length > 1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMultipleDefaultRowDefsError();\n        }\n        this._defaultRowDef = defaultRowDefs[0];\n    }\n    /**\n     * Check if the header, data, or footer rows have changed what columns they want to display or\n     * whether the sticky states have changed for the header or footer. If there is a diff, then\n     * re-render that section.\n     */\n    _renderUpdatedColumns() {\n        const columnsDiffReducer = (acc, def) => acc || !!def.getColumnsDiff();\n        // Force re-render data rows if the list of column definitions have changed.\n        const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n        if (dataColumnsChanged) {\n            this._forceRenderDataRows();\n        }\n        // Force re-render header/footer rows if the list of column definitions have changed.\n        const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n        if (headerColumnsChanged) {\n            this._forceRenderHeaderRows();\n        }\n        const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n        if (footerColumnsChanged) {\n            this._forceRenderFooterRows();\n        }\n        return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the row outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        this._data = [];\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n        // Stop listening for data from the previous data source.\n        if (this._renderChangeSubscription) {\n            this._renderChangeSubscription.unsubscribe();\n            this._renderChangeSubscription = null;\n        }\n        if (!dataSource) {\n            if (this._dataDiffer) {\n                this._dataDiffer.diff([]);\n            }\n            this._rowOutlet.viewContainer.clear();\n        }\n        this._dataSource = dataSource;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n        // If no data source has been set, there is nothing to observe for changes.\n        if (!this.dataSource) {\n            return;\n        }\n        let dataStream;\n        if (isDataSource(this.dataSource)) {\n            dataStream = this.dataSource.connect(this);\n        }\n        else if (isObservable(this.dataSource)) {\n            dataStream = this.dataSource;\n        }\n        else if (Array.isArray(this.dataSource)) {\n            dataStream = of(this.dataSource);\n        }\n        if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableUnknownDataSourceError();\n        }\n        this._renderChangeSubscription = dataStream\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(data => {\n            this._data = data || [];\n            this.renderRows();\n        });\n    }\n    /**\n     * Clears any existing content in the header row outlet and creates a new embedded view\n     * in the outlet using the header row definition.\n     */\n    _forceRenderHeaderRows() {\n        // Clear the header row outlet if any content exists.\n        if (this._headerRowOutlet.viewContainer.length > 0) {\n            this._headerRowOutlet.viewContainer.clear();\n        }\n        this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n        this.updateStickyHeaderRowStyles();\n    }\n    /**\n     * Clears any existing content in the footer row outlet and creates a new embedded view\n     * in the outlet using the footer row definition.\n     */\n    _forceRenderFooterRows() {\n        // Clear the footer row outlet if any content exists.\n        if (this._footerRowOutlet.viewContainer.length > 0) {\n            this._footerRowOutlet.viewContainer.clear();\n        }\n        this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n        this.updateStickyFooterRowStyles();\n    }\n    /** Adds the sticky column styles for the rows according to the columns' stick states. */\n    _addStickyColumnStyles(rows, rowDef) {\n        const columnDefs = Array.from(rowDef.columns || []).map(columnName => {\n            const columnDef = this._columnDefsByName.get(columnName);\n            if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnName);\n            }\n            return columnDef;\n        });\n        const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n        const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n        this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n    }\n    /** Gets the list of rows that have been rendered in the row outlet. */\n    _getRenderedRows(rowOutlet) {\n        const renderedRows = [];\n        for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n            const viewRef = rowOutlet.viewContainer.get(i);\n            renderedRows.push(viewRef.rootNodes[0]);\n        }\n        return renderedRows;\n    }\n    /**\n     * Get the matching row definitions that should be used for this row data. If there is only\n     * one row definition, it is returned. Otherwise, find the row definitions that has a when\n     * predicate that returns true with the data. If none return true, return the default row\n     * definition.\n     */\n    _getRowDefs(data, dataIndex) {\n        if (this._rowDefs.length == 1) {\n            return [this._rowDefs[0]];\n        }\n        let rowDefs = [];\n        if (this.multiTemplateDataRows) {\n            rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n        }\n        else {\n            let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n            if (rowDef) {\n                rowDefs.push(rowDef);\n            }\n        }\n        if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingMatchingRowDefError(data);\n        }\n        return rowDefs;\n    }\n    _getEmbeddedViewArgs(renderRow, index) {\n        const rowDef = renderRow.rowDef;\n        const context = { $implicit: renderRow.data };\n        return {\n            templateRef: rowDef.template,\n            context,\n            index,\n        };\n    }\n    /**\n     * Creates a new row template in the outlet and fills it with the set of cell templates.\n     * Optionally takes a context to provide to the row and cells, as well as an optional index\n     * of where to place the new row template in the outlet.\n     */\n    _renderRow(outlet, rowDef, index, context = {}) {\n        // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n        const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n        this._renderCellTemplateForItem(rowDef, context);\n        return view;\n    }\n    _renderCellTemplateForItem(rowDef, context) {\n        for (let cellTemplate of this._getCellTemplates(rowDef)) {\n            if (CdkCellOutlet.mostRecentCellOutlet) {\n                CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Updates the index-related context for each row to reflect any changes in the index of the rows,\n     * e.g. first/last/even/odd.\n     */\n    _updateRowIndexContext() {\n        const viewContainer = this._rowOutlet.viewContainer;\n        for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n            const viewRef = viewContainer.get(renderIndex);\n            const context = viewRef.context;\n            context.count = count;\n            context.first = renderIndex === 0;\n            context.last = renderIndex === count - 1;\n            context.even = renderIndex % 2 === 0;\n            context.odd = !context.even;\n            if (this.multiTemplateDataRows) {\n                context.dataIndex = this._renderRows[renderIndex].dataIndex;\n                context.renderIndex = renderIndex;\n            }\n            else {\n                context.index = this._renderRows[renderIndex].dataIndex;\n            }\n        }\n    }\n    /** Gets the column definitions for the provided row def. */\n    _getCellTemplates(rowDef) {\n        if (!rowDef || !rowDef.columns) {\n            return [];\n        }\n        return Array.from(rowDef.columns, columnId => {\n            const column = this._columnDefsByName.get(columnId);\n            if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnId);\n            }\n            return rowDef.extractCellTemplate(column);\n        });\n    }\n    /** Adds native table sections (e.g. tbody) and moves the row outlets into them. */\n    _applyNativeTableSections() {\n        const documentFragment = this._document.createDocumentFragment();\n        const sections = [\n            { tag: 'thead', outlets: [this._headerRowOutlet] },\n            { tag: 'tbody', outlets: [this._rowOutlet, this._noDataRowOutlet] },\n            { tag: 'tfoot', outlets: [this._footerRowOutlet] },\n        ];\n        for (const section of sections) {\n            const element = this._document.createElement(section.tag);\n            element.setAttribute('role', 'rowgroup');\n            for (const outlet of section.outlets) {\n                element.appendChild(outlet.elementRef.nativeElement);\n            }\n            documentFragment.appendChild(element);\n        }\n        // Use a DocumentFragment so we don't hit the DOM on each iteration.\n        this._elementRef.nativeElement.appendChild(documentFragment);\n    }\n    /**\n     * Forces a re-render of the data rows. Should be called in cases where there has been an input\n     * change that affects the evaluation of which rows should be rendered, e.g. toggling\n     * `multiTemplateDataRows` or adding/removing row definitions.\n     */\n    _forceRenderDataRows() {\n        this._dataDiffer.diff([]);\n        this._rowOutlet.viewContainer.clear();\n        this.renderRows();\n    }\n    /**\n     * Checks if there has been a change in sticky states since last check and applies the correct\n     * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n     * during a change detection and after the inputs are settled (after content check).\n     */\n    _checkStickyStates() {\n        const stickyCheckReducer = (acc, d) => {\n            return acc || d.hasStickyChanged();\n        };\n        // Note that the check needs to occur for every definition since it notifies the definition\n        // that it can reset its dirty state. Using another operator like `some` may short-circuit\n        // remaining definitions and leave them in an unchecked state.\n        if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyHeaderRowStyles();\n        }\n        if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyFooterRowStyles();\n        }\n        if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n            this._stickyColumnStylesNeedReset = true;\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Creates the sticky styler that will be used for sticky rows and columns. Listens\n     * for directionality changes and provides the latest direction to the styler. Re-applies column\n     * stickiness when directionality changes.\n     */\n    _setupStickyStyler() {\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener);\n        (this._dir ? this._dir.change : of())\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(value => {\n            this._stickyStyler.direction = value;\n            this.updateStickyColumnStyles();\n        });\n    }\n    /** Filters definitions that belong to this table from a QueryList. */\n    _getOwnDefs(items) {\n        return items.filter(item => !item._table || item._table === this);\n    }\n    /** Creates or removes the no data row, depending on whether any data is being shown. */\n    _updateNoDataRow() {\n        const noDataRow = this._customNoDataRow || this._noDataRow;\n        if (!noDataRow) {\n            return;\n        }\n        const shouldShow = this._rowOutlet.viewContainer.length === 0;\n        if (shouldShow === this._isShowingNoDataRow) {\n            return;\n        }\n        const container = this._noDataRowOutlet.viewContainer;\n        if (shouldShow) {\n            const view = container.createEmbeddedView(noDataRow.templateRef);\n            const rootNode = view.rootNodes[0];\n            // Only add the attributes if we have a single root node since it's hard\n            // to figure out which one to add it to when there are multiple.\n            if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n                rootNode.setAttribute('role', 'row');\n                rootNode.classList.add(noDataRow._contentClassName);\n            }\n        }\n        else {\n            container.clear();\n        }\n        this._isShowingNoDataRow = shouldShow;\n        this._changeDetectorRef.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTable, deps: [{ token: i0.IterableDiffers }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: 'role', attribute: true }, { token: i1.Directionality, optional: true }, { token: DOCUMENT }, { token: i2.Platform }, { token: _VIEW_REPEATER_STRATEGY }, { token: _COALESCED_STYLE_SCHEDULER }, { token: i3.ViewportRuler }, { token: STICKY_POSITIONING_LISTENER, optional: true, skipSelf: true }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkTable, selector: \"cdk-table, table[cdk-table]\", inputs: { trackBy: \"trackBy\", dataSource: \"dataSource\", multiTemplateDataRows: \"multiTemplateDataRows\", fixedLayout: \"fixedLayout\" }, outputs: { contentChanged: \"contentChanged\" }, host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"class.cdk-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"cdk-table\" }, providers: [\n            { provide: CDK_TABLE, useExisting: CdkTable },\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], queries: [{ propertyName: \"_noDataRow\", first: true, predicate: CdkNoDataRow, descendants: true }, { propertyName: \"_contentColumnDefs\", predicate: CdkColumnDef, descendants: true }, { propertyName: \"_contentRowDefs\", predicate: CdkRowDef, descendants: true }, { propertyName: \"_contentHeaderRowDefs\", predicate: CdkHeaderRowDef, descendants: true }, { propertyName: \"_contentFooterRowDefs\", predicate: CdkFooterRowDef, descendants: true }], viewQueries: [{ propertyName: \"_rowOutlet\", first: true, predicate: DataRowOutlet, descendants: true, static: true }, { propertyName: \"_headerRowOutlet\", first: true, predicate: HeaderRowOutlet, descendants: true, static: true }, { propertyName: \"_footerRowOutlet\", first: true, predicate: FooterRowOutlet, descendants: true, static: true }, { propertyName: \"_noDataRowOutlet\", first: true, predicate: NoDataRowOutlet, descendants: true, static: true }], exportAs: [\"cdkTable\"], ngImport: i0, template: \"\\n  <ng-content select=\\\"caption\\\"></ng-content>\\n  <ng-content select=\\\"colgroup, col\\\"></ng-content>\\n  <ng-container headerRowOutlet></ng-container>\\n  <ng-container rowOutlet></ng-container>\\n  <ng-container noDataRowOutlet></ng-container>\\n  <ng-container footerRowOutlet></ng-container>\\n\", isInline: true, styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"], dependencies: [{ kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-table, table[cdk-table]', exportAs: 'cdkTable', template: CDK_TABLE_TEMPLATE, host: {\n                        'class': 'cdk-table',\n                        '[class.cdk-table-fixed-layout]': 'fixedLayout',\n                        'ngSkipHydration': '',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        { provide: CDK_TABLE, useExisting: CdkTable },\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], styles: [\".cdk-table-fixed-layout{table-layout:fixed}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.IterableDiffers }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['role']\n                }] }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i2.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: _CoalescedStyleScheduler, decorators: [{\n                    type: Inject,\n                    args: [_COALESCED_STYLE_SCHEDULER]\n                }] }, { type: i3.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [STICKY_POSITIONING_LISTENER]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { trackBy: [{\n                type: Input\n            }], dataSource: [{\n                type: Input\n            }], multiTemplateDataRows: [{\n                type: Input\n            }], fixedLayout: [{\n                type: Input\n            }], contentChanged: [{\n                type: Output\n            }], _rowOutlet: [{\n                type: ViewChild,\n                args: [DataRowOutlet, { static: true }]\n            }], _headerRowOutlet: [{\n                type: ViewChild,\n                args: [HeaderRowOutlet, { static: true }]\n            }], _footerRowOutlet: [{\n                type: ViewChild,\n                args: [FooterRowOutlet, { static: true }]\n            }], _noDataRowOutlet: [{\n                type: ViewChild,\n                args: [NoDataRowOutlet, { static: true }]\n            }], _contentColumnDefs: [{\n                type: ContentChildren,\n                args: [CdkColumnDef, { descendants: true }]\n            }], _contentRowDefs: [{\n                type: ContentChildren,\n                args: [CdkRowDef, { descendants: true }]\n            }], _contentHeaderRowDefs: [{\n                type: ContentChildren,\n                args: [CdkHeaderRowDef, {\n                        descendants: true,\n                    }]\n            }], _contentFooterRowDefs: [{\n                type: ContentChildren,\n                args: [CdkFooterRowDef, {\n                        descendants: true,\n                    }]\n            }], _noDataRow: [{\n                type: ContentChild,\n                args: [CdkNoDataRow]\n            }] } });\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n    return array.concat(Array.from(set));\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n    /** Column name that should be used to reference this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._name = name;\n        // With Ivy, inputs can be initialized before static query results are\n        // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n        this._syncColumnDefName();\n    }\n    constructor(\n    // `CdkTextColumn` is always requiring a table, but we just assert it manually\n    // for better error reporting.\n    // tslint:disable-next-line: lightweight-tokens\n    _table, _options) {\n        this._table = _table;\n        this._options = _options;\n        /** Alignment of the cell values. */\n        this.justify = 'start';\n        this._options = _options || {};\n    }\n    ngOnInit() {\n        this._syncColumnDefName();\n        if (this.headerText === undefined) {\n            this.headerText = this._createDefaultHeaderText();\n        }\n        if (!this.dataAccessor) {\n            this.dataAccessor =\n                this._options.defaultDataAccessor || ((data, name) => data[name]);\n        }\n        if (this._table) {\n            // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n            // since the columnDef will not pick up its content by the time the table finishes checking\n            // its content and initializing the rows.\n            this.columnDef.cell = this.cell;\n            this.columnDef.headerCell = this.headerCell;\n            this._table.addColumnDef(this.columnDef);\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getTableTextColumnMissingParentTableError();\n        }\n    }\n    ngOnDestroy() {\n        if (this._table) {\n            this._table.removeColumnDef(this.columnDef);\n        }\n    }\n    /**\n     * Creates a default header text. Use the options' header text transformation function if one\n     * has been provided. Otherwise simply capitalize the column name.\n     */\n    _createDefaultHeaderText() {\n        const name = this.name;\n        if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableTextColumnMissingNameError();\n        }\n        if (this._options && this._options.defaultHeaderTextTransform) {\n            return this._options.defaultHeaderTextTransform(name);\n        }\n        return name[0].toUpperCase() + name.slice(1);\n    }\n    /** Synchronizes the column definition name with the text column name. */\n    _syncColumnDefName() {\n        if (this.columnDef) {\n            this.columnDef.name = this.name;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTextColumn, deps: [{ token: CdkTable, optional: true }, { token: TEXT_COLUMN_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkTextColumn, selector: \"cdk-text-column\", inputs: { name: \"name\", headerText: \"headerText\", dataAccessor: \"dataAccessor\", justify: \"justify\" }, viewQueries: [{ propertyName: \"columnDef\", first: true, predicate: CdkColumnDef, descendants: true, static: true }, { propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true, static: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true, static: true }], ngImport: i0, template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellDef, selector: \"[cdkCellDef]\" }, { kind: \"directive\", type: CdkHeaderCellDef, selector: \"[cdkHeaderCellDef]\" }, { kind: \"directive\", type: CdkColumnDef, selector: \"[cdkColumnDef]\", inputs: [\"sticky\", \"cdkColumnDef\", \"stickyEnd\"] }, { kind: \"directive\", type: CdkCell, selector: \"cdk-cell, td[cdk-cell]\" }, { kind: \"directive\", type: CdkHeaderCell, selector: \"cdk-header-cell, th[cdk-header-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-text-column',\n                    template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                }]\n        }], ctorParameters: function () { return [{ type: CdkTable, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TEXT_COLUMN_OPTIONS]\n                }] }]; }, propDecorators: { name: [{\n                type: Input\n            }], headerText: [{\n                type: Input\n            }], dataAccessor: [{\n                type: Input\n            }], justify: [{\n                type: Input\n            }], columnDef: [{\n                type: ViewChild,\n                args: [CdkColumnDef, { static: true }]\n            }], cell: [{\n                type: ViewChild,\n                args: [CdkCellDef, { static: true }]\n            }], headerCell: [{\n                type: ViewChild,\n                args: [CdkHeaderCellDef, { static: true }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkTable,\n    CdkRowDef,\n    CdkCellDef,\n    CdkCellOutlet,\n    CdkHeaderCellDef,\n    CdkFooterCellDef,\n    CdkColumnDef,\n    CdkCell,\n    CdkRow,\n    CdkHeaderCell,\n    CdkFooterCell,\n    CdkHeaderRow,\n    CdkHeaderRowDef,\n    CdkFooterRow,\n    CdkFooterRowDef,\n    DataRowOutlet,\n    HeaderRowOutlet,\n    FooterRowOutlet,\n    CdkTextColumn,\n    CdkNoDataRow,\n    CdkRecycleRows,\n    NoDataRowOutlet,\n];\nclass CdkTableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTableModule, declarations: [CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet], imports: [ScrollingModule], exports: [CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: EXPORTED_DECLARATIONS,\n                    declarations: EXPORTED_DECLARATIONS,\n                    imports: [ScrollingModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,YAAY,EAAEC,4BAA4B,QAAQ,0BAA0B;AAC5I,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7Q,SAASC,OAAO,EAAEC,IAAI,EAAEC,eAAe,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvE,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AALA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoDoG9B,EAAE,CAAAgC,cAAA,WAggE9B,CAAC;IAhgE2BhC,EAAE,CAAAiC,MAAA,EAkgEjG,CAAC;IAlgE8FjC,EAAE,CAAAkC,YAAA,CAkgE5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAlgEyFnC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAqC,WAAA,eAAAF,MAAA,CAAAG,OAggE/B,CAAC;IAhgE4BtC,EAAE,CAAAuC,SAAA,EAkgEjG,CAAC;IAlgE8FvC,EAAE,CAAAwC,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KAkgEjG,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlgE8F9B,EAAE,CAAAgC,cAAA,WAmgEhC,CAAC;IAngE6BhC,EAAE,CAAAiC,MAAA,EAqgEjG,CAAC;IArgE8FjC,EAAE,CAAAkC,YAAA,CAqgE5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAa,OAAA,GAAAZ,GAAA,CAAAa,SAAA;IAAA,MAAAC,MAAA,GArgEyF7C,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAqC,WAAA,eAAAQ,MAAA,CAAAP,OAmgEjC,CAAC;IAngE8BtC,EAAE,CAAAuC,SAAA,EAqgEjG,CAAC;IArgE8FvC,EAAE,CAAAwC,kBAAA,MAAAK,MAAA,CAAAC,YAAA,CAAAH,OAAA,EAAAE,MAAA,CAAAE,IAAA,MAqgEjG,CAAC;EAAA;AAAA;AAnjEN,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EAC/B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIC,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,OAAO;IACvB;IACA,IAAID,MAAMA,CAACE,CAAC,EAAE;MACV,MAAMC,SAAS,GAAG,IAAI,CAACF,OAAO;MAC9B,IAAI,CAACA,OAAO,GAAG7D,qBAAqB,CAAC8D,CAAC,CAAC;MACvC,IAAI,CAACE,iBAAiB,GAAGD,SAAS,KAAK,IAAI,CAACF,OAAO;IACvD;IACA;IACAI,gBAAgBA,CAAA,EAAG;MACf,MAAMA,gBAAgB,GAAG,IAAI,CAACD,iBAAiB;MAC/C,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,OAAOC,gBAAgB;IAC3B;IACA;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACF,iBAAiB,GAAG,KAAK;IAClC;IACAG,WAAWA,CAAC,GAAGC,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACP,OAAO,GAAG,KAAK;MACpB;MACA,IAAI,CAACG,iBAAiB,GAAG,KAAK;IAClC;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMK,SAAS,GAAG,IAAI1D,cAAc,CAAC,WAAW,CAAC;AACjD;AACA,MAAM2D,mBAAmB,GAAG,IAAI3D,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAM4D,UAAU,CAAC;EACbJ,WAAWA,CAAA,CAAC,oBAAqBK,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFJ,UAAU,EAApB7D,EAAE,CAAAkE,iBAAA,CAAoClE,EAAE,CAACmE,WAAW;IAAA,CAA4C;EAAE;EAClM;IAAS,IAAI,CAACC,IAAI,kBAD8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EACJT,UAAU;MAAAU,SAAA;IAAA,EAA2C;EAAE;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxE,EAAE,CAAAyE,iBAAA,CAGXZ,UAAU,EAAc,CAAC;IACxGS,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAe,CAAC;EACvC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAC9E;AACA;AACA;AACA;AACA,MAAMQ,gBAAgB,CAAC;EACnBlB,WAAWA,CAAA,CAAC,oBAAqBK,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAa,yBAAAX,CAAA;MAAA,YAAAA,CAAA,IAAwFU,gBAAgB,EAf1B3E,EAAE,CAAAkE,iBAAA,CAe0ClE,EAAE,CAACmE,WAAW;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAACC,IAAI,kBAhB8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAgBJK,gBAAgB;MAAAJ,SAAA;IAAA,EAAiD;EAAE;AACrK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlBoGxE,EAAE,CAAAyE,iBAAA,CAkBXE,gBAAgB,EAAc,CAAC;IAC9GL,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAqB,CAAC;EAC7C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAC9E;AACA;AACA;AACA;AACA,MAAMU,gBAAgB,CAAC;EACnBpB,WAAWA,CAAA,CAAC,oBAAqBK,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAe,yBAAAb,CAAA;MAAA,YAAAA,CAAA,IAAwFY,gBAAgB,EA9B1B7E,EAAE,CAAAkE,iBAAA,CA8B0ClE,EAAE,CAACmE,WAAW;IAAA,CAA4C;EAAE;EACxM;IAAS,IAAI,CAACC,IAAI,kBA/B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA+BJO,gBAAgB;MAAAN,SAAA;IAAA,EAAiD;EAAE;AACrK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjCoGxE,EAAE,CAAAyE,iBAAA,CAiCXI,gBAAgB,EAAc,CAAC;IAC9GP,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAqB,CAAC;EAC7C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAC9E;AACA;AACA,MAAMY,gBAAgB,CAAC;AAEvB,MAAMC,iBAAiB,GAAGhC,mBAAmB,CAAC+B,gBAAgB,CAAC;AAC/D;AACA;AACA;AACA;AACA,MAAME,YAAY,SAASD,iBAAiB,CAAC;EACzC;EACA,IAAIjC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmC,KAAK;EACrB;EACA,IAAInC,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACoC,aAAa,CAACpC,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIqC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAAChC,CAAC,EAAE;IACb,MAAMC,SAAS,GAAG,IAAI,CAACgC,UAAU;IACjC,IAAI,CAACA,UAAU,GAAG/F,qBAAqB,CAAC8D,CAAC,CAAC;IAC1C,IAAI,CAACE,iBAAiB,GAAGD,SAAS,KAAK,IAAI,CAACgC,UAAU;EAC1D;EACA5B,WAAWA,CAAC6B,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,UAAU,GAAG,KAAK;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACC,mBAAmB,GAAG,CAAE,cAAa,IAAI,CAACC,oBAAqB,EAAC,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIN,aAAaA,CAACO,KAAK,EAAE;IACjB;IACA;IACA,IAAIA,KAAK,EAAE;MACP,IAAI,CAACR,KAAK,GAAGQ,KAAK;MAClB,IAAI,CAACD,oBAAoB,GAAGC,KAAK,CAACC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC/D,IAAI,CAACJ,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACA;IAAS,IAAI,CAACxB,IAAI,YAAA6B,qBAAA3B,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,YAAY,EAjGtBjF,EAAE,CAAAkE,iBAAA,CAiGsCP,SAAS;IAAA,CAA4D;EAAE;EAC/M;IAAS,IAAI,CAACS,IAAI,kBAlG8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAkGJW,YAAY;MAAAV,SAAA;MAAAsB,cAAA,WAAAC,4BAAAhE,EAAA,EAAAC,GAAA,EAAAgE,QAAA;QAAA,IAAAjE,EAAA;UAlGV9B,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EAkGyQlC,UAAU;UAlGrR7D,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EAkGgWpB,gBAAgB;UAlGlX3E,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EAkG6blB,gBAAgB;QAAA;QAAA,IAAA/C,EAAA;UAAA,IAAAmE,EAAA;UAlG/cjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAqE,IAAA,GAAAH,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAuE,UAAA,GAAAL,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAwE,UAAA,GAAAN,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAG,MAAA;QAAAtD,MAAA;QAAAH,IAAA;QAAAqC,SAAA;MAAA;MAAAqB,QAAA,GAAFzG,EAAE,CAAA0G,kBAAA,CAkGuI,CAAC;QAAEC,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAE3B;MAAa,CAAC,CAAC,GAlG/MjF,EAAE,CAAA6G,0BAAA;IAAA,EAkG2gB;EAAE;AACnnB;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KApGoGxE,EAAE,CAAAyE,iBAAA,CAoGXQ,YAAY,EAAc,CAAC;IAC1GX,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,gBAAgB;MAC1B8B,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClBM,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAE3B;MAAa,CAAC;IACpF,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9D1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACC,SAAS;MACpB,CAAC,EAAE;QACCW,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2C,IAAI,EAAE,CAAC;MACnCuB,IAAI,EAAEjE,KAAK;MACXqD,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE0B,SAAS,EAAE,CAAC;MACZd,IAAI,EAAEjE,KAAK;MACXqD,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE0C,IAAI,EAAE,CAAC;MACP9B,IAAI,EAAEhE,YAAY;MAClBoD,IAAI,EAAE,CAACG,UAAU;IACrB,CAAC,CAAC;IAAEyC,UAAU,EAAE,CAAC;MACbhC,IAAI,EAAEhE,YAAY;MAClBoD,IAAI,EAAE,CAACiB,gBAAgB;IAC3B,CAAC,CAAC;IAAE4B,UAAU,EAAE,CAAC;MACbjC,IAAI,EAAEhE,YAAY;MAClBoD,IAAI,EAAE,CAACmB,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMoC,WAAW,CAAC;EACdxD,WAAWA,CAACyD,SAAS,EAAEC,UAAU,EAAE;IAC/BA,UAAU,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGJ,SAAS,CAAC1B,mBAAmB,CAAC;EAC5E;AACJ;AACA;AACA,MAAM+B,aAAa,SAASN,WAAW,CAAC;EACpCxD,WAAWA,CAACyD,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;EAChC;EACA;IAAS,IAAI,CAACpD,IAAI,YAAAyD,sBAAAvD,CAAA;MAAA,YAAAA,CAAA,IAAwFsD,aAAa,EA3IvBvH,EAAE,CAAAkE,iBAAA,CA2IuCe,YAAY,GA3IrDjF,EAAE,CAAAkE,iBAAA,CA2IgElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EAC7N;IAAS,IAAI,CAACrD,IAAI,kBA5I8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA4IJiD,aAAa;MAAAhD,SAAA;MAAAmD,SAAA,WAAkF,cAAc;MAAAjB,QAAA,GA5I3GzG,EAAE,CAAA6G,0BAAA;IAAA,EA4IwL;EAAE;AAChS;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KA9IoGxE,EAAE,CAAAyE,iBAAA,CA8IX8C,aAAa,EAAc,CAAC;IAC3GjD,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,sCAAsC;MAChDiD,IAAI,EAAE;QACF,OAAO,EAAE,iBAAiB;QAC1B,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErD,IAAI,EAAEW;IAAa,CAAC,EAAE;MAAEX,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG;AACA,MAAMG,aAAa,SAASX,WAAW,CAAC;EACpCxD,WAAWA,CAACyD,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;IAC5B,IAAID,SAAS,CAAC5B,MAAM,EAAEuC,WAAW,CAACT,aAAa,CAACU,QAAQ,KAAK,CAAC,EAAE;MAC5D,MAAMC,SAAS,GAAGb,SAAS,CAAC5B,MAAM,CAACuC,WAAW,CAACT,aAAa,CAACY,YAAY,CAAC,MAAM,CAAC;MACjF,MAAMC,IAAI,GAAGF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;MACnFZ,UAAU,CAACC,aAAa,CAACc,YAAY,CAAC,MAAM,EAAED,IAAI,CAAC;IACvD;EACJ;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAoE,sBAAAlE,CAAA;MAAA,YAAAA,CAAA,IAAwF2D,aAAa,EAlKvB5H,EAAE,CAAAkE,iBAAA,CAkKuCe,YAAY,GAlKrDjF,EAAE,CAAAkE,iBAAA,CAkKgElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EAC7N;IAAS,IAAI,CAACrD,IAAI,kBAnK8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAmKJsD,aAAa;MAAArD,SAAA;MAAAmD,SAAA;MAAAjB,QAAA,GAnKXzG,EAAE,CAAA6G,0BAAA;IAAA,EAmKgJ;EAAE;AACxP;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KArKoGxE,EAAE,CAAAyE,iBAAA,CAqKXmD,aAAa,EAAc,CAAC;IAC3GtD,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,sCAAsC;MAChDiD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErD,IAAI,EAAEW;IAAa,CAAC,EAAE;MAAEX,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AACrG;AACA,MAAMW,OAAO,SAASnB,WAAW,CAAC;EAC9BxD,WAAWA,CAACyD,SAAS,EAAEC,UAAU,EAAE;IAC/B,KAAK,CAACD,SAAS,EAAEC,UAAU,CAAC;IAC5B,IAAID,SAAS,CAAC5B,MAAM,EAAEuC,WAAW,CAACT,aAAa,CAACU,QAAQ,KAAK,CAAC,EAAE;MAC5D,MAAMC,SAAS,GAAGb,SAAS,CAAC5B,MAAM,CAACuC,WAAW,CAACT,aAAa,CAACY,YAAY,CAAC,MAAM,CAAC;MACjF,MAAMC,IAAI,GAAGF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;MACnFZ,UAAU,CAACC,aAAa,CAACc,YAAY,CAAC,MAAM,EAAED,IAAI,CAAC;IACvD;EACJ;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAsE,gBAAApE,CAAA;MAAA,YAAAA,CAAA,IAAwFmE,OAAO,EAxLjBpI,EAAE,CAAAkE,iBAAA,CAwLiCe,YAAY,GAxL/CjF,EAAE,CAAAkE,iBAAA,CAwL0DlE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EACvN;IAAS,IAAI,CAACrD,IAAI,kBAzL8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAyLJ8D,OAAO;MAAA7D,SAAA;MAAAmD,SAAA;MAAAjB,QAAA,GAzLLzG,EAAE,CAAA6G,0BAAA;IAAA,EAyLqH;EAAE;AAC7N;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KA3LoGxE,EAAE,CAAAyE,iBAAA,CA2LX2D,OAAO,EAAc,CAAC;IACrG9D,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,wBAAwB;MAClCiD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErD,IAAI,EAAEW;IAAa,CAAC,EAAE;MAAEX,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;;AAErG;AACA;AACA;AACA,MAAMa,SAAS,CAAC;EACZ7E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8E,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;AACJ;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAIxI,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyI,wBAAwB,CAAC;EAC3BjF,WAAWA,CAACkF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAIzH,OAAO,CAAC,CAAC;EACnC;EACA;AACJ;AACA;EACI0H,QAAQA,CAACC,IAAI,EAAE;IACX,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACJ,gBAAgB,CAACL,KAAK,CAACU,IAAI,CAACF,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIG,WAAWA,CAACH,IAAI,EAAE;IACd,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACJ,gBAAgB,CAACJ,QAAQ,CAACS,IAAI,CAACF,IAAI,CAAC;EAC7C;EACA;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAACQ,QAAQ,CAAC,CAAC;EAC9B;EACAL,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACJ,gBAAgB,EAAE;MACvB;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;IACvC,IAAI,CAACgB,sBAAsB,CAAC,CAAC,CACxBC,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACoH,UAAU,CAAC,CAAC,CAChCW,SAAS,CAAC,MAAM;MACjB,OAAO,IAAI,CAACZ,gBAAgB,CAACL,KAAK,CAACkB,MAAM,IAAI,IAAI,CAACb,gBAAgB,CAACJ,QAAQ,CAACiB,MAAM,EAAE;QAChF,MAAMX,QAAQ,GAAG,IAAI,CAACF,gBAAgB;QACtC;QACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;QACvC,KAAK,MAAMS,IAAI,IAAID,QAAQ,CAACP,KAAK,EAAE;UAC/BQ,IAAI,CAAC,CAAC;QACV;QACA,KAAK,MAAMA,IAAI,IAAID,QAAQ,CAACN,QAAQ,EAAE;UAClCO,IAAI,CAAC,CAAC;QACV;MACJ;MACA,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAChC,CAAC,CAAC;EACN;EACAU,sBAAsBA,CAAA,EAAG;IACrB;IACA;IACA,OAAO,IAAI,CAACX,OAAO,CAACe,QAAQ,GACtBrI,IAAI,CAACsI,OAAO,CAACC,OAAO,CAAC7C,SAAS,CAAC,CAAC,GAChC,IAAI,CAAC4B,OAAO,CAACkB,QAAQ,CAACN,IAAI,CAAC7H,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7C;EACA;IAAS,IAAI,CAACqC,IAAI,YAAA+F,iCAAA7F,CAAA;MAAA,YAAAA,CAAA,IAAwFyE,wBAAwB,EA9QlC1I,EAAE,CAAA+J,QAAA,CA8QkD/J,EAAE,CAACa,MAAM;IAAA,CAA6C;EAAE;EAC5M;IAAS,IAAI,CAACmJ,KAAK,kBA/Q6EhK,EAAE,CAAAiK,kBAAA;MAAAC,KAAA,EA+QYxB,wBAAwB;MAAAyB,OAAA,EAAxBzB,wBAAwB,CAAA3E;IAAA,EAAG;EAAE;AAC/I;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAjRoGxE,EAAE,CAAAyE,iBAAA,CAiRXiE,wBAAwB,EAAc,CAAC;IACtHpE,IAAI,EAAE/D;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+D,IAAI,EAAEtE,EAAE,CAACa;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEzE;AACA;AACA;AACA;AACA,MAAMuJ,gBAAgB,GAAI,6CAA4C;AACtE;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb5G,WAAWA,CAAA,CACX,oBAAqBK,QAAQ,EAAEwG,QAAQ,EAAE;IACrC,IAAI,CAACxG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwG,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACtB,MAAMC,OAAO,GAAIF,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,SAAS,CAAC,CAACG,YAAY,IAAK,EAAE;MAC7E,IAAI,CAACF,cAAc,GAAG,IAAI,CAACH,QAAQ,CAACM,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,CAAC,CAAC;MAC1D,IAAI,CAACJ,cAAc,CAACK,IAAI,CAACJ,OAAO,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACIK,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,cAAc,CAACK,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;EACjD;EACA;EACAM,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOD,MAAM,CAAC3E,UAAU,CAACxC,QAAQ;IACrC;IACA,IAAI,IAAI,YAAYqH,eAAe,EAAE;MACjC,OAAOF,MAAM,CAAC1E,UAAU,CAACzC,QAAQ;IACrC,CAAC,MACI;MACD,OAAOmH,MAAM,CAAC7E,IAAI,CAACtC,QAAQ;IAC/B;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAqH,mBAAAnH,CAAA;MAAA,YAAAA,CAAA,IAAwFoG,UAAU,EAhUpBrK,EAAE,CAAAkE,iBAAA,CAgUoClE,EAAE,CAACmE,WAAW,GAhUpDnE,EAAE,CAAAkE,iBAAA,CAgU+DlE,EAAE,CAACqL,eAAe;IAAA,CAA4C;EAAE;EACjO;IAAS,IAAI,CAACjH,IAAI,kBAjU8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAiUJ+F,UAAU;MAAA5D,QAAA,GAjURzG,EAAE,CAAAsL,oBAAA;IAAA,EAiU4C;EAAE;AACpJ;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAnUoGxE,EAAE,CAAAyE,iBAAA,CAmUX4F,UAAU,EAAc,CAAC;IACxG/F,IAAI,EAAEpE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoE,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,EAAE;MAAEG,IAAI,EAAEtE,EAAE,CAACqL;IAAgB,CAAC,CAAC;EAAE,CAAC;AAAA;AAC5G;AACA;AACA,MAAME,mBAAmB,SAASlB,UAAU,CAAC;AAE7C,MAAMmB,oBAAoB,GAAGxI,mBAAmB,CAACuI,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA,MAAML,eAAe,SAASM,oBAAoB,CAAC;EAC/C/H,WAAWA,CAACK,QAAQ,EAAEwG,QAAQ,EAAEhF,MAAM,EAAE;IACpC,KAAK,CAACxB,QAAQ,EAAEwG,QAAQ,CAAC;IACzB,IAAI,CAAChF,MAAM,GAAGA,MAAM;EACxB;EACA;EACA;EACAiF,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;IAAS,IAAI,CAACzG,IAAI,YAAA0H,wBAAAxH,CAAA;MAAA,YAAAA,CAAA,IAAwFiH,eAAe,EAzVzBlL,EAAE,CAAAkE,iBAAA,CAyVyClE,EAAE,CAACmE,WAAW,GAzVzDnE,EAAE,CAAAkE,iBAAA,CAyVoElE,EAAE,CAACqL,eAAe,GAzVxFrL,EAAE,CAAAkE,iBAAA,CAyVmGP,SAAS;IAAA,CAA4D;EAAE;EAC5Q;IAAS,IAAI,CAACS,IAAI,kBA1V8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA0VJ4G,eAAe;MAAA3G,SAAA;MAAAiC,MAAA;QAAAkE,OAAA;QAAAxH,MAAA;MAAA;MAAAuD,QAAA,GA1VbzG,EAAE,CAAA6G,0BAAA,EAAF7G,EAAE,CAAAsL,oBAAA;IAAA,EA0VyM;EAAE;AACjT;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KA5VoGxE,EAAE,CAAAyE,iBAAA,CA4VXyG,eAAe,EAAc,CAAC;IAC7G5G,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,mBAAmB;MAC7B8B,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElC,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,EAAE;MAAEG,IAAI,EAAEtE,EAAE,CAACqL;IAAgB,CAAC,EAAE;MAAE/G,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtH1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACC,SAAS;MACpB,CAAC,EAAE;QACCW,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA,MAAMsL,mBAAmB,SAASrB,UAAU,CAAC;AAE7C,MAAMsB,oBAAoB,GAAG3I,mBAAmB,CAAC0I,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA,MAAMP,eAAe,SAASQ,oBAAoB,CAAC;EAC/ClI,WAAWA,CAACK,QAAQ,EAAEwG,QAAQ,EAAEhF,MAAM,EAAE;IACpC,KAAK,CAACxB,QAAQ,EAAEwG,QAAQ,CAAC;IACzB,IAAI,CAAChF,MAAM,GAAGA,MAAM;EACxB;EACA;EACA;EACAiF,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;IAAS,IAAI,CAACzG,IAAI,YAAA6H,wBAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAwFkH,eAAe,EA3XzBnL,EAAE,CAAAkE,iBAAA,CA2XyClE,EAAE,CAACmE,WAAW,GA3XzDnE,EAAE,CAAAkE,iBAAA,CA2XoElE,EAAE,CAACqL,eAAe,GA3XxFrL,EAAE,CAAAkE,iBAAA,CA2XmGP,SAAS;IAAA,CAA4D;EAAE;EAC5Q;IAAS,IAAI,CAACS,IAAI,kBA5X8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA4XJ6G,eAAe;MAAA5G,SAAA;MAAAiC,MAAA;QAAAkE,OAAA;QAAAxH,MAAA;MAAA;MAAAuD,QAAA,GA5XbzG,EAAE,CAAA6G,0BAAA,EAAF7G,EAAE,CAAAsL,oBAAA;IAAA,EA4XyM;EAAE;AACjT;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KA9XoGxE,EAAE,CAAAyE,iBAAA,CA8XX0G,eAAe,EAAc,CAAC;IAC7G7G,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,mBAAmB;MAC7B8B,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElC,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,EAAE;MAAEG,IAAI,EAAEtE,EAAE,CAACqL;IAAgB,CAAC,EAAE;MAAE/G,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtH1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACC,SAAS;MACpB,CAAC,EAAE;QACCW,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA,MAAMyL,SAAS,SAASxB,UAAU,CAAC;EAC/B;EACA;EACA5G,WAAWA,CAACK,QAAQ,EAAEwG,QAAQ,EAAEhF,MAAM,EAAE;IACpC,KAAK,CAACxB,QAAQ,EAAEwG,QAAQ,CAAC;IACzB,IAAI,CAAChF,MAAM,GAAGA,MAAM;EACxB;EACA;IAAS,IAAI,CAACvB,IAAI,YAAA+H,kBAAA7H,CAAA;MAAA,YAAAA,CAAA,IAAwF4H,SAAS,EAtZnB7L,EAAE,CAAAkE,iBAAA,CAsZmClE,EAAE,CAACmE,WAAW,GAtZnDnE,EAAE,CAAAkE,iBAAA,CAsZ8DlE,EAAE,CAACqL,eAAe,GAtZlFrL,EAAE,CAAAkE,iBAAA,CAsZ6FP,SAAS;IAAA,CAA4D;EAAE;EACtQ;IAAS,IAAI,CAACS,IAAI,kBAvZ8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAuZJuH,SAAS;MAAAtH,SAAA;MAAAiC,MAAA;QAAAkE,OAAA;QAAAqB,IAAA;MAAA;MAAAtF,QAAA,GAvZPzG,EAAE,CAAA6G,0BAAA;IAAA,EAuZ6J;EAAE;AACrQ;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KAzZoGxE,EAAE,CAAAyE,iBAAA,CAyZXoH,SAAS,EAAc,CAAC;IACvGvH,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,aAAa;MACvB8B,MAAM,EAAE,CAAC,2BAA2B,EAAE,qBAAqB;IAC/D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElC,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,EAAE;MAAEG,IAAI,EAAEtE,EAAE,CAACqL;IAAgB,CAAC,EAAE;MAAE/G,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACtH1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACC,SAAS;MACpB,CAAC,EAAE;QACCW,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,MAAM4L,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;IAAS,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAAE;EAC3CxI,WAAWA,CAACyI,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpCF,aAAa,CAACC,oBAAoB,GAAG,IAAI;EAC7C;EACA9C,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI6C,aAAa,CAACC,oBAAoB,KAAK,IAAI,EAAE;MAC7CD,aAAa,CAACC,oBAAoB,GAAG,IAAI;IAC7C;EACJ;EACA;IAAS,IAAI,CAAClI,IAAI,YAAAoI,sBAAAlI,CAAA;MAAA,YAAAA,CAAA,IAAwF+H,aAAa,EA7bvBhM,EAAE,CAAAkE,iBAAA,CA6buClE,EAAE,CAACoM,gBAAgB;IAAA,CAA4C;EAAE;EAC1M;IAAS,IAAI,CAAChI,IAAI,kBA9b8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA8bJ0H,aAAa;MAAAzH,SAAA;IAAA,EAA8C;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhcoGxE,EAAE,CAAAyE,iBAAA,CAgcXuH,aAAa,EAAc,CAAC;IAC3G1H,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAkB,CAAC;EAC1C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACoM;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;AACnF;AACA,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAACtI,IAAI,YAAAuI,qBAAArI,CAAA;MAAA,YAAAA,CAAA,IAAwFoI,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAvc8EvM,EAAE,CAAAwM,iBAAA;MAAAlI,IAAA,EAucJ+H,YAAY;MAAA9H,SAAA;MAAAmD,SAAA,WAAgF,KAAK;MAAA+E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7I,QAAA,WAAA8I,sBAAA9K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvc/F9B,EAAE,CAAA6M,kBAAA,KAucwM,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AAC7f;AACA;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KAzcoGxE,EAAE,CAAAyE,iBAAA,CAycX4H,YAAY,EAAc,CAAC;IAC1G/H,IAAI,EAAE9D,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,oCAAoC;MAC9CZ,QAAQ,EAAEsG,gBAAgB;MAC1BzC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAqF,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACwM;IACrC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAACpJ,IAAI,YAAAqJ,qBAAAnJ,CAAA;MAAA,YAAAA,CAAA,IAAwFkJ,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACZ,IAAI,kBA3d8EvM,EAAE,CAAAwM,iBAAA;MAAAlI,IAAA,EA2dJ6I,YAAY;MAAA5I,SAAA;MAAAmD,SAAA,WAAgF,KAAK;MAAA+E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7I,QAAA,WAAAuJ,sBAAAvL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3d/F9B,EAAE,CAAA6M,kBAAA,KA2dwM,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AAC7f;AACA;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KA7doGxE,EAAE,CAAAyE,iBAAA,CA6dX0I,YAAY,EAAc,CAAC;IAC1G7I,IAAI,EAAE9D,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,oCAAoC;MAC9CZ,QAAQ,EAAEsG,gBAAgB;MAC1BzC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAqF,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACwM;IACrC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,MAAM,CAAC;EACT;IAAS,IAAI,CAACvJ,IAAI,YAAAwJ,eAAAtJ,CAAA;MAAA,YAAAA,CAAA,IAAwFqJ,MAAM;IAAA,CAAmD;EAAE;EACrK;IAAS,IAAI,CAACf,IAAI,kBA/e8EvM,EAAE,CAAAwM,iBAAA;MAAAlI,IAAA,EA+eJgJ,MAAM;MAAA/I,SAAA;MAAAmD,SAAA,WAAkE,KAAK;MAAA+E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7I,QAAA,WAAA0J,gBAAA1L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/e3E9B,EAAE,CAAA6M,kBAAA,KA+e6K,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6Dd,aAAa;MAAAe,aAAA;IAAA,EAAkI;EAAE;AACle;AACA;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KAjfoGxE,EAAE,CAAAyE,iBAAA,CAifX6I,MAAM,EAAc,CAAC;IACpGhJ,IAAI,EAAE9D,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,sBAAsB;MAChCZ,QAAQ,EAAEsG,gBAAgB;MAC1BzC,IAAI,EAAE;QACF,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAqF,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAChDF,aAAa,EAAErM,iBAAiB,CAACwM;IACrC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMO,YAAY,CAAC;EACfhK,WAAWA,CAACiK,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;EAC9C;EACA;IAAS,IAAI,CAAC5J,IAAI,YAAA6J,qBAAA3J,CAAA;MAAA,YAAAA,CAAA,IAAwFwJ,YAAY,EAtgBtBzN,EAAE,CAAAkE,iBAAA,CAsgBsClE,EAAE,CAACmE,WAAW;IAAA,CAA4C;EAAE;EACpM;IAAS,IAAI,CAACC,IAAI,kBAvgB8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAugBJmJ,YAAY;MAAAlJ,SAAA;IAAA,EAAwD;EAAE;AACxK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzgBoGxE,EAAE,CAAAyE,iBAAA,CAygBXgJ,YAAY,EAAc,CAAC;IAC1GnJ,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACmE;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE9E;AACA;AACA;AACA;AACA,MAAM0J,iBAAiB,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrK,WAAWA,CAACsK,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,UAAU,GAAG,IAAI,EAAEC,6BAA6B,GAAG,IAAI,EAAEC,iBAAiB,EAAE;IAC5J,IAAI,CAACN,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,cAAc,GAAG;MAClB,KAAK,EAAG,GAAEP,aAAc,kBAAiB;MACzC,QAAQ,EAAG,GAAEA,aAAc,qBAAoB;MAC/C,MAAM,EAAG,GAAEA,aAAc,mBAAkB;MAC3C,OAAO,EAAG,GAAEA,aAAc;IAC9B,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,sBAAsBA,CAACC,IAAI,EAAEC,gBAAgB,EAAE;IAC3C,MAAMC,eAAe,GAAG,EAAE;IAC1B,KAAK,MAAMC,GAAG,IAAIH,IAAI,EAAE;MACpB;MACA;MACA,IAAIG,GAAG,CAAC9G,QAAQ,KAAK8G,GAAG,CAACC,YAAY,EAAE;QACnC;MACJ;MACAF,eAAe,CAAC1F,IAAI,CAAC2F,GAAG,CAAC;MACzB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,QAAQ,CAACtF,MAAM,EAAEqF,CAAC,EAAE,EAAE;QAC1CH,eAAe,CAAC1F,IAAI,CAAC2F,GAAG,CAACG,QAAQ,CAACD,CAAC,CAAC,CAAC;MACzC;IACJ;IACA;IACA,IAAI,CAACZ,wBAAwB,CAACpF,QAAQ,CAAC,MAAM;MACzC,KAAK,MAAMkG,OAAO,IAAIL,eAAe,EAAE;QACnC,IAAI,CAACM,kBAAkB,CAACD,OAAO,EAAEN,gBAAgB,CAAC;MACtD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,mBAAmBA,CAACT,IAAI,EAAEU,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,GAAG,IAAI,EAAE;IACxF,IAAI,CAACZ,IAAI,CAAChF,MAAM,IACZ,CAAC,IAAI,CAAC0E,UAAU,IAChB,EAAEgB,iBAAiB,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,IAAIH,eAAe,CAACE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,CAAC,EAAE;MACnF,IAAI,IAAI,CAAClB,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACmB,oBAAoB,CAAC;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;QAC1D,IAAI,CAACpB,iBAAiB,CAACqB,uBAAuB,CAAC;UAAED,KAAK,EAAE;QAAG,CAAC,CAAC;MACjE;MACA;IACJ;IACA,MAAME,QAAQ,GAAGlB,IAAI,CAAC,CAAC,CAAC;IACxB,MAAMmB,QAAQ,GAAGD,QAAQ,CAACZ,QAAQ,CAACtF,MAAM;IACzC,MAAMoG,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,QAAQ,EAAEN,qBAAqB,CAAC;IACvE,MAAMU,cAAc,GAAG,IAAI,CAACC,8BAA8B,CAACH,UAAU,EAAEV,iBAAiB,CAAC;IACzF,MAAMc,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAACL,UAAU,EAAET,eAAe,CAAC;IACnF,MAAMe,eAAe,GAAGhB,iBAAiB,CAACiB,WAAW,CAAC,IAAI,CAAC;IAC3D,MAAMC,cAAc,GAAGjB,eAAe,CAACkB,OAAO,CAAC,IAAI,CAAC;IACpD;IACA,IAAI,CAACpC,wBAAwB,CAACpF,QAAQ,CAAC,MAAM;MACzC,MAAMyH,KAAK,GAAG,IAAI,CAACtC,SAAS,KAAK,KAAK;MACtC,MAAMuC,KAAK,GAAGD,KAAK,GAAG,OAAO,GAAG,MAAM;MACtC,MAAME,GAAG,GAAGF,KAAK,GAAG,MAAM,GAAG,OAAO;MACpC,KAAK,MAAM3B,GAAG,IAAIH,IAAI,EAAE;QACpB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,QAAQ,EAAEd,CAAC,EAAE,EAAE;UAC/B,MAAM1I,IAAI,GAAGwI,GAAG,CAACG,QAAQ,CAACD,CAAC,CAAC;UAC5B,IAAIK,iBAAiB,CAACL,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC4B,eAAe,CAACtK,IAAI,EAAEoK,KAAK,EAAET,cAAc,CAACjB,CAAC,CAAC,EAAEA,CAAC,KAAKqB,eAAe,CAAC;UAC/E;UACA,IAAIf,eAAe,CAACN,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC4B,eAAe,CAACtK,IAAI,EAAEqK,GAAG,EAAER,YAAY,CAACnB,CAAC,CAAC,EAAEA,CAAC,KAAKuB,cAAc,CAAC;UAC1E;QACJ;MACJ;MACA,IAAI,IAAI,CAAChC,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACmB,oBAAoB,CAAC;UACxCC,KAAK,EAAEU,eAAe,KAAK,CAAC,CAAC,GACvB,EAAE,GACFN,UAAU,CACPc,KAAK,CAAC,CAAC,EAAER,eAAe,GAAG,CAAC,CAAC,CAC7BS,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAM3B,iBAAiB,CAAC2B,KAAK,CAAC,GAAGD,KAAK,GAAG,IAAK;QAC5E,CAAC,CAAC;QACF,IAAI,CAACxC,iBAAiB,CAACqB,uBAAuB,CAAC;UAC3CD,KAAK,EAAEY,cAAc,KAAK,CAAC,CAAC,GACtB,EAAE,GACFR,UAAU,CACPc,KAAK,CAACN,cAAc,CAAC,CACrBO,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAM1B,eAAe,CAAC0B,KAAK,GAAGT,cAAc,CAAC,GAAGQ,KAAK,GAAG,IAAK,CAAC,CAC/EE,OAAO,CAAC;QACrB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IAC3C;IACA,IAAI,CAAC,IAAI,CAAChD,UAAU,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,MAAMM,IAAI,GAAG0C,QAAQ,KAAK,QAAQ,GAAGF,WAAW,CAACN,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGE,WAAW;IAChF,MAAMG,MAAM,GAAGD,QAAQ,KAAK,QAAQ,GAAGD,YAAY,CAACP,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGG,YAAY;IACpF;IACA,MAAMG,aAAa,GAAG,EAAE;IACxB,MAAMC,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,eAAe,GAAG,EAAE;IAC1B,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,QAAQ,GAAG/C,IAAI,CAAChF,MAAM,EAAE+H,QAAQ,EAAE,EAAE;MACzE,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;QACnB;MACJ;MACAH,aAAa,CAACG,QAAQ,CAAC,GAAGC,YAAY;MACtC,MAAM7C,GAAG,GAAGH,IAAI,CAAC+C,QAAQ,CAAC;MAC1BD,eAAe,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACzD,kBAAkB,GAC7C2D,KAAK,CAACrQ,IAAI,CAACuN,GAAG,CAACG,QAAQ,CAAC,GACxB,CAACH,GAAG,CAAC;MACX,MAAM+C,MAAM,GAAG/C,GAAG,CAACgD,qBAAqB,CAAC,CAAC,CAACD,MAAM;MACjDF,YAAY,IAAIE,MAAM;MACtBL,iBAAiB,CAACE,QAAQ,CAAC,GAAGG,MAAM;IACxC;IACA,MAAME,gBAAgB,GAAGT,MAAM,CAAChB,WAAW,CAAC,IAAI,CAAC;IACjD;IACA;IACA,IAAI,CAAClC,wBAAwB,CAACpF,QAAQ,CAAC,MAAM;MACzC,KAAK,IAAI0I,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG/C,IAAI,CAAChF,MAAM,EAAE+H,QAAQ,EAAE,EAAE;QACvD,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;UACnB;QACJ;QACA,MAAMM,MAAM,GAAGT,aAAa,CAACG,QAAQ,CAAC;QACtC,MAAMO,kBAAkB,GAAGP,QAAQ,KAAKK,gBAAgB;QACxD,KAAK,MAAM7C,OAAO,IAAIuC,eAAe,CAACC,QAAQ,CAAC,EAAE;UAC7C,IAAI,CAACd,eAAe,CAAC1B,OAAO,EAAEmC,QAAQ,EAAEW,MAAM,EAAEC,kBAAkB,CAAC;QACvE;MACJ;MACA,IAAIZ,QAAQ,KAAK,KAAK,EAAE;QACpB,IAAI,CAAC9C,iBAAiB,EAAE2D,uBAAuB,CAAC;UAC5CvC,KAAK,EAAE6B,iBAAiB;UACxBW,OAAO,EAAEZ,aAAa;UACtBa,QAAQ,EAAEX;QACd,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAAClD,iBAAiB,EAAE8D,uBAAuB,CAAC;UAC5C1C,KAAK,EAAE6B,iBAAiB;UACxBW,OAAO,EAAEZ,aAAa;UACtBa,QAAQ,EAAEX;QACd,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,2BAA2BA,CAACC,YAAY,EAAEnB,YAAY,EAAE;IACpD,IAAI,CAAC,IAAI,CAACnD,kBAAkB,EAAE;MAC1B;IACJ;IACA,MAAMuE,KAAK,GAAGD,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;IACjD;IACA,IAAI,CAACrE,wBAAwB,CAACpF,QAAQ,CAAC,MAAM;MACzC,IAAIoI,YAAY,CAAC5B,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAAC,EAAE;QACpC,IAAI,CAACN,kBAAkB,CAACqD,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;MAC9C,CAAC,MACI;QACD,IAAI,CAAC5B,eAAe,CAAC4B,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIrD,kBAAkBA,CAACD,OAAO,EAAEN,gBAAgB,EAAE;IAC1C,KAAK,MAAM8D,GAAG,IAAI9D,gBAAgB,EAAE;MAChCM,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,GAAG,EAAE;MACvBxD,OAAO,CAAC3H,SAAS,CAACqL,MAAM,CAAC,IAAI,CAACnE,cAAc,CAACiE,GAAG,CAAC,CAAC;IACtD;IACA;IACA;IACA;IACA;IACA,MAAMG,YAAY,GAAG9E,iBAAiB,CAACyB,IAAI,CAACkD,GAAG,IAAI9D,gBAAgB,CAAC4B,OAAO,CAACkC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIxD,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,CAAC;IAC9G,IAAIG,YAAY,EAAE;MACd3D,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC7D,OAAO,CAAC;IAC7D,CAAC,MACI;MACD;MACAA,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,EAAE;MACzB,IAAI,IAAI,CAACxE,6BAA6B,EAAE;QACpCY,OAAO,CAACyD,KAAK,CAACtB,QAAQ,GAAG,EAAE;MAC/B;MACAnC,OAAO,CAAC3H,SAAS,CAACqL,MAAM,CAAC,IAAI,CAAC1E,aAAa,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0C,eAAeA,CAAC1B,OAAO,EAAEwD,GAAG,EAAEM,QAAQ,EAAEC,eAAe,EAAE;IACrD/D,OAAO,CAAC3H,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC0G,aAAa,CAAC;IACzC,IAAI+E,eAAe,EAAE;MACjB/D,OAAO,CAAC3H,SAAS,CAACC,GAAG,CAAC,IAAI,CAACiH,cAAc,CAACiE,GAAG,CAAC,CAAC;IACnD;IACAxD,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,GAAI,GAAEM,QAAS,IAAG;IACpC9D,OAAO,CAACyD,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC7D,OAAO,CAAC;IACzD,IAAI,IAAI,CAACZ,6BAA6B,EAAE;MACpCY,OAAO,CAACyD,KAAK,CAACO,OAAO,IAAI,8CAA8C;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,oBAAoBA,CAAC7D,OAAO,EAAE;IAC1B,MAAMiE,gBAAgB,GAAG;MACrBC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACX,CAAC;IACD,IAAIT,MAAM,GAAG,CAAC;IACd;IACA;IACA;IACA,KAAK,MAAMJ,GAAG,IAAI3E,iBAAiB,EAAE;MACjC,IAAImB,OAAO,CAACyD,KAAK,CAACD,GAAG,CAAC,EAAE;QACpBI,MAAM,IAAIK,gBAAgB,CAACT,GAAG,CAAC;MACnC;IACJ;IACA,OAAOI,MAAM,GAAI,GAAEA,MAAO,EAAC,GAAG,EAAE;EACpC;EACA;EACA9C,cAAcA,CAAClB,GAAG,EAAES,qBAAqB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAACf,iBAAiB,CAAC7E,MAAM,EAAE;MACzD,OAAO,IAAI,CAAC6E,iBAAiB;IACjC;IACA,MAAMuB,UAAU,GAAG,EAAE;IACrB,MAAMyD,aAAa,GAAG1E,GAAG,CAACG,QAAQ;IAClC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,aAAa,CAAC7J,MAAM,EAAEqF,CAAC,EAAE,EAAE;MAC3C,IAAI1I,IAAI,GAAGkN,aAAa,CAACxE,CAAC,CAAC;MAC3Be,UAAU,CAAC5G,IAAI,CAAC7C,IAAI,CAACwL,qBAAqB,CAAC,CAAC,CAACf,KAAK,CAAC;IACvD;IACA,IAAI,CAACvC,iBAAiB,GAAGuB,UAAU;IACnC,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIG,8BAA8BA,CAACuD,MAAM,EAAErC,YAAY,EAAE;IACjD,MAAMsC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,MAAM,CAAC9J,MAAM,EAAEqF,CAAC,EAAE,EAAE;MACpC,IAAIoC,YAAY,CAACpC,CAAC,CAAC,EAAE;QACjB0E,SAAS,CAAC1E,CAAC,CAAC,GAAG2E,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACzE,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO0E,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;EACItD,4BAA4BA,CAACqD,MAAM,EAAErC,YAAY,EAAE;IAC/C,MAAMsC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI3E,CAAC,GAAGyE,MAAM,CAAC9J,MAAM,EAAEqF,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,IAAIoC,YAAY,CAACpC,CAAC,CAAC,EAAE;QACjB0E,SAAS,CAAC1E,CAAC,CAAC,GAAG2E,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACzE,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO0E,SAAS;EACpB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAACC,EAAE,EAAE;EACpC,OAAOC,KAAK,CAAE,kCAAiCD,EAAG,IAAG,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAAC9Q,IAAI,EAAE;EAC5C,OAAO6Q,KAAK,CAAE,+CAA8C7Q,IAAK,IAAG,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,SAAS+Q,mCAAmCA,CAAA,EAAG;EAC3C,OAAOF,KAAK,CAAE,sEAAqE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASG,kCAAkCA,CAACC,IAAI,EAAE;EAC9C,OAAOJ,KAAK,CAAE,kDAAiD,GAC1D,sBAAqBK,IAAI,CAACC,SAAS,CAACF,IAAI,CAAE,EAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,OAAOP,KAAK,CAAC,mDAAmD,GAC5D,oDAAoD,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASQ,8BAA8BA,CAAA,EAAG;EACtC,OAAOR,KAAK,CAAE,wEAAuE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA,SAASS,yCAAyCA,CAAA,EAAG;EACjD,OAAOT,KAAK,CAAE,6DAA4D,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,SAASU,kCAAkCA,CAAA,EAAG;EAC1C,OAAOV,KAAK,CAAE,qCAAoC,CAAC;AACvD;;AAEA;AACA,MAAMW,2BAA2B,GAAG,IAAItU,cAAc,CAAC,SAAS,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMuU,cAAc,CAAC;EACjB;IAAS,IAAI,CAACzQ,IAAI,YAAA0Q,uBAAAxQ,CAAA;MAAA,YAAAA,CAAA,IAAwFuQ,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACpQ,IAAI,kBAv6B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EAu6BJkQ,cAAc;MAAAjQ,SAAA;MAAAkC,QAAA,GAv6BZzG,EAAE,CAAA0G,kBAAA,CAu6B0F,CAAC;QAAEC,OAAO,EAAEpH,uBAAuB;QAAEmV,QAAQ,EAAElV;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACjS;AACA;EAAA,QAAAgF,SAAA,oBAAAA,SAAA,KAz6BoGxE,EAAE,CAAAyE,iBAAA,CAy6BX+P,cAAc,EAAc,CAAC;IAC5GlQ,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,uDAAuD;MACjEoC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEpH,uBAAuB;QAAEmV,QAAQ,EAAElV;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMmV,aAAa,CAAC;EAChBlR,WAAWA,CAACmR,aAAa,EAAEzN,UAAU,EAAE;IACnC,IAAI,CAACyN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzN,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACpD,IAAI,YAAA8Q,sBAAA5Q,CAAA;MAAA,YAAAA,CAAA,IAAwF0Q,aAAa,EAz7BvB3U,EAAE,CAAAkE,iBAAA,CAy7BuClE,EAAE,CAACoM,gBAAgB,GAz7B5DpM,EAAE,CAAAkE,iBAAA,CAy7BuElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAACrD,IAAI,kBA17B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA07BJqQ,aAAa;MAAApQ,SAAA;IAAA,EAA0C;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA57BoGxE,EAAE,CAAAyE,iBAAA,CA47BXkQ,aAAa,EAAc,CAAC;IAC3GrQ,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAc,CAAC;EACtC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACoM;IAAiB,CAAC,EAAE;MAAE9H,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC5G;AACA;AACA;AACA;AACA,MAAMqN,eAAe,CAAC;EAClBrR,WAAWA,CAACmR,aAAa,EAAEzN,UAAU,EAAE;IACnC,IAAI,CAACyN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzN,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACpD,IAAI,YAAAgR,wBAAA9Q,CAAA;MAAA,YAAAA,CAAA,IAAwF6Q,eAAe,EAz8BzB9U,EAAE,CAAAkE,iBAAA,CAy8ByClE,EAAE,CAACoM,gBAAgB,GAz8B9DpM,EAAE,CAAAkE,iBAAA,CAy8ByElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAACrD,IAAI,kBA18B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA08BJwQ,eAAe;MAAAvQ,SAAA;IAAA,EAAgD;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA58BoGxE,EAAE,CAAAyE,iBAAA,CA48BXqQ,eAAe,EAAc,CAAC;IAC7GxQ,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAoB,CAAC;EAC5C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACoM;IAAiB,CAAC,EAAE;MAAE9H,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC5G;AACA;AACA;AACA;AACA,MAAMuN,eAAe,CAAC;EAClBvR,WAAWA,CAACmR,aAAa,EAAEzN,UAAU,EAAE;IACnC,IAAI,CAACyN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzN,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACpD,IAAI,YAAAkR,wBAAAhR,CAAA;MAAA,YAAAA,CAAA,IAAwF+Q,eAAe,EAz9BzBhV,EAAE,CAAAkE,iBAAA,CAy9ByClE,EAAE,CAACoM,gBAAgB,GAz9B9DpM,EAAE,CAAAkE,iBAAA,CAy9ByElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAACrD,IAAI,kBA19B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA09BJ0Q,eAAe;MAAAzQ,SAAA;IAAA,EAAgD;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA59BoGxE,EAAE,CAAAyE,iBAAA,CA49BXuQ,eAAe,EAAc,CAAC;IAC7G1Q,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAoB,CAAC;EAC5C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACoM;IAAiB,CAAC,EAAE;MAAE9H,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC5G;AACA;AACA;AACA;AACA;AACA,MAAMyN,eAAe,CAAC;EAClBzR,WAAWA,CAACmR,aAAa,EAAEzN,UAAU,EAAE;IACnC,IAAI,CAACyN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzN,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACpD,IAAI,YAAAoR,wBAAAlR,CAAA;MAAA,YAAAA,CAAA,IAAwFiR,eAAe,EA1+BzBlV,EAAE,CAAAkE,iBAAA,CA0+ByClE,EAAE,CAACoM,gBAAgB,GA1+B9DpM,EAAE,CAAAkE,iBAAA,CA0+ByElE,EAAE,CAACyH,UAAU;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAACrD,IAAI,kBA3+B8EpE,EAAE,CAAAqE,iBAAA;MAAAC,IAAA,EA2+BJ4Q,eAAe;MAAA3Q,SAAA;IAAA,EAAgD;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7+BoGxE,EAAE,CAAAyE,iBAAA,CA6+BXyQ,eAAe,EAAc,CAAC;IAC7G5Q,IAAI,EAAEpE,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE;IAAoB,CAAC;EAC5C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAEtE,EAAE,CAACoM;IAAiB,CAAC,EAAE;MAAE9H,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC5G;AACA;AACA;AACA;AACA;AACA,MAAM2N,kBAAkB;AACxB;AACA;AACC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAAS1U,eAAe,CAAC;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2U,QAAQ,CAAC;EACX;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,OAAOA,CAACE,EAAE,EAAE;IACZ,IAAI,CAAC,OAAOjR,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKiR,EAAE,IAAI,IAAI,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC3FC,OAAO,CAACC,IAAI,CAAE,4CAA2C1B,IAAI,CAACC,SAAS,CAACuB,EAAE,CAAE,GAAE,CAAC;IACnF;IACA,IAAI,CAACD,UAAU,GAAGC,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAAC3S,CAAC,EAAE;IACzB,IAAI,CAAC4S,sBAAsB,GAAG1W,qBAAqB,CAAC8D,CAAC,CAAC;IACtD;IACA;IACA,IAAI,IAAI,CAAC6S,UAAU,IAAI,IAAI,CAACA,UAAU,CAACrB,aAAa,CAACnL,MAAM,EAAE;MACzD,IAAI,CAACyM,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAChT,CAAC,EAAE;IACf,IAAI,CAACiT,YAAY,GAAG/W,qBAAqB,CAAC8D,CAAC,CAAC;IAC5C;IACA,IAAI,CAACkT,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,4BAA4B,GAAG,IAAI;EAC5C;EACA9S,WAAWA,CAAC6G,QAAQ,EAAEkM,kBAAkB,EAAE3O,WAAW,EAAEI,IAAI,EAAEwO,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE1I,wBAAwB,EAAE2I,cAAc;EAChJ;AACJ;AACA;AACA;EACIC,0BAA0B;EAC1B;AACJ;AACA;AACA;EACInO,OAAO,EAAE;IACL,IAAI,CAAC2B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACkM,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC3O,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC4O,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1I,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAAC2I,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACnO,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACoO,UAAU,GAAG,IAAI3V,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4V,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACE,oBAAoB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,oBAAoB,GAAG,IAAIH,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACI,oBAAoB,GAAG,IAAI;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACjB,4BAA4B,GAAG,IAAI;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACD,2BAA2B,GAAG,IAAI;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmB,oBAAoB,GAAG,IAAIR,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACS,cAAc,GAAG,kBAAkB;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,4BAA4B,GAAG,IAAI;IACxC;IACA,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC5B,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,YAAY,GAAG,KAAK;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACwB,cAAc,GAAG,IAAIjX,YAAY,CAAC,CAAC;IACxC;IACA;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkX,UAAU,GAAG,IAAIxW,eAAe,CAAC;MAClCkP,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEsH,MAAM,CAACC;IAChB,CAAC,CAAC;IACF,IAAI,CAAC/P,IAAI,EAAE;MACP,IAAI,CAACJ,WAAW,CAACT,aAAa,CAACc,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;IAChE;IACA,IAAI,CAACwO,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC3I,kBAAkB,GAAG,IAAI,CAAClG,WAAW,CAACT,aAAa,CAAC6Q,QAAQ,KAAK,OAAO;EACjF;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,IAAI,CAACpK,kBAAkB,EAAE;MACzB,IAAI,CAACqK,yBAAyB,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC/N,QAAQ,CAACM,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACyN,EAAE,EAAEC,OAAO,KAAK;MAC9D,OAAO,IAAI,CAAChD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgD,OAAO,CAACC,SAAS,EAAED,OAAO,CAACvE,IAAI,CAAC,GAAGuE,OAAO;IACjF,CAAC,CAAC;IACF,IAAI,CAAC1B,cAAc,CACd4B,MAAM,CAAC,CAAC,CACRlP,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACsV,UAAU,CAAC,CAAC,CAChCvN,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC8M,2BAA2B,GAAG,IAAI;IAC3C,CAAC,CAAC;EACN;EACAoC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC,IAAI,CAACC,cAAc,CAACpP,MAAM,IAC3B,CAAC,IAAI,CAACqP,cAAc,CAACrP,MAAM,IAC3B,CAAC,IAAI,CAACsP,QAAQ,CAACtP,MAAM,KACpB,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM2P,2BAA2B,CAAC,CAAC;IACvC;IACA;IACA,MAAM6E,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACnD,MAAMC,cAAc,GAAGF,cAAc,IAAI,IAAI,CAACzB,oBAAoB,IAAI,IAAI,CAACC,oBAAoB;IAC/F;IACA,IAAI,CAACjB,4BAA4B,GAAG,IAAI,CAACA,4BAA4B,IAAI2C,cAAc;IACvF,IAAI,CAAC5C,2BAA2B,GAAG4C,cAAc;IACjD;IACA,IAAI,IAAI,CAAC3B,oBAAoB,EAAE;MAC3B,IAAI,CAAC4B,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC5B,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAAC4B,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC5B,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA;IACA,IAAI,IAAI,CAAC5B,UAAU,IAAI,IAAI,CAACmD,QAAQ,CAACtP,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC4P,yBAAyB,EAAE;MAChF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,IAAI,CAAC/C,4BAA4B,EAAE;MACxC;MACA;MACA,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAACoD,kBAAkB,CAAC,CAAC;EAC7B;EACApQ,WAAWA,CAAA,EAAG;IACV,CACI,IAAI,CAAC8M,UAAU,CAACrB,aAAa,EAC7B,IAAI,CAAC4E,gBAAgB,CAAC5E,aAAa,EACnC,IAAI,CAAC6E,gBAAgB,CAAC7E,aAAa,EACnC,IAAI,CAAC6C,oBAAoB,EACzB,IAAI,CAACP,iBAAiB,EACtB,IAAI,CAACE,cAAc,EACnB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACN,iBAAiB,CACzB,CAAC0C,OAAO,CAACC,GAAG,IAAI;MACbA,GAAG,CAACC,KAAK,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAI,CAACf,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACe,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC9C,UAAU,CAAC3N,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC2N,UAAU,CAAC1N,QAAQ,CAAC,CAAC;IAC1B,IAAI5J,YAAY,CAAC,IAAI,CAACmW,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAACkE,UAAU,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC3C,MAAMzP,OAAO,GAAG,IAAI,CAAC6N,WAAW,CAACvN,IAAI,CAAC,IAAI,CAACkP,WAAW,CAAC;IACvD,IAAI,CAACxP,OAAO,EAAE;MACV,IAAI,CAAC0P,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACrC,cAAc,CAACzO,IAAI,CAAC,CAAC;MAC1B;IACJ;IACA,MAAMwL,aAAa,GAAG,IAAI,CAACqB,UAAU,CAACrB,aAAa;IACnD,IAAI,CAACgC,aAAa,CAACuD,YAAY,CAAC3P,OAAO,EAAEoK,aAAa,EAAE,CAACwF,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,CAACI,IAAI,EAAEF,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACI,IAAI,CAACxG,IAAI,EAAGyE,MAAM,IAAK;MACpM,IAAIA,MAAM,CAACgC,SAAS,KAAK,CAAC,CAAC,yCAAyChC,MAAM,CAACiC,OAAO,EAAE;QAChF,IAAI,CAACC,0BAA0B,CAAClC,MAAM,CAAC2B,MAAM,CAACI,IAAI,CAACI,MAAM,EAAEnC,MAAM,CAACiC,OAAO,CAAC;MAC9E;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACG,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACArQ,OAAO,CAACsQ,qBAAqB,CAAEV,MAAM,IAAK;MACtC,MAAMW,OAAO,GAAGnG,aAAa,CAACoG,GAAG,CAACZ,MAAM,CAACE,YAAY,CAAC;MACtDS,OAAO,CAACL,OAAO,CAAC9X,SAAS,GAAGwX,MAAM,CAACI,IAAI,CAACxG,IAAI;IAChD,CAAC,CAAC;IACF,IAAI,CAACkG,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,IAAI,IAAI,CAACvR,OAAO,IAAI9H,MAAM,CAACoa,eAAe,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACtS,OAAO,CAACkB,QAAQ,CAACN,IAAI,CAAC7H,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAACsV,UAAU,CAAC,CAAC,CAACvN,SAAS,CAAC,MAAM;QAC5E,IAAI,CAAC2M,wBAAwB,CAAC,CAAC;MACnC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACA,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAAC0B,cAAc,CAACzO,IAAI,CAAC,CAAC;EAC9B;EACA;EACA8R,YAAYA,CAAChU,SAAS,EAAE;IACpB,IAAI,CAACgQ,iBAAiB,CAAC5P,GAAG,CAACJ,SAAS,CAAC;EACzC;EACA;EACAiU,eAAeA,CAACjU,SAAS,EAAE;IACvB,IAAI,CAACgQ,iBAAiB,CAACkE,MAAM,CAAClU,SAAS,CAAC;EAC5C;EACA;EACAmU,SAASA,CAACT,MAAM,EAAE;IACd,IAAI,CAACxD,cAAc,CAAC9P,GAAG,CAACsT,MAAM,CAAC;EACnC;EACA;EACAU,YAAYA,CAACV,MAAM,EAAE;IACjB,IAAI,CAACxD,cAAc,CAACgE,MAAM,CAACR,MAAM,CAAC;EACtC;EACA;EACAW,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACnE,oBAAoB,CAAC/P,GAAG,CAACkU,YAAY,CAAC;IAC3C,IAAI,CAACjE,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAkE,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAACnE,oBAAoB,CAAC+D,MAAM,CAACI,YAAY,CAAC;IAC9C,IAAI,CAACjE,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAmE,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACrE,oBAAoB,CAAChQ,GAAG,CAACqU,YAAY,CAAC;IAC3C,IAAI,CAACnE,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAoE,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAACrE,oBAAoB,CAAC8D,MAAM,CAACO,YAAY,CAAC;IAC9C,IAAI,CAACnE,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAqE,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAGD,SAAS;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC1C,gBAAgB,CAAC;IAC/D,MAAMnH,YAAY,GAAG,IAAI,CAACxK,WAAW,CAACT,aAAa;IACnD;IACA;IACA;IACA,MAAM+U,KAAK,GAAG9J,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;IACjD,IAAI4J,KAAK,EAAE;MACPA,KAAK,CAAC1J,KAAK,CAAC2J,OAAO,GAAGH,UAAU,CAACxS,MAAM,GAAG,EAAE,GAAG,MAAM;IACzD;IACA,MAAMyH,YAAY,GAAG,IAAI,CAAC2H,cAAc,CAACjI,GAAG,CAAC+I,GAAG,IAAIA,GAAG,CAACzW,MAAM,CAAC;IAC/D,IAAI,CAACmZ,aAAa,CAAC7N,sBAAsB,CAACyN,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACI,aAAa,CAACrL,SAAS,CAACiL,UAAU,EAAE/K,YAAY,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAAC2H,cAAc,CAACa,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACnW,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8Y,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAACzC,gBAAgB,CAAC;IAC/D,MAAMpH,YAAY,GAAG,IAAI,CAACxK,WAAW,CAACT,aAAa;IACnD;IACA;IACA;IACA,MAAMkL,KAAK,GAAGD,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;IACjD,IAAID,KAAK,EAAE;MACPA,KAAK,CAACG,KAAK,CAAC2J,OAAO,GAAGG,UAAU,CAAC9S,MAAM,GAAG,EAAE,GAAG,MAAM;IACzD;IACA,MAAMyH,YAAY,GAAG,IAAI,CAAC4H,cAAc,CAAClI,GAAG,CAAC+I,GAAG,IAAIA,GAAG,CAACzW,MAAM,CAAC;IAC/D,IAAI,CAACmZ,aAAa,CAAC7N,sBAAsB,CAAC+N,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjE,IAAI,CAACF,aAAa,CAACrL,SAAS,CAACuL,UAAU,EAAErL,YAAY,EAAE,QAAQ,CAAC;IAChE,IAAI,CAACmL,aAAa,CAACjK,2BAA2B,CAAC,IAAI,CAACvK,WAAW,CAACT,aAAa,EAAE8J,YAAY,CAAC;IAC5F;IACA,IAAI,CAAC4H,cAAc,CAACY,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACnW,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2S,wBAAwBA,CAAA,EAAG;IACvB,MAAM8F,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC1C,gBAAgB,CAAC;IAC/D,MAAMgD,QAAQ,GAAG,IAAI,CAACN,gBAAgB,CAAC,IAAI,CAACjG,UAAU,CAAC;IACvD,MAAMsG,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAACzC,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA;IACA,IAAK,IAAI,CAAC1L,kBAAkB,IAAI,CAAC,IAAI,CAACsI,YAAY,IAAK,IAAI,CAACE,4BAA4B,EAAE;MACtF;MACA;MACA,IAAI,CAAC8F,aAAa,CAAC7N,sBAAsB,CAAC,CAAC,GAAGyN,UAAU,EAAE,GAAGO,QAAQ,EAAE,GAAGD,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MACzG,IAAI,CAAChG,4BAA4B,GAAG,KAAK;IAC7C;IACA;IACA0F,UAAU,CAACvC,OAAO,CAAC,CAAC+C,SAAS,EAAE3N,CAAC,KAAK;MACjC,IAAI,CAAC4N,sBAAsB,CAAC,CAACD,SAAS,CAAC,EAAE,IAAI,CAAC5D,cAAc,CAAC/J,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA,IAAI,CAACiK,QAAQ,CAACW,OAAO,CAACkB,MAAM,IAAI;MAC5B;MACA,MAAMnM,IAAI,GAAG,EAAE;MACf,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0N,QAAQ,CAAC/S,MAAM,EAAEqF,CAAC,EAAE,EAAE;QACtC,IAAI,IAAI,CAACkL,WAAW,CAAClL,CAAC,CAAC,CAAC8L,MAAM,KAAKA,MAAM,EAAE;UACvCnM,IAAI,CAACxF,IAAI,CAACuT,QAAQ,CAAC1N,CAAC,CAAC,CAAC;QAC1B;MACJ;MACA,IAAI,CAAC4N,sBAAsB,CAACjO,IAAI,EAAEmM,MAAM,CAAC;IAC7C,CAAC,CAAC;IACF;IACA2B,UAAU,CAAC7C,OAAO,CAAC,CAACiD,SAAS,EAAE7N,CAAC,KAAK;MACjC,IAAI,CAAC4N,sBAAsB,CAAC,CAACC,SAAS,CAAC,EAAE,IAAI,CAAC7D,cAAc,CAAChK,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA4C,KAAK,CAACrQ,IAAI,CAAC,IAAI,CAAC2V,iBAAiB,CAAC4F,MAAM,CAAC,CAAC,CAAC,CAAClD,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACnW,kBAAkB,CAAC,CAAC,CAAC;EACxF;EACA;AACJ;AACA;AACA;AACA;EACIyW,iBAAiBA,CAAA,EAAG;IAChB,MAAMF,UAAU,GAAG,EAAE;IACrB;IACA;IACA,MAAM8C,oBAAoB,GAAG,IAAI,CAACpF,oBAAoB;IACtD,IAAI,CAACA,oBAAoB,GAAG,IAAIR,GAAG,CAAC,CAAC;IACrC;IACA;IACA,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgO,KAAK,CAACrT,MAAM,EAAEqF,CAAC,EAAE,EAAE;MACxC,IAAIkF,IAAI,GAAG,IAAI,CAAC8I,KAAK,CAAChO,CAAC,CAAC;MACxB,MAAMiO,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAChJ,IAAI,EAAElF,CAAC,EAAE+N,oBAAoB,CAAC7B,GAAG,CAAChH,IAAI,CAAC,CAAC;MAC7F,IAAI,CAAC,IAAI,CAACyD,oBAAoB,CAACwF,GAAG,CAACjJ,IAAI,CAAC,EAAE;QACtC,IAAI,CAACyD,oBAAoB,CAACyF,GAAG,CAAClJ,IAAI,EAAE,IAAImJ,OAAO,CAAC,CAAC,CAAC;MACtD;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,iBAAiB,CAACtT,MAAM,EAAE2T,CAAC,EAAE,EAAE;QAC/C,IAAIC,SAAS,GAAGN,iBAAiB,CAACK,CAAC,CAAC;QACpC,MAAME,KAAK,GAAG,IAAI,CAAC7F,oBAAoB,CAACuD,GAAG,CAACqC,SAAS,CAACrJ,IAAI,CAAC;QAC3D,IAAIsJ,KAAK,CAACL,GAAG,CAACI,SAAS,CAACzC,MAAM,CAAC,EAAE;UAC7B0C,KAAK,CAACtC,GAAG,CAACqC,SAAS,CAACzC,MAAM,CAAC,CAAC3R,IAAI,CAACoU,SAAS,CAAC;QAC/C,CAAC,MACI;UACDC,KAAK,CAACJ,GAAG,CAACG,SAAS,CAACzC,MAAM,EAAE,CAACyC,SAAS,CAAC,CAAC;QAC5C;QACAtD,UAAU,CAAC9Q,IAAI,CAACoU,SAAS,CAAC;MAC9B;IACJ;IACA,OAAOtD,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIiD,qBAAqBA,CAAChJ,IAAI,EAAEwE,SAAS,EAAE8E,KAAK,EAAE;IAC1C,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACxJ,IAAI,EAAEwE,SAAS,CAAC;IACjD,OAAO+E,OAAO,CAAC3M,GAAG,CAACgK,MAAM,IAAI;MACzB,MAAM6C,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAACL,GAAG,CAACrC,MAAM,CAAC,GAAG0C,KAAK,CAACtC,GAAG,CAACJ,MAAM,CAAC,GAAG,EAAE;MAC5E,IAAI6C,gBAAgB,CAAChU,MAAM,EAAE;QACzB,MAAM8O,OAAO,GAAGkF,gBAAgB,CAACC,KAAK,CAAC,CAAC;QACxCnF,OAAO,CAACC,SAAS,GAAGA,SAAS;QAC7B,OAAOD,OAAO;MAClB,CAAC,MACI;QACD,OAAO;UAAEvE,IAAI;UAAE4G,MAAM;UAAEpC;QAAU,CAAC;MACtC;IACJ,CAAC,CAAC;EACN;EACA;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC5B,iBAAiB,CAAC4C,KAAK,CAAC,CAAC;IAC9B,MAAM+D,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,kBAAkB,CAAC,EAAE,IAAI,CAAC5G,iBAAiB,CAAC;IACtGyG,UAAU,CAACjE,OAAO,CAACxS,SAAS,IAAI;MAC5B,IAAI,IAAI,CAAC8P,iBAAiB,CAACiG,GAAG,CAAC/V,SAAS,CAACnE,IAAI,CAAC,KACzC,OAAOyB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMqP,gCAAgC,CAAC3M,SAAS,CAACnE,IAAI,CAAC;MAC1D;MACA,IAAI,CAACiU,iBAAiB,CAACkG,GAAG,CAAChW,SAAS,CAACnE,IAAI,EAAEmE,SAAS,CAAC;IACzD,CAAC,CAAC;EACN;EACA;EACAyR,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,cAAc,GAAG+E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAAC,EAAE,IAAI,CAAC1G,oBAAoB,CAAC;IAC/G,IAAI,CAACyB,cAAc,GAAG8E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACG,qBAAqB,CAAC,EAAE,IAAI,CAAC1G,oBAAoB,CAAC;IAC/G,IAAI,CAACyB,QAAQ,GAAG6E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACI,eAAe,CAAC,EAAE,IAAI,CAAC7G,cAAc,CAAC;IAC7F;IACA,MAAM8G,cAAc,GAAG,IAAI,CAACnF,QAAQ,CAACoF,MAAM,CAACxE,GAAG,IAAI,CAACA,GAAG,CAAC5N,IAAI,CAAC;IAC7D,IAAI,CAAC,IAAI,CAACgK,qBAAqB,IAC3BmI,cAAc,CAACzU,MAAM,GAAG,CAAC,KACxB,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMsP,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAAC+F,cAAc,GAAGqE,cAAc,CAAC,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIjF,qBAAqBA,CAAA,EAAG;IACpB,MAAMmF,kBAAkB,GAAGA,CAACC,GAAG,EAAE1E,GAAG,KAAK0E,GAAG,IAAI,CAAC,CAAC1E,GAAG,CAAC5O,cAAc,CAAC,CAAC;IACtE;IACA,MAAMuT,kBAAkB,GAAG,IAAI,CAACvF,QAAQ,CAACwF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAC1E,IAAIE,kBAAkB,EAAE;MACpB,IAAI,CAACpI,oBAAoB,CAAC,CAAC;IAC/B;IACA;IACA,MAAMsI,oBAAoB,GAAG,IAAI,CAAC3F,cAAc,CAAC0F,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAII,oBAAoB,EAAE;MACtB,IAAI,CAACrF,sBAAsB,CAAC,CAAC;IACjC;IACA,MAAMsF,oBAAoB,GAAG,IAAI,CAAC3F,cAAc,CAACyF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAIK,oBAAoB,EAAE;MACtB,IAAI,CAACrF,sBAAsB,CAAC,CAAC;IACjC;IACA,OAAOkF,kBAAkB,IAAIE,oBAAoB,IAAIC,oBAAoB;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACI3I,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,CAACkH,KAAK,GAAG,EAAE;IACf,IAAIrd,YAAY,CAAC,IAAI,CAACmW,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAACkE,UAAU,CAAC,IAAI,CAAC;IACpC;IACA;IACA,IAAI,IAAI,CAACT,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACqF,WAAW,CAAC,CAAC;MAC5C,IAAI,CAACrF,yBAAyB,GAAG,IAAI;IACzC;IACA,IAAI,CAACzD,UAAU,EAAE;MACb,IAAI,IAAI,CAACyC,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACvN,IAAI,CAAC,EAAE,CAAC;MAC7B;MACA,IAAI,CAACmL,UAAU,CAACrB,aAAa,CAACgF,KAAK,CAAC,CAAC;IACzC;IACA,IAAI,CAAC/D,WAAW,GAAGD,UAAU;EACjC;EACA;EACA0D,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAAC,IAAI,CAAC1D,UAAU,EAAE;MAClB;IACJ;IACA,IAAI+I,UAAU;IACd,IAAIlf,YAAY,CAAC,IAAI,CAACmW,UAAU,CAAC,EAAE;MAC/B+I,UAAU,GAAG,IAAI,CAAC/I,UAAU,CAACgJ,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC,MACI,IAAIrd,YAAY,CAAC,IAAI,CAACqU,UAAU,CAAC,EAAE;MACpC+I,UAAU,GAAG,IAAI,CAAC/I,UAAU;IAChC,CAAC,MACI,IAAIlE,KAAK,CAACmN,OAAO,CAAC,IAAI,CAACjJ,UAAU,CAAC,EAAE;MACrC+I,UAAU,GAAGnd,EAAE,CAAC,IAAI,CAACoU,UAAU,CAAC;IACpC;IACA,IAAI+I,UAAU,KAAK5X,SAAS,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAM4P,8BAA8B,CAAC,CAAC;IAC1C;IACA,IAAI,CAACiF,yBAAyB,GAAGsF,UAAU,CACtCpV,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACsV,UAAU,CAAC,CAAC,CAChCvN,SAAS,CAACwK,IAAI,IAAI;MACnB,IAAI,CAAC8I,KAAK,GAAG9I,IAAI,IAAI,EAAE;MACvB,IAAI,CAAC+F,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIZ,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACK,gBAAgB,CAAC5E,aAAa,CAACnL,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAAC+P,gBAAgB,CAAC5E,aAAa,CAACgF,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACf,cAAc,CAACa,OAAO,CAAC,CAACC,GAAG,EAAE7K,CAAC,KAAK,IAAI,CAACgQ,UAAU,CAAC,IAAI,CAACtF,gBAAgB,EAAEG,GAAG,EAAE7K,CAAC,CAAC,CAAC;IACvF,IAAI,CAACkN,2BAA2B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACI5C,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACK,gBAAgB,CAAC7E,aAAa,CAACnL,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAACgQ,gBAAgB,CAAC7E,aAAa,CAACgF,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACd,cAAc,CAACY,OAAO,CAAC,CAACC,GAAG,EAAE7K,CAAC,KAAK,IAAI,CAACgQ,UAAU,CAAC,IAAI,CAACrF,gBAAgB,EAAEE,GAAG,EAAE7K,CAAC,CAAC,CAAC;IACvF,IAAI,CAACwN,2BAA2B,CAAC,CAAC;EACtC;EACA;EACAI,sBAAsBA,CAACjO,IAAI,EAAEmM,MAAM,EAAE;IACjC,MAAM+C,UAAU,GAAGjM,KAAK,CAACrQ,IAAI,CAACuZ,MAAM,CAAClQ,OAAO,IAAI,EAAE,CAAC,CAACkG,GAAG,CAACmO,UAAU,IAAI;MAClE,MAAM7X,SAAS,GAAG,IAAI,CAAC8P,iBAAiB,CAACgE,GAAG,CAAC+D,UAAU,CAAC;MACxD,IAAI,CAAC7X,SAAS,KAAK,OAAO1C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/D,MAAMkP,0BAA0B,CAACqL,UAAU,CAAC;MAChD;MACA,OAAO7X,SAAS;IACpB,CAAC,CAAC;IACF,MAAMiI,iBAAiB,GAAGwO,UAAU,CAAC/M,GAAG,CAAC1J,SAAS,IAAIA,SAAS,CAAChE,MAAM,CAAC;IACvE,MAAMkM,eAAe,GAAGuO,UAAU,CAAC/M,GAAG,CAAC1J,SAAS,IAAIA,SAAS,CAAC9B,SAAS,CAAC;IACxE,IAAI,CAACiX,aAAa,CAACnN,mBAAmB,CAACT,IAAI,EAAEU,iBAAiB,EAAEC,eAAe,EAAE,CAAC,IAAI,CAACiH,YAAY,IAAI,IAAI,CAACC,2BAA2B,CAAC;EAC5I;EACA;EACA4F,gBAAgBA,CAAC8C,SAAS,EAAE;IACxB,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,IAAInQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,SAAS,CAACpK,aAAa,CAACnL,MAAM,EAAEqF,CAAC,EAAE,EAAE;MACrD,MAAMoQ,OAAO,GAAGF,SAAS,CAACpK,aAAa,CAACoG,GAAG,CAAClM,CAAC,CAAC;MAC9CmQ,YAAY,CAAChW,IAAI,CAACiW,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzB,WAAWA,CAACxJ,IAAI,EAAEwE,SAAS,EAAE;IACzB,IAAI,IAAI,CAACO,QAAQ,CAACtP,MAAM,IAAI,CAAC,EAAE;MAC3B,OAAO,CAAC,IAAI,CAACsP,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA,IAAIwE,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACxH,qBAAqB,EAAE;MAC5BwH,OAAO,GAAG,IAAI,CAACxE,QAAQ,CAACoF,MAAM,CAACxE,GAAG,IAAI,CAACA,GAAG,CAAC5N,IAAI,IAAI4N,GAAG,CAAC5N,IAAI,CAACyM,SAAS,EAAExE,IAAI,CAAC,CAAC;IACjF,CAAC,MACI;MACD,IAAI4G,MAAM,GAAG,IAAI,CAAC7B,QAAQ,CAACnO,IAAI,CAAC+O,GAAG,IAAIA,GAAG,CAAC5N,IAAI,IAAI4N,GAAG,CAAC5N,IAAI,CAACyM,SAAS,EAAExE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC6F,cAAc;MACpG,IAAIe,MAAM,EAAE;QACR2C,OAAO,CAACtU,IAAI,CAAC2R,MAAM,CAAC;MACxB;IACJ;IACA,IAAI,CAAC2C,OAAO,CAAC9T,MAAM,KAAK,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACpE,MAAMuP,kCAAkC,CAACC,IAAI,CAAC;IAClD;IACA,OAAOuJ,OAAO;EAClB;EACAhD,oBAAoBA,CAAC8C,SAAS,EAAEvM,KAAK,EAAE;IACnC,MAAM8J,MAAM,GAAGyC,SAAS,CAACzC,MAAM;IAC/B,MAAMF,OAAO,GAAG;MAAE9X,SAAS,EAAEya,SAAS,CAACrJ;IAAK,CAAC;IAC7C,OAAO;MACHtG,WAAW,EAAEkN,MAAM,CAAC9W,QAAQ;MAC5B4W,OAAO;MACP5J;IACJ,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIgO,UAAUA,CAACM,MAAM,EAAExE,MAAM,EAAE9J,KAAK,EAAE4J,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C;IACA,MAAM2E,IAAI,GAAGD,MAAM,CAACxK,aAAa,CAAC0K,kBAAkB,CAAC1E,MAAM,CAAC9W,QAAQ,EAAE4W,OAAO,EAAE5J,KAAK,CAAC;IACrF,IAAI,CAAC6J,0BAA0B,CAACC,MAAM,EAAEF,OAAO,CAAC;IAChD,OAAO2E,IAAI;EACf;EACA1E,0BAA0BA,CAACC,MAAM,EAAEF,OAAO,EAAE;IACxC,KAAK,IAAI6E,YAAY,IAAI,IAAI,CAACC,iBAAiB,CAAC5E,MAAM,CAAC,EAAE;MACrD,IAAI5O,aAAa,CAACC,oBAAoB,EAAE;QACpCD,aAAa,CAACC,oBAAoB,CAACC,cAAc,CAACoT,kBAAkB,CAACC,YAAY,EAAE7E,OAAO,CAAC;MAC/F;IACJ;IACA,IAAI,CAAClE,kBAAkB,CAACiJ,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI5E,sBAAsBA,CAAA,EAAG;IACrB,MAAMjG,aAAa,GAAG,IAAI,CAACqB,UAAU,CAACrB,aAAa;IACnD,KAAK,IAAI8K,WAAW,GAAG,CAAC,EAAEC,KAAK,GAAG/K,aAAa,CAACnL,MAAM,EAAEiW,WAAW,GAAGC,KAAK,EAAED,WAAW,EAAE,EAAE;MACxF,MAAMR,OAAO,GAAGtK,aAAa,CAACoG,GAAG,CAAC0E,WAAW,CAAC;MAC9C,MAAMhF,OAAO,GAAGwE,OAAO,CAACxE,OAAO;MAC/BA,OAAO,CAACiF,KAAK,GAAGA,KAAK;MACrBjF,OAAO,CAACrU,KAAK,GAAGqZ,WAAW,KAAK,CAAC;MACjChF,OAAO,CAACkF,IAAI,GAAGF,WAAW,KAAKC,KAAK,GAAG,CAAC;MACxCjF,OAAO,CAACmF,IAAI,GAAGH,WAAW,GAAG,CAAC,KAAK,CAAC;MACpChF,OAAO,CAACoF,GAAG,GAAG,CAACpF,OAAO,CAACmF,IAAI;MAC3B,IAAI,IAAI,CAAC9J,qBAAqB,EAAE;QAC5B2E,OAAO,CAAClC,SAAS,GAAG,IAAI,CAACwB,WAAW,CAAC0F,WAAW,CAAC,CAAClH,SAAS;QAC3DkC,OAAO,CAACgF,WAAW,GAAGA,WAAW;MACrC,CAAC,MACI;QACDhF,OAAO,CAAC5J,KAAK,GAAG,IAAI,CAACkJ,WAAW,CAAC0F,WAAW,CAAC,CAAClH,SAAS;MAC3D;IACJ;EACJ;EACA;EACAgH,iBAAiBA,CAAC5E,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAAClQ,OAAO,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,OAAOgH,KAAK,CAACrQ,IAAI,CAACuZ,MAAM,CAAClQ,OAAO,EAAEqV,QAAQ,IAAI;MAC1C,MAAM9U,MAAM,GAAG,IAAI,CAAC+L,iBAAiB,CAACgE,GAAG,CAAC+E,QAAQ,CAAC;MACnD,IAAI,CAAC9U,MAAM,KAAK,OAAOzG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC5D,MAAMkP,0BAA0B,CAACqM,QAAQ,CAAC;MAC9C;MACA,OAAOnF,MAAM,CAAC5P,mBAAmB,CAACC,MAAM,CAAC;IAC7C,CAAC,CAAC;EACN;EACA;EACAmN,yBAAyBA,CAAA,EAAG;IACxB,MAAM4H,gBAAgB,GAAG,IAAI,CAACtJ,SAAS,CAACuJ,sBAAsB,CAAC,CAAC;IAChE,MAAMC,QAAQ,GAAG,CACb;MAAEC,GAAG,EAAE,OAAO;MAAEC,OAAO,EAAE,CAAC,IAAI,CAAC5G,gBAAgB;IAAE,CAAC,EAClD;MAAE2G,GAAG,EAAE,OAAO;MAAEC,OAAO,EAAE,CAAC,IAAI,CAACnK,UAAU,EAAE,IAAI,CAACoK,gBAAgB;IAAE,CAAC,EACnE;MAAEF,GAAG,EAAE,OAAO;MAAEC,OAAO,EAAE,CAAC,IAAI,CAAC3G,gBAAgB;IAAE,CAAC,CACrD;IACD,KAAK,MAAM6G,OAAO,IAAIJ,QAAQ,EAAE;MAC5B,MAAMlR,OAAO,GAAG,IAAI,CAAC0H,SAAS,CAAC6J,aAAa,CAACD,OAAO,CAACH,GAAG,CAAC;MACzDnR,OAAO,CAAC9G,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACxC,KAAK,MAAMkX,MAAM,IAAIkB,OAAO,CAACF,OAAO,EAAE;QAClCpR,OAAO,CAACwR,WAAW,CAACpB,MAAM,CAACjY,UAAU,CAACC,aAAa,CAAC;MACxD;MACA4Y,gBAAgB,CAACQ,WAAW,CAACxR,OAAO,CAAC;IACzC;IACA;IACA,IAAI,CAACnH,WAAW,CAACT,aAAa,CAACoZ,WAAW,CAACR,gBAAgB,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;EACI9J,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACmC,WAAW,CAACvN,IAAI,CAAC,EAAE,CAAC;IACzB,IAAI,CAACmL,UAAU,CAACrB,aAAa,CAACgF,KAAK,CAAC,CAAC;IACrC,IAAI,CAACG,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIR,kBAAkBA,CAAA,EAAG;IACjB,MAAMkH,kBAAkB,GAAGA,CAACpC,GAAG,EAAEqC,CAAC,KAAK;MACnC,OAAOrC,GAAG,IAAIqC,CAAC,CAACnd,gBAAgB,CAAC,CAAC;IACtC,CAAC;IACD;IACA;IACA;IACA,IAAI,IAAI,CAACsV,cAAc,CAAC0F,MAAM,CAACkC,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAACzE,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAAClD,cAAc,CAACyF,MAAM,CAACkC,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAACnE,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI5K,KAAK,CAACrQ,IAAI,CAAC,IAAI,CAAC2V,iBAAiB,CAAC4F,MAAM,CAAC,CAAC,CAAC,CAAC2B,MAAM,CAACkC,kBAAkB,EAAE,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAClK,4BAA4B,GAAG,IAAI;MACxC,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIgC,kBAAkBA,CAAA,EAAG;IACjB,MAAMlK,SAAS,GAAG,IAAI,CAACwI,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC/Q,KAAK,GAAG,KAAK;IACrD,IAAI,CAAC2W,aAAa,GAAG,IAAIvO,YAAY,CAAC,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAAC2J,cAAc,EAAEzJ,SAAS,EAAE,IAAI,CAACC,wBAAwB,EAAE,IAAI,CAACyI,SAAS,CAACgK,SAAS,EAAE,IAAI,CAAChJ,4BAA4B,EAAE,IAAI,CAACb,0BAA0B,CAAC;IAC3N,CAAC,IAAI,CAACL,IAAI,GAAG,IAAI,CAACA,IAAI,CAACgC,MAAM,GAAGjX,EAAE,CAAC,CAAC,EAC/B+H,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACsV,UAAU,CAAC,CAAC,CAChCvN,SAAS,CAAC9D,KAAK,IAAI;MACpB,IAAI,CAAC2W,aAAa,CAACpO,SAAS,GAAGvI,KAAK;MACpC,IAAI,CAACyQ,wBAAwB,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA;EACA0H,WAAWA,CAAC+C,KAAK,EAAE;IACf,OAAOA,KAAK,CAACzC,MAAM,CAAC3D,IAAI,IAAI,CAACA,IAAI,CAAClV,MAAM,IAAIkV,IAAI,CAAClV,MAAM,KAAK,IAAI,CAAC;EACrE;EACA;EACA4U,gBAAgBA,CAAA,EAAG;IACf,MAAM4B,SAAS,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAAC8E,UAAU;IAC1D,IAAI,CAAC/E,SAAS,EAAE;MACZ;IACJ;IACA,MAAMgF,UAAU,GAAG,IAAI,CAAC7K,UAAU,CAACrB,aAAa,CAACnL,MAAM,KAAK,CAAC;IAC7D,IAAIqX,UAAU,KAAK,IAAI,CAAClJ,mBAAmB,EAAE;MACzC;IACJ;IACA,MAAMmJ,SAAS,GAAG,IAAI,CAACV,gBAAgB,CAACzL,aAAa;IACrD,IAAIkM,UAAU,EAAE;MACZ,MAAMzB,IAAI,GAAG0B,SAAS,CAACzB,kBAAkB,CAACxD,SAAS,CAACpO,WAAW,CAAC;MAChE,MAAMsT,QAAQ,GAAG3B,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;MAClC;MACA;MACA,IAAIE,IAAI,CAACF,SAAS,CAAC1V,MAAM,KAAK,CAAC,IAAIuX,QAAQ,EAAElZ,QAAQ,KAAK,IAAI,CAAC4O,SAAS,CAAC7H,YAAY,EAAE;QACnFmS,QAAQ,CAAC9Y,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;QACpC8Y,QAAQ,CAAC3Z,SAAS,CAACC,GAAG,CAACwU,SAAS,CAACnO,iBAAiB,CAAC;MACvD;IACJ,CAAC,MACI;MACDoT,SAAS,CAACnH,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAAChC,mBAAmB,GAAGkJ,UAAU;IACrC,IAAI,CAACtK,kBAAkB,CAACiJ,YAAY,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAAC1b,IAAI,YAAAkd,iBAAAhd,CAAA;MAAA,YAAAA,CAAA,IAAwFqR,QAAQ,EAp1DlBtV,EAAE,CAAAkE,iBAAA,CAo1DkClE,EAAE,CAACqL,eAAe,GAp1DtDrL,EAAE,CAAAkE,iBAAA,CAo1DiElE,EAAE,CAACkhB,iBAAiB,GAp1DvFlhB,EAAE,CAAAkE,iBAAA,CAo1DkGlE,EAAE,CAACyH,UAAU,GAp1DjHzH,EAAE,CAAAmhB,iBAAA,CAo1D4H,MAAM,GAp1DpInhB,EAAE,CAAAkE,iBAAA,CAo1DgK7E,EAAE,CAAC+hB,cAAc,MAp1DnLphB,EAAE,CAAAkE,iBAAA,CAo1D8MnE,QAAQ,GAp1DxNC,EAAE,CAAAkE,iBAAA,CAo1DmOtE,EAAE,CAACyhB,QAAQ,GAp1DhPrhB,EAAE,CAAAkE,iBAAA,CAo1D2P3E,uBAAuB,GAp1DpRS,EAAE,CAAAkE,iBAAA,CAo1D+RuE,0BAA0B,GAp1D3TzI,EAAE,CAAAkE,iBAAA,CAo1DsUrE,EAAE,CAACyhB,aAAa,GAp1DxVthB,EAAE,CAAAkE,iBAAA,CAo1DmWqQ,2BAA2B,OAp1DhYvU,EAAE,CAAAkE,iBAAA,CAo1D2alE,EAAE,CAACa,MAAM;IAAA,CAA4D;EAAE;EACplB;IAAS,IAAI,CAAC0L,IAAI,kBAr1D8EvM,EAAE,CAAAwM,iBAAA;MAAAlI,IAAA,EAq1DJgR,QAAQ;MAAA/Q,SAAA;MAAAsB,cAAA,WAAA0b,wBAAAzf,EAAA,EAAAC,GAAA,EAAAgE,QAAA;QAAA,IAAAjE,EAAA;UAr1DN9B,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EA21D3B0H,YAAY;UA31DazN,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EA21DyDd,YAAY;UA31DvEjF,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EA21D0I8F,SAAS;UA31DrJ7L,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EA21D8NmF,eAAe;UA31D/OlL,EAAE,CAAAgG,cAAA,CAAAD,QAAA,EA21DwToF,eAAe;QAAA;QAAA,IAAArJ,EAAA;UAAA,IAAAmE,EAAA;UA31DzUjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAA8e,UAAA,GAAA5a,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAA+b,kBAAA,GAAA7X,EAAA;UAAFjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAkc,eAAA,GAAAhY,EAAA;UAAFjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAgc,qBAAA,GAAA9X,EAAA;UAAFjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAic,qBAAA,GAAA/X,EAAA;QAAA;MAAA;MAAAub,SAAA,WAAAC,eAAA3f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9B,EAAE,CAAA0hB,WAAA,CA21Dma/M,aAAa;UA31Dlb3U,EAAE,CAAA0hB,WAAA,CA21DihB5M,eAAe;UA31DliB9U,EAAE,CAAA0hB,WAAA,CA21DioB1M,eAAe;UA31DlpBhV,EAAE,CAAA0hB,WAAA,CA21DivBxM,eAAe;QAAA;QAAA,IAAApT,EAAA;UAAA,IAAAmE,EAAA;UA31DlwBjG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAkU,UAAA,GAAAhQ,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAyX,gBAAA,GAAAvT,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAA0X,gBAAA,GAAAxT,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAse,gBAAA,GAAApa,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAqB,SAAA,sBAq1D6Q,EAAE;MAAAia,QAAA;MAAAC,YAAA,WAAAC,sBAAA/f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAr1DjR9B,EAAE,CAAA8hB,WAAA,2BAAA/f,GAAA,CAAAqU,WAAA;QAAA;MAAA;MAAA5P,MAAA;QAAA+O,OAAA;QAAAK,UAAA;QAAAG,qBAAA;QAAAK,WAAA;MAAA;MAAA2L,OAAA;QAAAlK,cAAA;MAAA;MAAAmK,QAAA;MAAAvb,QAAA,GAAFzG,EAAE,CAAA0G,kBAAA,CAq1D4X,CACtd;QAAEC,OAAO,EAAEhD,SAAS;QAAEiD,WAAW,EAAE0O;MAAS,CAAC,EAC7C;QAAE3O,OAAO,EAAEpH,uBAAuB;QAAEmV,QAAQ,EAAEhV;MAA6B,CAAC,EAC5E;QAAEiH,OAAO,EAAE8B,0BAA0B;QAAEiM,QAAQ,EAAEhM;MAAyB,CAAC;MAC3E;MACA;QAAE/B,OAAO,EAAE4N,2BAA2B;QAAE0N,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAAC,kBAAA,EAAAtgB,GAAA;MAAA6K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7I,QAAA,WAAAqe,kBAAArgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA31D2F9B,EAAE,CAAAoiB,eAAA,CAAAzgB,GAAA;UAAF3B,EAAE,CAAAqiB,YAAA,EA21Ds4B,CAAC;UA31Dz4BriB,EAAE,CAAAqiB,YAAA,KA21D47B,CAAC;UA31D/7BriB,EAAE,CAAA6M,kBAAA,KA21D6+B,CAAC,KAAD,CAAC,KAAD,CAAC,KAAD,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAqQ6H,aAAa,EAAwDG,eAAe,EAA8DE,eAAe,EAA8DE,eAAe;MAAAoN,MAAA;MAAAvV,aAAA;IAAA,EAAoI;EAAE;AAC7sD;AACA;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KA71DoGxE,EAAE,CAAAyE,iBAAA,CA61DX6Q,QAAQ,EAAc,CAAC;IACtGhR,IAAI,EAAE9D,SAAS;IACfkD,IAAI,EAAE,CAAC;MAAEgB,QAAQ,EAAE,6BAA6B;MAAEsd,QAAQ,EAAE,UAAU;MAAEle,QAAQ,EAAEsR,kBAAkB;MAAEzN,IAAI,EAAE;QAChG,OAAO,EAAE,WAAW;QACpB,gCAAgC,EAAE,aAAa;QAC/C,iBAAiB,EAAE;MACvB,CAAC;MAAEoF,aAAa,EAAErM,iBAAiB,CAACwM,IAAI;MAAEF,eAAe,EAAEvM,uBAAuB,CAACwM,OAAO;MAAEnG,SAAS,EAAE,CACnG;QAAEH,OAAO,EAAEhD,SAAS;QAAEiD,WAAW,EAAE0O;MAAS,CAAC,EAC7C;QAAE3O,OAAO,EAAEpH,uBAAuB;QAAEmV,QAAQ,EAAEhV;MAA6B,CAAC,EAC5E;QAAEiH,OAAO,EAAE8B,0BAA0B;QAAEiM,QAAQ,EAAEhM;MAAyB,CAAC;MAC3E;MACA;QAAE/B,OAAO,EAAE4N,2BAA2B;QAAE0N,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEK,MAAM,EAAE,CAAC,6CAA6C;IAAE,CAAC;EACxE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhe,IAAI,EAAEtE,EAAE,CAACqL;IAAgB,CAAC,EAAE;MAAE/G,IAAI,EAAEtE,EAAE,CAACkhB;IAAkB,CAAC,EAAE;MAAE5c,IAAI,EAAEtE,EAAE,CAACyH;IAAW,CAAC,EAAE;MAAEnD,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrJ1C,IAAI,EAAExD,SAAS;QACf4C,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEY,IAAI,EAAEjF,EAAE,CAAC+hB,cAAc;MAAEpa,UAAU,EAAE,CAAC;QAC1C1C,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEkE,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClC1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAAC3D,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEuE,IAAI,EAAE1E,EAAE,CAACyhB;IAAS,CAAC,EAAE;MAAE/c,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzD1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACnE,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAE+E,IAAI,EAAEoE,wBAAwB;MAAE1B,UAAU,EAAE,CAAC;QACjD1C,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAAC+E,0BAA0B;MACrC,CAAC;IAAE,CAAC,EAAE;MAAEnE,IAAI,EAAEzE,EAAE,CAACyhB;IAAc,CAAC,EAAE;MAAEhd,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9D1C,IAAI,EAAElE;MACV,CAAC,EAAE;QACCkE,IAAI,EAAEvD;MACV,CAAC,EAAE;QACCuD,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAAC6Q,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAEjQ,IAAI,EAAEtE,EAAE,CAACa,MAAM;MAAEmG,UAAU,EAAE,CAAC;QAClC1C,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmV,OAAO,EAAE,CAAC;MACtCjR,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAEuV,UAAU,EAAE,CAAC;MACbtR,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAE0V,qBAAqB,EAAE,CAAC;MACxBzR,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAE+V,WAAW,EAAE,CAAC;MACd9R,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAEwX,cAAc,EAAE,CAAC;MACjBvT,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEiV,UAAU,EAAE,CAAC;MACb3R,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACiR,aAAa,EAAE;QAAE4N,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAE/I,gBAAgB,EAAE,CAAC;MACnBlV,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACoR,eAAe,EAAE;QAAEyN,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE9I,gBAAgB,EAAE,CAAC;MACnBnV,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACsR,eAAe,EAAE;QAAEuN,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAElC,gBAAgB,EAAE,CAAC;MACnB/b,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACwR,eAAe,EAAE;QAAEqN,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEzE,kBAAkB,EAAE,CAAC;MACrBxZ,IAAI,EAAEpD,eAAe;MACrBwC,IAAI,EAAE,CAACuB,YAAY,EAAE;QAAEud,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEvE,eAAe,EAAE,CAAC;MAClB3Z,IAAI,EAAEpD,eAAe;MACrBwC,IAAI,EAAE,CAACmI,SAAS,EAAE;QAAE2W,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEzE,qBAAqB,EAAE,CAAC;MACxBzZ,IAAI,EAAEpD,eAAe;MACrBwC,IAAI,EAAE,CAACwH,eAAe,EAAE;QAChBsX,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAExE,qBAAqB,EAAE,CAAC;MACxB1Z,IAAI,EAAEpD,eAAe;MACrBwC,IAAI,EAAE,CAACyH,eAAe,EAAE;QAChBqX,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAE3B,UAAU,EAAE,CAAC;MACbvc,IAAI,EAAEhE,YAAY;MAClBoD,IAAI,EAAE,CAAC+J,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASmQ,gBAAgBA,CAAC6E,KAAK,EAAEvF,GAAG,EAAE;EAClC,OAAOuF,KAAK,CAACC,MAAM,CAAChR,KAAK,CAACrQ,IAAI,CAAC6b,GAAG,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyF,aAAa,CAAC;EAChB;EACA,IAAI5f,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmC,KAAK;EACrB;EACA,IAAInC,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACmC,KAAK,GAAGnC,IAAI;IACjB;IACA;IACA,IAAI,CAAC6f,kBAAkB,CAAC,CAAC;EAC7B;EACAnf,WAAWA;EACX;EACA;EACA;EACA6B,MAAM,EAAEud,QAAQ,EAAE;IACd,IAAI,CAACvd,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACud,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACvgB,OAAO,GAAG,OAAO;IACtB,IAAI,CAACugB,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;EAClC;EACA3K,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC0K,kBAAkB,CAAC,CAAC;IACzB,IAAI,IAAI,CAACngB,UAAU,KAAKsE,SAAS,EAAE;MAC/B,IAAI,CAACtE,UAAU,GAAG,IAAI,CAACqgB,wBAAwB,CAAC,CAAC;IACrD;IACA,IAAI,CAAC,IAAI,CAAChgB,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GACb,IAAI,CAAC+f,QAAQ,CAACE,mBAAmB,KAAK,CAAC/O,IAAI,EAAEjR,IAAI,KAAKiR,IAAI,CAACjR,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,IAAI,CAACuC,MAAM,EAAE;MACb;MACA;MACA;MACA,IAAI,CAAC4B,SAAS,CAACd,IAAI,GAAG,IAAI,CAACA,IAAI;MAC/B,IAAI,CAACc,SAAS,CAACZ,UAAU,GAAG,IAAI,CAACA,UAAU;MAC3C,IAAI,CAAChB,MAAM,CAAC4V,YAAY,CAAC,IAAI,CAAChU,SAAS,CAAC;IAC5C,CAAC,MACI,IAAI,OAAO1C,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAM6P,yCAAyC,CAAC,CAAC;IACrD;EACJ;EACAlL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC7D,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC6V,eAAe,CAAC,IAAI,CAACjU,SAAS,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACI4b,wBAAwBA,CAAA,EAAG;IACvB,MAAM/f,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,KAAK,OAAOyB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1D,MAAM8P,kCAAkC,CAAC,CAAC;IAC9C;IACA,IAAI,IAAI,CAACuO,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACG,0BAA0B,EAAE;MAC3D,OAAO,IAAI,CAACH,QAAQ,CAACG,0BAA0B,CAACjgB,IAAI,CAAC;IACzD;IACA,OAAOA,IAAI,CAAC,CAAC,CAAC,CAACkgB,WAAW,CAAC,CAAC,GAAGlgB,IAAI,CAAC4N,KAAK,CAAC,CAAC,CAAC;EAChD;EACA;EACAiS,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1b,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACnE,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;EACA;IAAS,IAAI,CAACgB,IAAI,YAAAmf,sBAAAjf,CAAA;MAAA,YAAAA,CAAA,IAAwF0e,aAAa,EA7/DvB3iB,EAAE,CAAAkE,iBAAA,CA6/DuCoR,QAAQ,MA7/DjDtV,EAAE,CAAAkE,iBAAA,CA6/D4EN,mBAAmB;IAAA,CAA4D;EAAE;EAC/P;IAAS,IAAI,CAAC2I,IAAI,kBA9/D8EvM,EAAE,CAAAwM,iBAAA;MAAAlI,IAAA,EA8/DJqe,aAAa;MAAApe,SAAA;MAAAid,SAAA,WAAA2B,oBAAArhB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9/DX9B,EAAE,CAAA0hB,WAAA,CA8/DiNzc,YAAY;UA9/D/NjF,EAAE,CAAA0hB,WAAA,CA8/DkT7d,UAAU;UA9/D9T7D,EAAE,CAAA0hB,WAAA,CA8/DuZ/c,gBAAgB;QAAA;QAAA,IAAA7C,EAAA;UAAA,IAAAmE,EAAA;UA9/DzajG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAmF,SAAA,GAAAjB,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAqE,IAAA,GAAAH,EAAA,CAAAI,KAAA;UAAFrG,EAAE,CAAAkG,cAAA,CAAAD,EAAA,GAAFjG,EAAE,CAAAmG,WAAA,QAAApE,GAAA,CAAAuE,UAAA,GAAAL,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAG,MAAA;QAAAzD,IAAA;QAAAN,UAAA;QAAAK,YAAA;QAAAR,OAAA;MAAA;MAAAmK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7I,QAAA,WAAAsf,uBAAAthB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9B,EAAE,CAAAqjB,uBAAA,KA+/DxE,CAAC;UA//DqErjB,EAAE,CAAAsjB,UAAA,IAAAzhB,2BAAA,eAkgE5F,CAAC;UAlgEyF7B,EAAE,CAAAsjB,UAAA,IAAA5gB,2BAAA,eAqgE5F,CAAC;UArgEyF1C,EAAE,CAAAujB,qBAAA,CAsgEpF,CAAC;QAAA;MAAA;MAAAzW,YAAA,GAC4CjJ,UAAU,EAAyDc,gBAAgB,EAA+DM,YAAY,EAA4GmD,OAAO,EAAmEb,aAAa;MAAAwF,aAAA;IAAA,EAAuJ;EAAE;AACzjB;AACA;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KAzgEoGxE,EAAE,CAAAyE,iBAAA,CAygEXke,aAAa,EAAc,CAAC;IAC3Gre,IAAI,EAAE9D,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCgB,QAAQ,EAAE,iBAAiB;MAC3BZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBiJ,aAAa,EAAErM,iBAAiB,CAACwM,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAF,eAAe,EAAEvM,uBAAuB,CAACwM;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3I,IAAI,EAAEgR,QAAQ;MAAEtO,UAAU,EAAE,CAAC;QAC7D1C,IAAI,EAAElE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEkE,IAAI,EAAEyC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClC1C,IAAI,EAAElE;MACV,CAAC,EAAE;QACCkE,IAAI,EAAEnE,MAAM;QACZuD,IAAI,EAAE,CAACE,mBAAmB;MAC9B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEb,IAAI,EAAE,CAAC;MACnCuB,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAEoC,UAAU,EAAE,CAAC;MACb6B,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAEyC,YAAY,EAAE,CAAC;MACfwB,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAEiC,OAAO,EAAE,CAAC;MACVgC,IAAI,EAAEjE;IACV,CAAC,CAAC;IAAE6G,SAAS,EAAE,CAAC;MACZ5C,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACuB,YAAY,EAAE;QAAEsd,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAEnc,IAAI,EAAE,CAAC;MACP9B,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACG,UAAU,EAAE;QAAE0e,MAAM,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC;IAAEjc,UAAU,EAAE,CAAC;MACbhC,IAAI,EAAErD,SAAS;MACfyC,IAAI,EAAE,CAACiB,gBAAgB,EAAE;QAAE4d,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiB,qBAAqB,GAAG,CAC1BlO,QAAQ,EACRzJ,SAAS,EACThI,UAAU,EACVmI,aAAa,EACbrH,gBAAgB,EAChBE,gBAAgB,EAChBI,YAAY,EACZmD,OAAO,EACPkF,MAAM,EACN/F,aAAa,EACbK,aAAa,EACbyE,YAAY,EACZnB,eAAe,EACfiC,YAAY,EACZhC,eAAe,EACfwJ,aAAa,EACbG,eAAe,EACfE,eAAe,EACf2N,aAAa,EACblV,YAAY,EACZ+G,cAAc,EACdU,eAAe,CAClB;AACD,MAAMuO,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC1f,IAAI,YAAA2f,uBAAAzf,CAAA;MAAA,YAAAA,CAAA,IAAwFwf,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAplE8E3jB,EAAE,CAAA4jB,gBAAA;MAAAtf,IAAA,EAolESmf;IAAc,EA0C9F;EAAE;EAC7B;IAAS,IAAI,CAACI,IAAI,kBA/nE8E7jB,EAAE,CAAA8jB,gBAAA;MAAAC,OAAA,GA+nEmCjkB,eAAe;IAAA,EAAI;EAAE;AAC9J;AACA;EAAA,QAAA0E,SAAA,oBAAAA,SAAA,KAjoEoGxE,EAAE,CAAAyE,iBAAA,CAioEXgf,cAAc,EAAc,CAAC;IAC5Gnf,IAAI,EAAEnD,QAAQ;IACduC,IAAI,EAAE,CAAC;MACCsgB,OAAO,EAAER,qBAAqB;MAC9BS,YAAY,EAAET,qBAAqB;MACnCO,OAAO,EAAE,CAACjkB,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASmH,WAAW,EAAEoD,UAAU,EAAED,gBAAgB,EAAEzG,SAAS,EAAEyR,kBAAkB,EAAEhN,OAAO,EAAEvE,UAAU,EAAEmI,aAAa,EAAE/G,YAAY,EAAE2C,aAAa,EAAE/C,gBAAgB,EAAEsI,YAAY,EAAEhC,eAAe,EAAE5D,aAAa,EAAE5C,gBAAgB,EAAE0H,YAAY,EAAEnB,eAAe,EAAEuC,YAAY,EAAE+G,cAAc,EAAElH,MAAM,EAAEzB,SAAS,EAAEyJ,QAAQ,EAAEmO,cAAc,EAAEd,aAAa,EAAEhO,aAAa,EAAEK,eAAe,EAAEF,eAAe,EAAEI,eAAe,EAAErH,iBAAiB,EAAE0G,2BAA2B,EAAEzG,YAAY,EAAElK,mBAAmB,EAAE6E,0BAA0B,EAAEC,wBAAwB,EAAEJ,SAAS,EAAEtF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}