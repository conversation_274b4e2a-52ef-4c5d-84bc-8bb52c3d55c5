{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { JobListComponent } from './components/job-list/job-list.component';\nimport { JobDetailComponent } from './components/job-detail/job-detail.component';\nimport { JobCreateComponent } from './components/job-create/job-create.component';\nimport { ApplicationListComponent } from './components/application-list/application-list.component';\nimport { ProfileComponent } from './components/profile/profile.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport { EmployerGuard } from './guards/employer.guard';\nimport { JobSeekerGuard } from './guards/job-seeker.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/jobs',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'jobs',\n  component: JobListComponent\n}, {\n  path: 'jobs/:id',\n  component: JobDetailComponent\n}, {\n  path: 'create-job',\n  component: JobCreateComponent,\n  canActivate: [AuthGuard, EmployerGuard]\n}, {\n  path: 'my-jobs',\n  component: JobListComponent,\n  canActivate: [AuthGuard, EmployerGuard],\n  data: {\n    employerJobs: true\n  }\n}, {\n  path: 'my-applications',\n  component: ApplicationListComponent,\n  canActivate: [AuthGuard, JobSeekerGuard]\n}, {\n  path: 'profile',\n  component: ProfileComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/jobs'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "RegisterComponent", "JobListComponent", "JobDetailComponent", "JobCreateComponent", "ApplicationListComponent", "ProfileComponent", "<PERSON><PERSON><PERSON><PERSON>", "Employer<PERSON><PERSON>", "JobSeekerGuard", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "data", "employerJobs", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\findit\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { LoginComponent } from './components/login/login.component';\r\nimport { RegisterComponent } from './components/register/register.component';\r\nimport { JobListComponent } from './components/job-list/job-list.component';\r\nimport { JobDetailComponent } from './components/job-detail/job-detail.component';\r\nimport { JobCreateComponent } from './components/job-create/job-create.component';\r\nimport { ApplicationListComponent } from './components/application-list/application-list.component';\r\nimport { ProfileComponent } from './components/profile/profile.component';\r\nimport { AuthGuard } from './guards/auth.guard';\r\nimport { EmployerGuard } from './guards/employer.guard';\r\nimport { JobSeekerGuard } from './guards/job-seeker.guard';\r\n\r\nconst routes: Routes = [\r\n  { path: '', redirectTo: '/jobs', pathMatch: 'full' },\r\n  { path: 'login', component: LoginComponent },\r\n  { path: 'register', component: RegisterComponent },\r\n  { path: 'jobs', component: JobListComponent },\r\n  { path: 'jobs/:id', component: JobDetailComponent },\r\n  {\r\n    path: 'create-job',\r\n    component: JobCreateComponent,\r\n    canActivate: [AuthGuard, EmployerGuard]\r\n  },\r\n  {\r\n    path: 'my-jobs',\r\n    component: JobListComponent,\r\n    canActivate: [AuthGuard, EmployerGuard],\r\n    data: { employerJobs: true }\r\n  },\r\n  {\r\n    path: 'my-applications',\r\n    component: ApplicationListComponent,\r\n    canActivate: [AuthGuard, JobSeekerGuard]\r\n  },\r\n  {\r\n    path: 'profile',\r\n    component: ProfileComponent,\r\n    canActivate: [AuthGuard]\r\n  },\r\n  { path: '**', redirectTo: '/jobs' }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,QAAQ,2BAA2B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEd;AAAc,CAAE,EAC5C;EAAEW,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEb;AAAiB,CAAE,EAClD;EAAEU,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEZ;AAAgB,CAAE,EAC7C;EAAES,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEX;AAAkB,CAAE,EACnD;EACEQ,IAAI,EAAE,YAAY;EAClBG,SAAS,EAAEV,kBAAkB;EAC7BW,WAAW,EAAE,CAACR,SAAS,EAAEC,aAAa;CACvC,EACD;EACEG,IAAI,EAAE,SAAS;EACfG,SAAS,EAAEZ,gBAAgB;EAC3Ba,WAAW,EAAE,CAACR,SAAS,EAAEC,aAAa,CAAC;EACvCQ,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAI;CAC3B,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBG,SAAS,EAAET,wBAAwB;EACnCU,WAAW,EAAE,CAACR,SAAS,EAAEE,cAAc;CACxC,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,SAAS,EAAER,gBAAgB;EAC3BS,WAAW,EAAE,CAACR,SAAS;CACxB,EACD;EAAEI,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAO,CAAE,CACpC;AAMD,OAAM,MAAOM,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBnB,YAAY,CAACoB,OAAO,CAACT,MAAM,CAAC,EAC5BX,YAAY;IAAA;EAAA;;;2EAEXmB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAtB,YAAA;IAAAuB,OAAA,GAFjBvB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}