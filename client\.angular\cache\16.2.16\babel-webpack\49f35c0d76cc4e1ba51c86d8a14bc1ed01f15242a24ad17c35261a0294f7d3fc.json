{"ast": null, "code": "import { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/application.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/paginator\";\nimport * as i10 from \"@angular/material/sort\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/menu\";\nfunction ApplicationListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-spinner\", 6);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"folder_open\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 8);\n    i0.ɵɵtext(6, \"Browse Jobs\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.isJobSeeker() ? \"You have not applied to any jobs yet.\" : \"No applications found.\");\n  }\n}\nfunction ApplicationListComponent_div_6_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Job Title\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_4_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const application_r17 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.viewJobDetails(application_r17.jobId));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const application_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (application_r17.job == null ? null : application_r17.job.title) || \"Unknown Job\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Company\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (application_r20.job == null ? null : application_r20.job.employer == null ? null : application_r20.job.employer.profile == null ? null : application_r20.job.employer.profile.companyName) || \"Unknown Company\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Applicant\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r21 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ((application_r21.applicant == null ? null : application_r21.applicant.profile == null ? null : application_r21.applicant.profile.firstName) || \"\") + \" \" + ((application_r21.applicant == null ? null : application_r21.applicant.profile == null ? null : application_r21.applicant.profile.lastName) || \"\") || (application_r21.applicant == null ? null : application_r21.applicant.email) || \"Unknown Applicant\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Applied Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, application_r22.createdAt), \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const application_r23 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.getStatusClass(application_r23.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, application_r23.status), \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 31)(6, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.updateStatus(application_r24, \"applied\"));\n    });\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Applied\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.updateStatus(application_r24, \"reviewed\"));\n    });\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Reviewed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.updateStatus(application_r24, \"interviewed\"));\n    });\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Interviewed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.updateStatus(application_r24, \"rejected\"));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Rejected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.updateStatus(application_r24, \"hired\"));\n    });\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Hired\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r27 = i0.ɵɵreference(5);\n    const application_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r27);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"applied\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"reviewed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"interviewed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"rejected\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"hired\");\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.viewJobDetails(application_r24.jobId));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtemplate(1, ApplicationListComponent_div_6_td_19_div_1_Template, 21, 6, \"div\", 28);\n    i0.ɵɵtemplate(2, ApplicationListComponent_div_6_td_19_div_2_Template, 4, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isEmployer());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isJobSeeker());\n  }\n}\nfunction ApplicationListComponent_div_6_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 34);\n  }\n}\nfunction ApplicationListComponent_div_6_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 35);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 25, 50];\n};\nfunction ApplicationListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"table\", 10);\n    i0.ɵɵelementContainerStart(2, 11);\n    i0.ɵɵtemplate(3, ApplicationListComponent_div_6_th_3_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(4, ApplicationListComponent_div_6_td_4_Template, 3, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 14);\n    i0.ɵɵtemplate(6, ApplicationListComponent_div_6_th_6_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(7, ApplicationListComponent_div_6_td_7_Template, 2, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 15);\n    i0.ɵɵtemplate(9, ApplicationListComponent_div_6_th_9_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(10, ApplicationListComponent_div_6_td_10_Template, 2, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 16);\n    i0.ɵɵtemplate(12, ApplicationListComponent_div_6_th_12_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(13, ApplicationListComponent_div_6_td_13_Template, 3, 3, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 17);\n    i0.ɵɵtemplate(15, ApplicationListComponent_div_6_th_15_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(16, ApplicationListComponent_div_6_td_16_Template, 4, 4, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 18);\n    i0.ɵɵtemplate(18, ApplicationListComponent_div_6_th_18_Template, 2, 0, \"th\", 19);\n    i0.ɵɵtemplate(19, ApplicationListComponent_div_6_td_19_Template, 3, 2, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ApplicationListComponent_div_6_tr_20_Template, 1, 0, \"tr\", 20);\n    i0.ɵɵtemplate(21, ApplicationListComponent_div_6_tr_21_Template, 1, 0, \"tr\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-paginator\", 22);\n    i0.ɵɵlistener(\"page\", function ApplicationListComponent_div_6_Template_mat_paginator_page_22_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.dataSource);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r2.totalItems)(\"pageSize\", ctx_r2.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(7, _c0))(\"pageIndex\", ctx_r2.currentPage);\n  }\n}\nexport class ApplicationListComponent {\n  constructor(applicationService, authService, route, router, snackBar, dialog) {\n    this.applicationService = applicationService;\n    this.authService = authService;\n    this.route = route;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.applications = [];\n    this.dataSource = new MatTableDataSource([]);\n    this.loading = false;\n    this.totalItems = 0;\n    this.pageSize = 10;\n    this.currentPage = 0;\n    this.displayedColumns = [];\n  }\n  ngOnInit() {\n    // Check if we're viewing applications for a specific job\n    this.route.paramMap.subscribe(params => {\n      const jobIdParam = params.get('jobId');\n      if (jobIdParam) {\n        this.jobId = +jobIdParam;\n      }\n    });\n    // Set displayed columns based on user role\n    this.setDisplayedColumns();\n    // Load applications\n    this.loadApplications();\n  }\n  setDisplayedColumns() {\n    if (this.isEmployer()) {\n      this.displayedColumns = ['applicantName', 'jobTitle', 'appliedDate', 'status', 'actions'];\n    } else {\n      this.displayedColumns = ['jobTitle', 'companyName', 'appliedDate', 'status', 'actions'];\n    }\n  }\n  loadApplications() {\n    this.loading = true;\n    const params = {\n      page: this.currentPage + 1,\n      limit: this.pageSize\n    };\n    // Determine which service method to call based on user role and context\n    let applicationsObservable;\n    if (this.isEmployer() && this.jobId) {\n      // Employer viewing applications for a specific job\n      applicationsObservable = this.applicationService.getJobApplications(this.jobId, params);\n    } else if (this.isJobSeeker()) {\n      // Job seeker viewing their own applications\n      applicationsObservable = this.applicationService.getUserApplications(params);\n    } else {\n      // Unauthorized access\n      this.snackBar.open('Unauthorized access', 'Close', {\n        duration: 5000\n      });\n      this.router.navigate(['/jobs']);\n      return;\n    }\n    applicationsObservable.subscribe({\n      next: response => {\n        this.applications = response.data.applications;\n        this.dataSource = new MatTableDataSource(this.applications);\n        this.totalItems = response.data.pagination.totalItems;\n        this.loading = false;\n        // Set up sorting and pagination after data is loaded\n        setTimeout(() => {\n          if (this.sort) {\n            this.dataSource.sort = this.sort;\n          }\n          if (this.paginator) {\n            this.dataSource.paginator = this.paginator;\n          }\n        });\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error loading applications', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onPageChange(event) {\n    this.currentPage = event.pageIndex;\n    this.pageSize = event.pageSize;\n    this.loadApplications();\n  }\n  updateStatus(application, newStatus) {\n    if (!application.id) return;\n    const statusRequest = {\n      status: newStatus\n    };\n    this.applicationService.updateApplicationStatus(application.id, statusRequest).subscribe({\n      next: response => {\n        this.snackBar.open('Application status updated successfully!', 'Close', {\n          duration: 5000,\n          panelClass: ['success-snackbar']\n        });\n        // Update the application in the list\n        const index = this.applications.findIndex(app => app.id === application.id);\n        if (index !== -1) {\n          this.applications[index] = response.data.application;\n          this.dataSource.data = [...this.applications];\n        }\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error updating application status', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  viewJobDetails(jobId) {\n    this.router.navigate(['/jobs', jobId]);\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case 'applied':\n        return 'status-applied';\n      case 'reviewed':\n        return 'status-reviewed';\n      case 'interviewed':\n        return 'status-interviewed';\n      case 'rejected':\n        return 'status-rejected';\n      case 'hired':\n        return 'status-hired';\n      default:\n        return '';\n    }\n  }\n  isEmployer() {\n    return this.authService.isEmployer();\n  }\n  isJobSeeker() {\n    return this.authService.isJobSeeker();\n  }\n  static {\n    this.ɵfac = function ApplicationListComponent_Factory(t) {\n      return new (t || ApplicationListComponent)(i0.ɵɵdirectiveInject(i1.ApplicationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApplicationListComponent,\n      selectors: [[\"app-application-list\"]],\n      viewQuery: function ApplicationListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"application-list-container\"], [1, \"application-list-header\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"applications-table-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"no-results\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/jobs\"], [1, \"applications-table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"applications-table\", 3, \"dataSource\"], [\"matColumnDef\", \"jobTitle\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"companyName\"], [\"matColumnDef\", \"applicantName\"], [\"matColumnDef\", \"appliedDate\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"aria-label\", \"Select page\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"job-link\", 3, \"click\"], [1, \"status-chip\", 3, \"ngClass\"], [\"mat-header-cell\", \"\"], [\"class\", \"action-buttons\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Change status\", 3, \"matMenuTriggerFor\"], [\"statusMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"aria-label\", \"View job\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function ApplicationListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, ApplicationListComponent_div_4_Template, 2, 0, \"div\", 2);\n          i0.ɵɵtemplate(5, ApplicationListComponent_div_5_Template, 7, 1, \"div\", 3);\n          i0.ɵɵtemplate(6, ApplicationListComponent_div_6_Template, 23, 8, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.isJobSeeker() ? \"My Applications\" : ctx.jobId ? \"Applications for This Job\" : \"All Applications\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.applications.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.applications.length > 0);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i3.RouterLink, i7.MatButton, i7.MatIconButton, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, i9.MatPaginator, i10.MatSort, i10.MatSortHeader, i11.MatIcon, i12.MatProgressSpinner, i13.MatMenu, i13.MatMenuItem, i13.MatMenuTrigger, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\".application-list-container[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.application-list-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.application-list-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px 0;\\n  color: #666;\\n}\\n.no-results[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-bottom: 24px;\\n}\\n\\n.applications-table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.applications-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.applications-table[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.applications-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.job-link[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n.job-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.status-chip[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 16px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  display: inline-block;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.status-applied[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n}\\n\\n.status-reviewed[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n}\\n\\n.status-interviewed[_ngcontent-%COMP%] {\\n  background-color: #9c27b0;\\n  color: white;\\n}\\n\\n.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n\\n.status-hired[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .applications-table[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%], .applications-table[_ngcontent-%COMP%]   td.mat-cell[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MatPaginator", "MatSort", "MatTableDataSource", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "isJobSeeker", "ɵɵlistener", "ApplicationListComponent_div_6_td_4_Template_a_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r19", "application_r17", "$implicit", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "viewJobDetails", "jobId", "ɵɵtextInterpolate1", "job", "title", "application_r20", "employer", "profile", "companyName", "application_r21", "applicant", "firstName", "lastName", "email", "ɵɵpipeBind1", "application_r22", "createdAt", "ɵɵproperty", "ctx_r12", "getStatusClass", "application_r23", "status", "ApplicationListComponent_div_6_td_19_div_1_Template_button_click_6_listener", "_r30", "application_r24", "ctx_r28", "updateStatus", "ApplicationListComponent_div_6_td_19_div_1_Template_button_click_9_listener", "ctx_r31", "ApplicationListComponent_div_6_td_19_div_1_Template_button_click_12_listener", "ctx_r33", "ApplicationListComponent_div_6_td_19_div_1_Template_button_click_15_listener", "ctx_r35", "ApplicationListComponent_div_6_td_19_div_1_Template_button_click_18_listener", "ctx_r37", "_r27", "ApplicationListComponent_div_6_td_19_div_2_Template_button_click_1_listener", "_r42", "ctx_r40", "ɵɵtemplate", "ApplicationListComponent_div_6_td_19_div_1_Template", "ApplicationListComponent_div_6_td_19_div_2_Template", "ctx_r14", "isEmployer", "ɵɵelementContainerStart", "ApplicationListComponent_div_6_th_3_Template", "ApplicationListComponent_div_6_td_4_Template", "ɵɵelementContainerEnd", "ApplicationListComponent_div_6_th_6_Template", "ApplicationListComponent_div_6_td_7_Template", "ApplicationListComponent_div_6_th_9_Template", "ApplicationListComponent_div_6_td_10_Template", "ApplicationListComponent_div_6_th_12_Template", "ApplicationListComponent_div_6_td_13_Template", "ApplicationListComponent_div_6_th_15_Template", "ApplicationListComponent_div_6_td_16_Template", "ApplicationListComponent_div_6_th_18_Template", "ApplicationListComponent_div_6_td_19_Template", "ApplicationListComponent_div_6_tr_20_Template", "ApplicationListComponent_div_6_tr_21_Template", "ApplicationListComponent_div_6_Template_mat_paginator_page_22_listener", "$event", "_r45", "ctx_r44", "onPageChange", "ctx_r2", "dataSource", "displayedColumns", "totalItems", "pageSize", "ɵɵpureFunction0", "_c0", "currentPage", "ApplicationListComponent", "constructor", "applicationService", "authService", "route", "router", "snackBar", "dialog", "applications", "loading", "ngOnInit", "paramMap", "subscribe", "params", "jobIdParam", "get", "setDisplayedColumns", "loadApplications", "page", "limit", "applicationsObservable", "getJobApplications", "getUserApplications", "open", "duration", "navigate", "next", "response", "data", "pagination", "setTimeout", "sort", "paginator", "error", "message", "panelClass", "event", "pageIndex", "application", "newStatus", "id", "statusRequest", "updateApplicationStatus", "index", "findIndex", "app", "ɵɵdirectiveInject", "i1", "ApplicationService", "i2", "AuthService", "i3", "ActivatedRoute", "Router", "i4", "MatSnackBar", "i5", "MatDialog", "selectors", "viewQuery", "ApplicationListComponent_Query", "rf", "ctx", "ApplicationListComponent_div_4_Template", "ApplicationListComponent_div_5_Template", "ApplicationListComponent_div_6_Template", "length"], "sources": ["D:\\findit\\client\\src\\app\\components\\application-list\\application-list.component.ts", "D:\\findit\\client\\src\\app\\components\\application-list\\application-list.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatPaginator } from '@angular/material/paginator';\r\nimport { MatSort } from '@angular/material/sort';\r\nimport { MatTableDataSource } from '@angular/material/table';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { ApplicationService } from '../../services/application.service';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { Application, ApplicationStatusRequest } from '../../models/application.model';\r\n\r\n@Component({\r\n  selector: 'app-application-list',\r\n  templateUrl: './application-list.component.html',\r\n  styleUrls: ['./application-list.component.scss']\r\n})\r\nexport class ApplicationListComponent implements OnInit {\r\n  applications: Application[] = [];\r\n  dataSource = new MatTableDataSource<Application>([]);\r\n  loading = false;\r\n  totalItems = 0;\r\n  pageSize = 10;\r\n  currentPage = 0;\r\n  jobId?: number;\r\n\r\n  displayedColumns: string[] = [];\r\n\r\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\r\n  @ViewChild(MatSort) sort!: MatSort;\r\n\r\n  constructor(\r\n    private applicationService: ApplicationService,\r\n    private authService: AuthService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private snackBar: MatSnackBar,\r\n    private dialog: MatDialog\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // Check if we're viewing applications for a specific job\r\n    this.route.paramMap.subscribe(params => {\r\n      const jobIdParam = params.get('jobId');\r\n      if (jobIdParam) {\r\n        this.jobId = +jobIdParam;\r\n      }\r\n    });\r\n\r\n    // Set displayed columns based on user role\r\n    this.setDisplayedColumns();\r\n\r\n    // Load applications\r\n    this.loadApplications();\r\n  }\r\n\r\n  setDisplayedColumns(): void {\r\n    if (this.isEmployer()) {\r\n      this.displayedColumns = ['applicantName', 'jobTitle', 'appliedDate', 'status', 'actions'];\r\n    } else {\r\n      this.displayedColumns = ['jobTitle', 'companyName', 'appliedDate', 'status', 'actions'];\r\n    }\r\n  }\r\n\r\n  loadApplications(): void {\r\n    this.loading = true;\r\n\r\n    const params = {\r\n      page: this.currentPage + 1, // API uses 1-based indexing\r\n      limit: this.pageSize\r\n    };\r\n\r\n    // Determine which service method to call based on user role and context\r\n    let applicationsObservable;\r\n\r\n    if (this.isEmployer() && this.jobId) {\r\n      // Employer viewing applications for a specific job\r\n      applicationsObservable = this.applicationService.getJobApplications(this.jobId, params);\r\n    } else if (this.isJobSeeker()) {\r\n      // Job seeker viewing their own applications\r\n      applicationsObservable = this.applicationService.getUserApplications(params);\r\n    } else {\r\n      // Unauthorized access\r\n      this.snackBar.open('Unauthorized access', 'Close', { duration: 5000 });\r\n      this.router.navigate(['/jobs']);\r\n      return;\r\n    }\r\n\r\n    applicationsObservable.subscribe({\r\n      next: (response) => {\r\n        this.applications = response.data.applications;\r\n        this.dataSource = new MatTableDataSource(this.applications);\r\n        this.totalItems = response.data.pagination.totalItems;\r\n        this.loading = false;\r\n\r\n        // Set up sorting and pagination after data is loaded\r\n        setTimeout(() => {\r\n          if (this.sort) {\r\n            this.dataSource.sort = this.sort;\r\n          }\r\n          if (this.paginator) {\r\n            this.dataSource.paginator = this.paginator;\r\n          }\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error loading applications', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onPageChange(event: any): void {\r\n    this.currentPage = event.pageIndex;\r\n    this.pageSize = event.pageSize;\r\n    this.loadApplications();\r\n  }\r\n\r\n  updateStatus(application: Application, newStatus: string): void {\r\n    if (!application.id) return;\r\n\r\n    const statusRequest: ApplicationStatusRequest = {\r\n      status: newStatus as any\r\n    };\r\n\r\n    this.applicationService.updateApplicationStatus(application.id, statusRequest).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('Application status updated successfully!', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n\r\n        // Update the application in the list\r\n        const index = this.applications.findIndex(app => app.id === application.id);\r\n        if (index !== -1) {\r\n          this.applications[index] = response.data.application;\r\n          this.dataSource.data = [...this.applications];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error updating application status', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  viewJobDetails(jobId: number): void {\r\n    this.router.navigate(['/jobs', jobId]);\r\n  }\r\n\r\n  getStatusClass(status: string): string {\r\n    switch (status) {\r\n      case 'applied': return 'status-applied';\r\n      case 'reviewed': return 'status-reviewed';\r\n      case 'interviewed': return 'status-interviewed';\r\n      case 'rejected': return 'status-rejected';\r\n      case 'hired': return 'status-hired';\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  isEmployer(): boolean {\r\n    return this.authService.isEmployer();\r\n  }\r\n\r\n  isJobSeeker(): boolean {\r\n    return this.authService.isJobSeeker();\r\n  }\r\n}\r\n", "<div class=\"application-list-container\">\r\n    <div class=\"application-list-header\">\r\n        <h1>{{ isJobSeeker() ? 'My Applications' : (jobId ? 'Applications for This Job' : 'All Applications') }}</h1>\r\n    </div>\r\n\r\n    <!-- Loading spinner -->\r\n    <div *ngIf=\"loading\" class=\"loading-container\">\r\n        <mat-spinner diameter=\"40\"></mat-spinner>\r\n    </div>\r\n\r\n    <!-- No applications message -->\r\n    <div *ngIf=\"!loading && applications.length === 0\" class=\"no-results\">\r\n        <mat-icon>folder_open</mat-icon>\r\n        <p>{{ isJobSeeker() ? 'You have not applied to any jobs yet.' : 'No applications found.' }}</p>\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/jobs\">Browse Jobs</button>\r\n    </div>\r\n\r\n    <!-- Applications table -->\r\n    <div *ngIf=\"!loading && applications.length > 0\" class=\"applications-table-container\">\r\n        <table mat-table [dataSource]=\"dataSource\" matSort class=\"applications-table\">\r\n\r\n            <!-- Job Title Column -->\r\n            <ng-container matColumnDef=\"jobTitle\">\r\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Job Title</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    <a (click)=\"viewJobDetails(application.jobId)\" class=\"job-link\">\r\n                        {{ application.job?.title || 'Unknown Job' }}\r\n                    </a>\r\n                </td>\r\n            </ng-container>\r\n\r\n            <!-- Company Name Column (for job seekers) -->\r\n            <ng-container matColumnDef=\"companyName\">\r\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Company</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    {{ application.job?.employer?.profile?.companyName || 'Unknown Company' }}\r\n                </td>\r\n            </ng-container>\r\n\r\n            <!-- Applicant Name Column (for employers) -->\r\n            <ng-container matColumnDef=\"applicantName\">\r\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Applicant</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    {{ (application.applicant?.profile?.firstName || '') + ' ' +\r\n                    (application.applicant?.profile?.lastName || '') || application.applicant?.email || 'Unknown\r\n                    Applicant' }}\r\n                </td>\r\n            </ng-container>\r\n\r\n            <!-- Applied Date Column -->\r\n            <ng-container matColumnDef=\"appliedDate\">\r\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Applied Date</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    {{ application.createdAt | date }}\r\n                </td>\r\n            </ng-container>\r\n\r\n            <!-- Status Column -->\r\n            <ng-container matColumnDef=\"status\">\r\n                <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    <span class=\"status-chip\" [ngClass]=\"getStatusClass(application.status)\">\r\n                        {{ application.status | titlecase }}\r\n                    </span>\r\n                </td>\r\n            </ng-container>\r\n\r\n            <!-- Actions Column -->\r\n            <ng-container matColumnDef=\"actions\">\r\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\r\n                <td mat-cell *matCellDef=\"let application\">\r\n                    <!-- Actions for employers -->\r\n                    <div *ngIf=\"isEmployer()\" class=\"action-buttons\">\r\n                        <button mat-icon-button [matMenuTriggerFor]=\"statusMenu\" aria-label=\"Change status\">\r\n                            <mat-icon>more_vert</mat-icon>\r\n                        </button>\r\n                        <mat-menu #statusMenu=\"matMenu\">\r\n                            <button mat-menu-item (click)=\"updateStatus(application, 'applied')\"\r\n                                [disabled]=\"application.status === 'applied'\">\r\n                                <span>Applied</span>\r\n                            </button>\r\n                            <button mat-menu-item (click)=\"updateStatus(application, 'reviewed')\"\r\n                                [disabled]=\"application.status === 'reviewed'\">\r\n                                <span>Reviewed</span>\r\n                            </button>\r\n                            <button mat-menu-item (click)=\"updateStatus(application, 'interviewed')\"\r\n                                [disabled]=\"application.status === 'interviewed'\">\r\n                                <span>Interviewed</span>\r\n                            </button>\r\n                            <button mat-menu-item (click)=\"updateStatus(application, 'rejected')\"\r\n                                [disabled]=\"application.status === 'rejected'\">\r\n                                <span>Rejected</span>\r\n                            </button>\r\n                            <button mat-menu-item (click)=\"updateStatus(application, 'hired')\"\r\n                                [disabled]=\"application.status === 'hired'\">\r\n                                <span>Hired</span>\r\n                            </button>\r\n                        </mat-menu>\r\n                    </div>\r\n\r\n                    <!-- Actions for job seekers -->\r\n                    <div *ngIf=\"isJobSeeker()\" class=\"action-buttons\">\r\n                        <button mat-icon-button (click)=\"viewJobDetails(application.jobId)\" aria-label=\"View job\">\r\n                            <mat-icon>visibility</mat-icon>\r\n                        </button>\r\n                    </div>\r\n                </td>\r\n            </ng-container>\r\n\r\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n\r\n        <!-- Pagination -->\r\n        <mat-paginator [length]=\"totalItems\" [pageSize]=\"pageSize\" [pageSizeOptions]=\"[5, 10, 25, 50]\"\r\n            [pageIndex]=\"currentPage\" (page)=\"onPageChange($event)\" aria-label=\"Select page\">\r\n        </mat-paginator>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,kBAAkB,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;ICExDC,EAAA,CAAAC,cAAA,aAA+C;IAC3CD,EAAA,CAAAE,SAAA,qBAAyC;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,aAAsE;IACxDD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAwF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC/FH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAD9EH,EAAA,CAAAK,SAAA,GAAwF;IAAxFL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,wEAAwF;;;;;IAUnFR,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;;IACpEH,EAAA,CAAAC,cAAA,aAA2C;IACpCD,EAAA,CAAAS,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,WAAA,GAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAF,OAAA,CAAAG,cAAA,CAAAL,eAAA,CAAAM,KAAA,CAAiC;IAAA,EAAC;IAC1CpB,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADAH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAqB,kBAAA,OAAAP,eAAA,CAAAQ,GAAA,kBAAAR,eAAA,CAAAQ,GAAA,CAAAC,KAAA,wBACJ;;;;;IAMJvB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IAClEH,EAAA,CAAAC,cAAA,aAA2C;IACvCD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IADDH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAqB,kBAAA,OAAAG,eAAA,CAAAF,GAAA,kBAAAE,eAAA,CAAAF,GAAA,CAAAG,QAAA,kBAAAD,eAAA,CAAAF,GAAA,CAAAG,QAAA,CAAAC,OAAA,kBAAAF,eAAA,CAAAF,GAAA,CAAAG,QAAA,CAAAC,OAAA,CAAAC,WAAA,4BACJ;;;;;IAKA3B,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IACpEH,EAAA,CAAAC,cAAA,aAA2C;IACvCD,EAAA,CAAAI,MAAA,GAGJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAHDH,EAAA,CAAAK,SAAA,GAGJ;IAHIL,EAAA,CAAAqB,kBAAA,QAAAO,eAAA,CAAAC,SAAA,kBAAAD,eAAA,CAAAC,SAAA,CAAAH,OAAA,kBAAAE,eAAA,CAAAC,SAAA,CAAAH,OAAA,CAAAI,SAAA,mBAAAF,eAAA,CAAAC,SAAA,kBAAAD,eAAA,CAAAC,SAAA,CAAAH,OAAA,kBAAAE,eAAA,CAAAC,SAAA,CAAAH,OAAA,CAAAK,QAAA,aAAAH,eAAA,CAAAC,SAAA,kBAAAD,eAAA,CAAAC,SAAA,CAAAG,KAAA,8BAGJ;;;;;IAKAhC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IACvEH,EAAA,CAAAC,cAAA,aAA2C;IACvCD,EAAA,CAAAI,MAAA,GACJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IADDH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAiC,WAAA,OAAAC,eAAA,CAAAC,SAAA,OACJ;;;;;IAKAnC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;IACjEH,EAAA,CAAAC,cAAA,aAA2C;IAEnCD,EAAA,CAAAI,MAAA,GACJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAFmBH,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoC,UAAA,YAAAC,OAAA,CAAAC,cAAA,CAAAC,eAAA,CAAAC,MAAA,EAA8C;IACpExC,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAiC,WAAA,OAAAM,eAAA,CAAAC,MAAA,OACJ;;;;;IAMJxC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;;;IAG9CH,EAAA,CAAAC,cAAA,cAAiD;IAE/BD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAElCH,EAAA,CAAAC,cAAA,yBAAgC;IACND,EAAA,CAAAS,UAAA,mBAAAgC,4EAAA;MAAAzC,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAA6B,OAAA,GAAA5C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA0B,OAAA,CAAAC,YAAA,CAAAF,eAAA,EAA0B,SAAS,CAAC;IAAA,EAAC;IAEhE3C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAExBH,EAAA,CAAAC,cAAA,iBACmD;IAD7BD,EAAA,CAAAS,UAAA,mBAAAqC,4EAAA;MAAA9C,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAgC,OAAA,GAAA/C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA6B,OAAA,CAAAF,YAAA,CAAAF,eAAA,EAA0B,UAAU,CAAC;IAAA,EAAC;IAEjE3C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEzBH,EAAA,CAAAC,cAAA,kBACsD;IADhCD,EAAA,CAAAS,UAAA,mBAAAuC,6EAAA;MAAAhD,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAkC,OAAA,GAAAjD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA+B,OAAA,CAAAJ,YAAA,CAAAF,eAAA,EAA0B,aAAa,CAAC;IAAA,EAAC;IAEpE3C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE5BH,EAAA,CAAAC,cAAA,kBACmD;IAD7BD,EAAA,CAAAS,UAAA,mBAAAyC,6EAAA;MAAAlD,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAoC,OAAA,GAAAnD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAiC,OAAA,CAAAN,YAAA,CAAAF,eAAA,EAA0B,UAAU,CAAC;IAAA,EAAC;IAEjE3C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEzBH,EAAA,CAAAC,cAAA,kBACgD;IAD1BD,EAAA,CAAAS,UAAA,mBAAA2C,6EAAA;MAAApD,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAC,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAsC,OAAA,GAAArD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAmC,OAAA,CAAAR,YAAA,CAAAF,eAAA,EAA0B,OAAO,CAAC;IAAA,EAAC;IAE9D3C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAtBFH,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAoC,UAAA,sBAAAkB,IAAA,CAAgC;IAKhDtD,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAoC,UAAA,aAAAO,eAAA,CAAAH,MAAA,eAA6C;IAI7CxC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoC,UAAA,aAAAO,eAAA,CAAAH,MAAA,gBAA8C;IAI9CxC,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAoC,UAAA,aAAAO,eAAA,CAAAH,MAAA,mBAAiD;IAIjDxC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoC,UAAA,aAAAO,eAAA,CAAAH,MAAA,gBAA8C;IAI9CxC,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAoC,UAAA,aAAAO,eAAA,CAAAH,MAAA,aAA2C;;;;;;IAOvDxC,EAAA,CAAAC,cAAA,cAAkD;IACtBD,EAAA,CAAAS,UAAA,mBAAA8C,4EAAA;MAAAvD,EAAA,CAAAY,aAAA,CAAA4C,IAAA;MAAA,MAAAb,eAAA,GAAA3C,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAA0C,OAAA,GAAAzD,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAuC,OAAA,CAAAtC,cAAA,CAAAwB,eAAA,CAAAvB,KAAA,CAAiC;IAAA,EAAC;IAC/DpB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAW;;;;;IAjC3CH,EAAA,CAAAC,cAAA,aAA2C;IAEvCD,EAAA,CAAA0D,UAAA,IAAAC,mDAAA,mBA0BM;IAGN3D,EAAA,CAAA0D,UAAA,IAAAE,mDAAA,kBAIM;IACV5D,EAAA,CAAAG,YAAA,EAAK;;;;IAlCKH,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAoC,UAAA,SAAAyB,OAAA,CAAAC,UAAA,GAAkB;IA6BlB9D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAoC,UAAA,SAAAyB,OAAA,CAAArD,WAAA,GAAmB;;;;;IAQjCR,EAAA,CAAAE,SAAA,aAA4D;;;;;IAC5DF,EAAA,CAAAE,SAAA,aAAkE;;;;;;;;;IA5F1EF,EAAA,CAAAC,cAAA,aAAsF;IAI9ED,EAAA,CAAA+D,uBAAA,OAAsC;IAClC/D,EAAA,CAAA0D,UAAA,IAAAM,4CAAA,iBAAoE;IACpEhE,EAAA,CAAA0D,UAAA,IAAAO,4CAAA,iBAIK;IACTjE,EAAA,CAAAkE,qBAAA,EAAe;IAGflE,EAAA,CAAA+D,uBAAA,OAAyC;IACrC/D,EAAA,CAAA0D,UAAA,IAAAS,4CAAA,iBAAkE;IAClEnE,EAAA,CAAA0D,UAAA,IAAAU,4CAAA,iBAEK;IACTpE,EAAA,CAAAkE,qBAAA,EAAe;IAGflE,EAAA,CAAA+D,uBAAA,OAA2C;IACvC/D,EAAA,CAAA0D,UAAA,IAAAW,4CAAA,iBAAoE;IACpErE,EAAA,CAAA0D,UAAA,KAAAY,6CAAA,iBAIK;IACTtE,EAAA,CAAAkE,qBAAA,EAAe;IAGflE,EAAA,CAAA+D,uBAAA,QAAyC;IACrC/D,EAAA,CAAA0D,UAAA,KAAAa,6CAAA,iBAAuE;IACvEvE,EAAA,CAAA0D,UAAA,KAAAc,6CAAA,iBAEK;IACTxE,EAAA,CAAAkE,qBAAA,EAAe;IAGflE,EAAA,CAAA+D,uBAAA,QAAoC;IAChC/D,EAAA,CAAA0D,UAAA,KAAAe,6CAAA,iBAAiE;IACjEzE,EAAA,CAAA0D,UAAA,KAAAgB,6CAAA,iBAIK;IACT1E,EAAA,CAAAkE,qBAAA,EAAe;IAGflE,EAAA,CAAA+D,uBAAA,QAAqC;IACjC/D,EAAA,CAAA0D,UAAA,KAAAiB,6CAAA,iBAAkD;IAClD3E,EAAA,CAAA0D,UAAA,KAAAkB,6CAAA,iBAoCK;IACT5E,EAAA,CAAAkE,qBAAA,EAAe;IAEflE,EAAA,CAAA0D,UAAA,KAAAmB,6CAAA,iBAA4D;IAC5D7E,EAAA,CAAA0D,UAAA,KAAAoB,6CAAA,iBAAkE;IACtE9E,EAAA,CAAAG,YAAA,EAAQ;IAGRH,EAAA,CAAAC,cAAA,yBACqF;IAAvDD,EAAA,CAAAS,UAAA,kBAAAsE,uEAAAC,MAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAAqE,IAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAAiB,aAAA;MAAA,OAAQjB,EAAA,CAAAkB,WAAA,CAAAgE,OAAA,CAAAC,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAC3DhF,EAAA,CAAAG,YAAA,EAAgB;;;;IAjGCH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,UAAA,eAAAgD,MAAA,CAAAC,UAAA,CAAyB;IA0FlBrF,EAAA,CAAAK,SAAA,IAAiC;IAAjCL,EAAA,CAAAoC,UAAA,oBAAAgD,MAAA,CAAAE,gBAAA,CAAiC;IACpBtF,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAoC,UAAA,qBAAAgD,MAAA,CAAAE,gBAAA,CAA0B;IAIhDtF,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAoC,UAAA,WAAAgD,MAAA,CAAAG,UAAA,CAAqB,aAAAH,MAAA,CAAAI,QAAA,qBAAAxF,EAAA,CAAAyF,eAAA,IAAAC,GAAA,gBAAAN,MAAA,CAAAO,WAAA;;;ADlG5C,OAAM,MAAOC,wBAAwB;EAcnCC,YACUC,kBAAsC,EACtCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,QAAqB,EACrBC,MAAiB;IALjB,KAAAL,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAnBhB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAf,UAAU,GAAG,IAAItF,kBAAkB,CAAc,EAAE,CAAC;IACpD,KAAAsG,OAAO,GAAG,KAAK;IACf,KAAAd,UAAU,GAAG,CAAC;IACd,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAG,WAAW,GAAG,CAAC;IAGf,KAAAL,gBAAgB,GAAa,EAAE;EAY3B;EAEJgB,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,MAAMC,UAAU,GAAGD,MAAM,CAACE,GAAG,CAAC,OAAO,CAAC;MACtC,IAAID,UAAU,EAAE;QACd,IAAI,CAACtF,KAAK,GAAG,CAACsF,UAAU;;IAE5B,CAAC,CAAC;IAEF;IACA,IAAI,CAACE,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC9C,UAAU,EAAE,EAAE;MACrB,IAAI,CAACwB,gBAAgB,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;KAC1F,MAAM;MACL,IAAI,CAACA,gBAAgB,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;;EAE3F;EAEAuB,gBAAgBA,CAAA;IACd,IAAI,CAACR,OAAO,GAAG,IAAI;IAEnB,MAAMI,MAAM,GAAG;MACbK,IAAI,EAAE,IAAI,CAACnB,WAAW,GAAG,CAAC;MAC1BoB,KAAK,EAAE,IAAI,CAACvB;KACb;IAED;IACA,IAAIwB,sBAAsB;IAE1B,IAAI,IAAI,CAAClD,UAAU,EAAE,IAAI,IAAI,CAAC1C,KAAK,EAAE;MACnC;MACA4F,sBAAsB,GAAG,IAAI,CAAClB,kBAAkB,CAACmB,kBAAkB,CAAC,IAAI,CAAC7F,KAAK,EAAEqF,MAAM,CAAC;KACxF,MAAM,IAAI,IAAI,CAACjG,WAAW,EAAE,EAAE;MAC7B;MACAwG,sBAAsB,GAAG,IAAI,CAAClB,kBAAkB,CAACoB,mBAAmB,CAACT,MAAM,CAAC;KAC7E,MAAM;MACL;MACA,IAAI,CAACP,QAAQ,CAACiB,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACtE,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B;;IAGFL,sBAAsB,CAACR,SAAS,CAAC;MAC/Bc,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACnB,YAAY,GAAGmB,QAAQ,CAACC,IAAI,CAACpB,YAAY;QAC9C,IAAI,CAACf,UAAU,GAAG,IAAItF,kBAAkB,CAAC,IAAI,CAACqG,YAAY,CAAC;QAC3D,IAAI,CAACb,UAAU,GAAGgC,QAAQ,CAACC,IAAI,CAACC,UAAU,CAAClC,UAAU;QACrD,IAAI,CAACc,OAAO,GAAG,KAAK;QAEpB;QACAqB,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACC,IAAI,EAAE;YACb,IAAI,CAACtC,UAAU,CAACsC,IAAI,GAAG,IAAI,CAACA,IAAI;;UAElC,IAAI,IAAI,CAACC,SAAS,EAAE;YAClB,IAAI,CAACvC,UAAU,CAACuC,SAAS,GAAG,IAAI,CAACA,SAAS;;QAE9C,CAAC,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3B,QAAQ,CAACiB,IAAI,CAACU,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,4BAA4B,EAAE,OAAO,EAAE;UAChFV,QAAQ,EAAE,IAAI;UACdW,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAlB,YAAYA,CAAC6C,KAAU;IACrB,IAAI,CAACrC,WAAW,GAAGqC,KAAK,CAACC,SAAS;IAClC,IAAI,CAACzC,QAAQ,GAAGwC,KAAK,CAACxC,QAAQ;IAC9B,IAAI,CAACqB,gBAAgB,EAAE;EACzB;EAEAhE,YAAYA,CAACqF,WAAwB,EAAEC,SAAiB;IACtD,IAAI,CAACD,WAAW,CAACE,EAAE,EAAE;IAErB,MAAMC,aAAa,GAA6B;MAC9C7F,MAAM,EAAE2F;KACT;IAED,IAAI,CAACrC,kBAAkB,CAACwC,uBAAuB,CAACJ,WAAW,CAACE,EAAE,EAAEC,aAAa,CAAC,CAAC7B,SAAS,CAAC;MACvFc,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACrB,QAAQ,CAACiB,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;UACtEC,QAAQ,EAAE,IAAI;UACdW,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QAEF;QACA,MAAMQ,KAAK,GAAG,IAAI,CAACnC,YAAY,CAACoC,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACL,EAAE,KAAKF,WAAW,CAACE,EAAE,CAAC;QAC3E,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACnC,YAAY,CAACmC,KAAK,CAAC,GAAGhB,QAAQ,CAACC,IAAI,CAACU,WAAW;UACpD,IAAI,CAAC7C,UAAU,CAACmC,IAAI,GAAG,CAAC,GAAG,IAAI,CAACpB,YAAY,CAAC;;MAEjD,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3B,QAAQ,CAACiB,IAAI,CAACU,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,mCAAmC,EAAE,OAAO,EAAE;UACvFV,QAAQ,EAAE,IAAI;UACdW,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA5G,cAAcA,CAACC,KAAa;IAC1B,IAAI,CAAC6E,MAAM,CAACoB,QAAQ,CAAC,CAAC,OAAO,EAAEjG,KAAK,CAAC,CAAC;EACxC;EAEAkB,cAAcA,CAACE,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,aAAa;QAAE,OAAO,oBAAoB;MAC/C,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC;QAAS,OAAO,EAAE;;EAEtB;EAEAsB,UAAUA,CAAA;IACR,OAAO,IAAI,CAACiC,WAAW,CAACjC,UAAU,EAAE;EACtC;EAEAtD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACuF,WAAW,CAACvF,WAAW,EAAE;EACvC;;;uBA3JWoF,wBAAwB,EAAA5F,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAE,MAAA,GAAAjJ,EAAA,CAAA0I,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAnJ,EAAA,CAAA0I,iBAAA,CAAAU,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAxBzD,wBAAwB;MAAA0D,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAWxB5J,YAAY;yBACZC,OAAO;;;;;;;;;;;;;UC5BpBE,EAAA,CAAAC,cAAA,aAAwC;UAE5BD,EAAA,CAAAI,MAAA,GAAoG;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAIjHH,EAAA,CAAA0D,UAAA,IAAAiG,uCAAA,iBAEM;UAGN3J,EAAA,CAAA0D,UAAA,IAAAkG,uCAAA,iBAIM;UAGN5J,EAAA,CAAA0D,UAAA,IAAAmG,uCAAA,kBAmGM;UACV7J,EAAA,CAAAG,YAAA,EAAM;;;UApHMH,EAAA,CAAAK,SAAA,GAAoG;UAApGL,EAAA,CAAAM,iBAAA,CAAAoJ,GAAA,CAAAlJ,WAAA,yBAAAkJ,GAAA,CAAAtI,KAAA,oDAAoG;UAItGpB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoC,UAAA,SAAAsH,GAAA,CAAArD,OAAA,CAAa;UAKbrG,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAoC,UAAA,UAAAsH,GAAA,CAAArD,OAAA,IAAAqD,GAAA,CAAAtD,YAAA,CAAA0D,MAAA,OAA2C;UAO3C9J,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAoC,UAAA,UAAAsH,GAAA,CAAArD,OAAA,IAAAqD,GAAA,CAAAtD,YAAA,CAAA0D,MAAA,KAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}