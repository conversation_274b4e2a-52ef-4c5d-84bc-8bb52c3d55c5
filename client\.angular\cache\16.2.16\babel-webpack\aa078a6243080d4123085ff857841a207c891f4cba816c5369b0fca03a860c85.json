{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/job.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"../../services/application.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/chips\";\nfunction JobDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"mat-spinner\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.job.salaryRange, \"\");\n  }\n}\nfunction JobDetailComponent_div_7_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h3\");\n    i0.ɵɵtext(2, \"Requirements\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.job.requirements);\n  }\n}\nfunction JobDetailComponent_div_7_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h3\");\n    i0.ɵɵtext(2, \"Your Application Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"mat-chip\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.getApplicationStatusClass(ctx_r5.userApplication.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 3, ctx_r5.userApplication.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Applied on: \", i0.ɵɵpipeBind1(9, 5, ctx_r5.userApplication.createdAt), \"\");\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Apply Now\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_7_div_37_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.applyForJob());\n    });\n    i0.ɵɵtemplate(1, JobDetailComponent_div_7_div_37_button_1_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 25);\n    i0.ɵɵtemplate(2, JobDetailComponent_div_7_div_37_button_1_span_2_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.applying);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.applying);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.applying);\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵtext(1, \" Already Applied \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, JobDetailComponent_div_7_div_37_button_1_Template, 3, 3, \"button\", 22);\n    i0.ɵɵtemplate(2, JobDetailComponent_div_7_div_37_button_2_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.userApplication);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.userApplication);\n  }\n}\nfunction JobDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_7_div_38_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.editJob());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Edit Job \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.job.status === \"active\" ? \"Close Job\" : \"Reopen Job\", \" \");\n  }\n}\nconst _c0 = function () {\n  return [\"/login\"];\n};\nconst _c1 = function (a0) {\n  return {\n    returnUrl: a0\n  };\n};\nfunction JobDetailComponent_div_7_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 30);\n    i0.ɵɵtext(2, \" Login to Apply \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c0))(\"queryParams\", i0.ɵɵpureFunction1(3, _c1, \"/jobs/\" + ctx_r8.job.id));\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.job.employer == null ? null : ctx_r18.job.employer.profile == null ? null : ctx_r18.job.employer.profile.companyDescription, \" \");\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" No company description available. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 31)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"About the Company\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, JobDetailComponent_div_7_mat_card_40_p_7_Template, 2, 1, \"p\", 11);\n    i0.ɵɵtemplate(8, JobDetailComponent_div_7_mat_card_40_p_8_Template, 2, 0, \"p\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyName) || \"Company\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyDescription));\n  }\n}\nfunction JobDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-card\", 9)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 10)(7, \"span\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\")(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\")(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, JobDetailComponent_div_7_span_20_Template, 4, 1, \"span\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"mat-card-content\")(22, \"div\", 12)(23, \"mat-chip\", 13);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, JobDetailComponent_div_7_div_34_Template, 5, 1, \"div\", 16);\n    i0.ɵɵtemplate(35, JobDetailComponent_div_7_div_35_Template, 10, 7, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"mat-card-actions\");\n    i0.ɵɵtemplate(37, JobDetailComponent_div_7_div_37_Template, 3, 2, \"div\", 11);\n    i0.ɵɵtemplate(38, JobDetailComponent_div_7_div_38_Template, 9, 1, \"div\", 11);\n    i0.ɵɵtemplate(39, JobDetailComponent_div_7_div_39_Template, 3, 5, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, JobDetailComponent_div_7_mat_card_40_Template, 9, 3, \"mat-card\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.job.title);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.job.employer == null ? null : ctx_r1.job.employer.profile == null ? null : ctx_r1.job.employer.profile.companyName) || \"Company\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.job.location, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 15, ctx_r1.job.jobType), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.salaryRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ctx_r1.job.status === \"active\" ? \"primary\" : \"warn\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 17, ctx_r1.job.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Posted: \", i0.ɵɵpipeBind1(28, 19, ctx_r1.job.createdAt), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.job.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.requirements);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAuthenticated() && ctx_r1.isJobSeeker() && ctx_r1.userApplication);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJobSeeker() && ctx_r1.job.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEmployer() && ctx_r1.isJobOwner());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isAuthenticated() && ctx_r1.job.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.employer == null ? null : ctx_r1.job.employer.profile);\n  }\n}\nfunction JobDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Job not found or has been removed.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.goBack());\n    });\n    i0.ɵɵtext(6, \" Back to Jobs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class JobDetailComponent {\n  constructor(route, router, jobService, authService, applicationService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.jobService = jobService;\n    this.authService = authService;\n    this.applicationService = applicationService;\n    this.snackBar = snackBar;\n    this.job = null;\n    this.loading = true;\n    this.applying = false;\n    this.userApplication = null;\n  }\n  ngOnInit() {\n    this.loadJob();\n  }\n  loadJob() {\n    const jobId = Number(this.route.snapshot.paramMap.get('id'));\n    if (!jobId) {\n      this.router.navigate(['/jobs']);\n      return;\n    }\n    this.loading = true;\n    this.jobService.getJobById(jobId).subscribe({\n      next: response => {\n        this.job = response.data.job;\n        this.loading = false;\n        // If user is authenticated and is a job seeker, check if they've already applied\n        if (this.isAuthenticated() && this.isJobSeeker()) {\n          this.checkApplicationStatus(jobId);\n        }\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error loading job details', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.loading = false;\n        this.router.navigate(['/jobs']);\n      }\n    });\n  }\n  checkApplicationStatus(jobId) {\n    this.applicationService.getUserApplications().subscribe({\n      next: response => {\n        const applications = response.data.applications;\n        this.userApplication = applications.find(app => app.jobId === jobId) || null;\n      },\n      error: error => {\n        console.error('Error checking application status:', error);\n      }\n    });\n  }\n  applyForJob() {\n    if (!this.job || !this.job.id) return;\n    if (!this.isAuthenticated()) {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: `/jobs/${this.job.id}`\n        }\n      });\n      return;\n    }\n    if (!this.isJobSeeker()) {\n      this.snackBar.open('Only job seekers can apply for jobs', 'Close', {\n        duration: 5000\n      });\n      return;\n    }\n    // Open application dialog\n    // For simplicity, we'll just apply directly here\n    // In a real app, you might want to open a dialog to collect cover letter\n    this.applying = true;\n    const applicationRequest = {\n      jobId: this.job.id,\n      coverLetter: 'I am interested in this position and would like to apply.'\n    };\n    this.applicationService.applyForJob(applicationRequest).subscribe({\n      next: response => {\n        this.snackBar.open('Application submitted successfully!', 'Close', {\n          duration: 5000,\n          panelClass: ['success-snackbar']\n        });\n        this.userApplication = response.data.application;\n        this.applying = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Error submitting application', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        this.applying = false;\n      }\n    });\n  }\n  isAuthenticated() {\n    return this.authService.checkAuth();\n  }\n  isEmployer() {\n    return this.authService.isEmployer();\n  }\n  isJobSeeker() {\n    return this.authService.isJobSeeker();\n  }\n  isJobOwner() {\n    if (!this.job || !this.isAuthenticated()) return false;\n    // Use currentUserValue instead of getCurrentUser()\n    const currentUser = this.authService.currentUserValue;\n    return currentUser?.id === this.job.employerId;\n  }\n  getApplicationStatusClass(status) {\n    switch (status) {\n      case 'applied':\n        return 'status-applied';\n      case 'reviewed':\n        return 'status-reviewed';\n      case 'interviewed':\n        return 'status-interviewed';\n      case 'rejected':\n        return 'status-rejected';\n      case 'hired':\n        return 'status-hired';\n      default:\n        return '';\n    }\n  }\n  editJob() {\n    if (!this.job || !this.job.id) return;\n    this.router.navigate(['/create-job']);\n  }\n  goBack() {\n    this.router.navigate(['/jobs']);\n  }\n  static {\n    this.ɵfac = function JobDetailComponent_Factory(t) {\n      return new (t || JobDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.JobService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ApplicationService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: JobDetailComponent,\n      selectors: [[\"app-job-detail\"]],\n      decls: 9,\n      vars: 3,\n      consts: [[1, \"job-detail-container\"], [1, \"back-button\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"job-content\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"job-content\"], [1, \"job-card\"], [1, \"job-subtitle\"], [4, \"ngIf\"], [1, \"job-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"job-date\"], [1, \"job-section\"], [\"class\", \"job-section\", 4, \"ngIf\"], [\"class\", \"application-status\", 4, \"ngIf\"], [\"class\", \"company-card\", 4, \"ngIf\"], [1, \"application-status\"], [1, \"status-chip\"], [\"selected\", \"\", 3, \"ngClass\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"disabled\", \"\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"disabled\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 1, \"action-button\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"routerLink\", \"queryParams\"], [1, \"company-card\"], [1, \"error-container\"]],\n      template: function JobDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function JobDetailComponent_Template_button_click_2_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" Back to Jobs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, JobDetailComponent_div_6_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(7, JobDetailComponent_div_7_Template, 41, 21, \"div\", 4);\n          i0.ɵɵtemplate(8, JobDetailComponent_div_8_Template, 7, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.job);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.job);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgIf, i1.RouterLink, i7.MatButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatProgressSpinner, i11.MatChip, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\".job-detail-container[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.job-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 20px;\\n}\\n\\n.job-card[_ngcontent-%COMP%], .company-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.job-subtitle[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-top: 8px;\\n}\\n.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  height: 16px;\\n  width: 16px;\\n  margin-right: 4px;\\n}\\n\\n.job-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 16px 0;\\n}\\n.job-status[_ngcontent-%COMP%]   .job-date[_ngcontent-%COMP%] {\\n  margin-left: 16px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n.job-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.job-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.job-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  white-space: pre-line;\\n}\\n\\n.application-status[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding: 16px;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n}\\n.application-status[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-weight: 500;\\n}\\n.application-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.application-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-left: 16px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\nmat-card-actions[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\nmat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 40px 0;\\n}\\n.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n  color: #f44336;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-bottom: 24px;\\n}\\n\\n.status-applied[_ngcontent-%COMP%] {\\n  background-color: #2196f3 !important;\\n  color: white !important;\\n}\\n\\n.status-reviewed[_ngcontent-%COMP%] {\\n  background-color: #ff9800 !important;\\n  color: white !important;\\n}\\n\\n.status-interviewed[_ngcontent-%COMP%] {\\n  background-color: #9c27b0 !important;\\n  color: white !important;\\n}\\n\\n.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n\\n.status-hired[_ngcontent-%COMP%] {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .job-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r3", "job", "salaryRange", "ɵɵtextInterpolate", "ctx_r4", "requirements", "ɵɵproperty", "ctx_r5", "getApplicationStatusClass", "userApplication", "status", "ɵɵpipeBind1", "createdAt", "ɵɵlistener", "JobDetailComponent_div_7_div_37_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtemplate", "JobDetailComponent_div_7_div_37_button_1_mat_spinner_1_Template", "JobDetailComponent_div_7_div_37_button_1_span_2_Template", "ctx_r10", "applying", "JobDetailComponent_div_7_div_37_button_1_Template", "JobDetailComponent_div_7_div_37_button_2_Template", "ctx_r6", "JobDetailComponent_div_7_div_38_Template_button_click_1_listener", "_r17", "ctx_r16", "edit<PERSON><PERSON>", "ctx_r7", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "ctx_r8", "id", "ctx_r18", "employer", "profile", "companyDescription", "JobDetailComponent_div_7_mat_card_40_p_7_Template", "JobDetailComponent_div_7_mat_card_40_p_8_Template", "ctx_r9", "companyName", "JobDetailComponent_div_7_span_20_Template", "JobDetailComponent_div_7_div_34_Template", "JobDetailComponent_div_7_div_35_Template", "JobDetailComponent_div_7_div_37_Template", "JobDetailComponent_div_7_div_38_Template", "JobDetailComponent_div_7_div_39_Template", "JobDetailComponent_div_7_mat_card_40_Template", "ctx_r1", "title", "location", "jobType", "description", "isAuthenticated", "isJobSeeker", "isEmployer", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "JobDetailComponent_div_8_Template_button_click_5_listener", "_r21", "ctx_r20", "goBack", "JobDetailComponent", "constructor", "route", "router", "jobService", "authService", "applicationService", "snackBar", "loading", "ngOnInit", "loadJob", "jobId", "Number", "snapshot", "paramMap", "get", "navigate", "getJobById", "subscribe", "next", "response", "data", "checkApplicationStatus", "error", "open", "message", "duration", "panelClass", "getUserApplications", "applications", "find", "app", "console", "queryParams", "returnUrl", "applicationRequest", "coverLetter", "application", "checkAuth", "currentUser", "currentUserValue", "employerId", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "JobService", "i3", "AuthService", "i4", "ApplicationService", "i5", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "JobDetailComponent_Template", "rf", "ctx", "JobDetailComponent_Template_button_click_2_listener", "JobDetailComponent_div_6_Template", "JobDetailComponent_div_7_Template", "JobDetailComponent_div_8_Template"], "sources": ["D:\\findit\\client\\src\\app\\components\\job-detail\\job-detail.component.ts", "D:\\findit\\client\\src\\app\\components\\job-detail\\job-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { JobService } from '../../services/job.service';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { ApplicationService } from '../../services/application.service';\r\nimport { Job } from '../../models/job.model';\r\nimport { Application } from '../../models/application.model';\r\n\r\n@Component({\r\n  selector: 'app-job-detail',\r\n  templateUrl: './job-detail.component.html',\r\n  styleUrls: ['./job-detail.component.scss']\r\n})\r\nexport class JobDetailComponent implements OnInit {\r\n  job: Job | null = null;\r\n  loading = true;\r\n  applying = false;\r\n  userApplication: Application | null = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private jobService: JobService,\r\n    private authService: AuthService,\r\n    private applicationService: ApplicationService,\r\n    private snackBar: MatSnackBar\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadJob();\r\n  }\r\n\r\n  loadJob(): void {\r\n    const jobId = Number(this.route.snapshot.paramMap.get('id'));\r\n    if (!jobId) {\r\n      this.router.navigate(['/jobs']);\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.jobService.getJobById(jobId).subscribe({\r\n      next: (response) => {\r\n        this.job = response.data.job;\r\n        this.loading = false;\r\n\r\n        // If user is authenticated and is a job seeker, check if they've already applied\r\n        if (this.isAuthenticated() && this.isJobSeeker()) {\r\n          this.checkApplicationStatus(jobId);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error loading job details', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        this.loading = false;\r\n        this.router.navigate(['/jobs']);\r\n      }\r\n    });\r\n  }\r\n\r\n  checkApplicationStatus(jobId: number): void {\r\n    this.applicationService.getUserApplications().subscribe({\r\n      next: (response) => {\r\n        const applications = response.data.applications;\r\n        this.userApplication = applications.find(app => app.jobId === jobId) || null;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error checking application status:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  applyForJob(): void {\r\n    if (!this.job || !this.job.id) return;\r\n\r\n    if (!this.isAuthenticated()) {\r\n      this.router.navigate(['/login'], {\r\n        queryParams: { returnUrl: `/jobs/${this.job.id}` }\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!this.isJobSeeker()) {\r\n      this.snackBar.open('Only job seekers can apply for jobs', 'Close', {\r\n        duration: 5000\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Open application dialog\r\n    // For simplicity, we'll just apply directly here\r\n    // In a real app, you might want to open a dialog to collect cover letter\r\n    this.applying = true;\r\n\r\n    const applicationRequest = {\r\n      jobId: this.job.id,\r\n      coverLetter: 'I am interested in this position and would like to apply.'\r\n    };\r\n\r\n    this.applicationService.applyForJob(applicationRequest).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('Application submitted successfully!', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['success-snackbar']\r\n        });\r\n        this.userApplication = response.data.application;\r\n        this.applying = false;\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(error.error?.message || 'Error submitting application', 'Close', {\r\n          duration: 5000,\r\n          panelClass: ['error-snackbar']\r\n        });\r\n        this.applying = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return this.authService.checkAuth();\r\n  }\r\n\r\n  isEmployer(): boolean {\r\n    return this.authService.isEmployer();\r\n  }\r\n\r\n  isJobSeeker(): boolean {\r\n    return this.authService.isJobSeeker();\r\n  }\r\n\r\n  isJobOwner(): boolean {\r\n    if (!this.job || !this.isAuthenticated()) return false;\r\n\r\n    // Use currentUserValue instead of getCurrentUser()\r\n    const currentUser = this.authService.currentUserValue;\r\n    return currentUser?.id === this.job.employerId;\r\n  }\r\n\r\n  getApplicationStatusClass(status: string): string {\r\n    switch (status) {\r\n      case 'applied': return 'status-applied';\r\n      case 'reviewed': return 'status-reviewed';\r\n      case 'interviewed': return 'status-interviewed';\r\n      case 'rejected': return 'status-rejected';\r\n      case 'hired': return 'status-hired';\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  editJob(): void {\r\n    if (!this.job || !this.job.id) return;\r\n    this.router.navigate(['/create-job']);\r\n  }\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/jobs']);\r\n  }\r\n}\r\n", "<div class=\"job-detail-container\">\r\n    <!-- Back button -->\r\n    <div class=\"back-button\">\r\n        <button mat-stroked-button (click)=\"goBack()\">\r\n            <mat-icon>arrow_back</mat-icon> Back to Jobs\r\n        </button>\r\n    </div>\r\n\r\n    <!-- Loading spinner -->\r\n    <div *ngIf=\"loading\" class=\"loading-container\">\r\n        <mat-spinner diameter=\"40\"></mat-spinner>\r\n    </div>\r\n\r\n    <!-- Job details -->\r\n    <div *ngIf=\"!loading && job\" class=\"job-content\">\r\n        <mat-card class=\"job-card\">\r\n            <mat-card-header>\r\n                <mat-card-title>{{ job.title }}</mat-card-title>\r\n                <mat-card-subtitle>\r\n                    <div class=\"job-subtitle\">\r\n                        <span><mat-icon>business</mat-icon> {{ job.employer?.profile?.companyName || 'Company' }}</span>\r\n                        <span><mat-icon>location_on</mat-icon> {{ job.location }}</span>\r\n                        <span><mat-icon>work</mat-icon> {{ job.jobType | titlecase }}</span>\r\n                        <span *ngIf=\"job.salaryRange\"><mat-icon>attach_money</mat-icon> {{ job.salaryRange }}</span>\r\n                    </div>\r\n                </mat-card-subtitle>\r\n            </mat-card-header>\r\n\r\n            <mat-card-content>\r\n                <div class=\"job-status\">\r\n                    <mat-chip [color]=\"job.status === 'active' ? 'primary' : 'warn'\" selected>\r\n                        {{ job.status | titlecase }}\r\n                    </mat-chip>\r\n                    <span class=\"job-date\">Posted: {{ job.createdAt | date }}</span>\r\n                </div>\r\n\r\n                <div class=\"job-section\">\r\n                    <h3>Description</h3>\r\n                    <p>{{ job.description }}</p>\r\n                </div>\r\n\r\n                <div *ngIf=\"job.requirements\" class=\"job-section\">\r\n                    <h3>Requirements</h3>\r\n                    <p>{{ job.requirements }}</p>\r\n                </div>\r\n\r\n                <!-- Application status for job seekers -->\r\n                <div *ngIf=\"isAuthenticated() && isJobSeeker() && userApplication\" class=\"application-status\">\r\n                    <h3>Your Application Status</h3>\r\n                    <div class=\"status-chip\">\r\n                        <mat-chip [ngClass]=\"getApplicationStatusClass(userApplication.status)\" selected>\r\n                            {{ userApplication.status | titlecase }}\r\n                        </mat-chip>\r\n                        <span>Applied on: {{ userApplication.createdAt | date }}</span>\r\n                    </div>\r\n                </div>\r\n            </mat-card-content>\r\n\r\n            <mat-card-actions>\r\n                <!-- Actions for job seekers -->\r\n                <div *ngIf=\"isJobSeeker() && job.status === 'active'\">\r\n                    <button *ngIf=\"!userApplication\" mat-raised-button color=\"primary\" (click)=\"applyForJob()\"\r\n                        [disabled]=\"applying\">\r\n                        <mat-spinner diameter=\"20\" *ngIf=\"applying\"></mat-spinner>\r\n                        <span *ngIf=\"!applying\">Apply Now</span>\r\n                    </button>\r\n                    <button *ngIf=\"userApplication\" mat-raised-button color=\"primary\" disabled>\r\n                        Already Applied\r\n                    </button>\r\n                </div>\r\n\r\n                <!-- Actions for employers (job owners) -->\r\n                <div *ngIf=\"isEmployer() && isJobOwner()\">\r\n                    <button mat-raised-button color=\"primary\" (click)=\"editJob()\">\r\n                        <mat-icon>edit</mat-icon> Edit Job\r\n                    </button>\r\n                    <button mat-stroked-button color=\"warn\" class=\"action-button\">\r\n                        <mat-icon>close</mat-icon> {{ job.status === 'active' ? 'Close Job' : 'Reopen Job' }}\r\n                    </button>\r\n                </div>\r\n\r\n                <!-- Login prompt for unauthenticated users -->\r\n                <div *ngIf=\"!isAuthenticated() && job.status === 'active'\">\r\n                    <button mat-raised-button color=\"primary\" [routerLink]=\"['/login']\"\r\n                        [queryParams]=\"{returnUrl: '/jobs/' + job.id}\">\r\n                        Login to Apply\r\n                    </button>\r\n                </div>\r\n            </mat-card-actions>\r\n        </mat-card>\r\n\r\n        <!-- Company info card -->\r\n        <mat-card *ngIf=\"job.employer?.profile\" class=\"company-card\">\r\n            <mat-card-header>\r\n                <mat-card-title>About the Company</mat-card-title>\r\n            </mat-card-header>\r\n            <mat-card-content>\r\n                <h3>{{ job.employer?.profile?.companyName || 'Company' }}</h3>\r\n                <p *ngIf=\"job.employer?.profile?.companyDescription\">\r\n                    {{ job.employer?.profile?.companyDescription }}\r\n                </p>\r\n                <p *ngIf=\"!job.employer?.profile?.companyDescription\">\r\n                    No company description available.\r\n                </p>\r\n            </mat-card-content>\r\n        </mat-card>\r\n    </div>\r\n\r\n    <!-- Error message -->\r\n    <div *ngIf=\"!loading && !job\" class=\"error-container\">\r\n        <mat-icon>error</mat-icon>\r\n        <p>Job not found or has been removed.</p>\r\n        <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\r\n            Back to Jobs\r\n        </button>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;ICSIA,EAAA,CAAAC,cAAA,aAA+C;IAC3CD,EAAA,CAAAE,SAAA,qBAAyC;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYcH,EAAA,CAAAC,cAAA,WAA8B;IAAUD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,GAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,GAAA,CAAAC,WAAA,KAAqB;;;;;IAkB7FT,EAAA,CAAAC,cAAA,cAAkD;IAC1CD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAA1BH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAH,GAAA,CAAAI,YAAA,CAAsB;;;;;IAI7BZ,EAAA,CAAAC,cAAA,cAA8F;IACtFD,EAAA,CAAAI,MAAA,8BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,cAAyB;IAEjBD,EAAA,CAAAI,MAAA,GACJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAkD;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAHrDH,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAa,UAAA,YAAAC,MAAA,CAAAC,yBAAA,CAAAD,MAAA,CAAAE,eAAA,CAAAC,MAAA,EAA6D;IACnEjB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAkB,WAAA,OAAAJ,MAAA,CAAAE,eAAA,CAAAC,MAAA,OACJ;IACMjB,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,kBAAA,iBAAAN,EAAA,CAAAkB,WAAA,OAAAJ,MAAA,CAAAE,eAAA,CAAAG,SAAA,MAAkD;;;;;IAUxDnB,EAAA,CAAAE,SAAA,sBAA0D;;;;;IAC1DF,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAH5CH,EAAA,CAAAC,cAAA,iBAC0B;IADyCD,EAAA,CAAAoB,UAAA,mBAAAC,0EAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEtF3B,EAAA,CAAA4B,UAAA,IAAAC,+DAAA,0BAA0D;IAC1D7B,EAAA,CAAA4B,UAAA,IAAAE,wDAAA,mBAAwC;IAC5C9B,EAAA,CAAAG,YAAA,EAAS;;;;IAHLH,EAAA,CAAAa,UAAA,aAAAkB,OAAA,CAAAC,QAAA,CAAqB;IACOhC,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAa,UAAA,SAAAkB,OAAA,CAAAC,QAAA,CAAc;IACnChC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAa,UAAA,UAAAkB,OAAA,CAAAC,QAAA,CAAe;;;;;IAE1BhC,EAAA,CAAAC,cAAA,iBAA2E;IACvED,EAAA,CAAAI,MAAA,wBACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IARbH,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAA4B,UAAA,IAAAK,iDAAA,qBAIS;IACTjC,EAAA,CAAA4B,UAAA,IAAAM,iDAAA,qBAES;IACblC,EAAA,CAAAG,YAAA,EAAM;;;;IAROH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAa,UAAA,UAAAsB,MAAA,CAAAnB,eAAA,CAAsB;IAKtBhB,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAa,UAAA,SAAAsB,MAAA,CAAAnB,eAAA,CAAqB;;;;;;IAMlChB,EAAA,CAAAC,cAAA,UAA0C;IACID,EAAA,CAAAoB,UAAA,mBAAAgB,iEAAA;MAAApC,EAAA,CAAAsB,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAY,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IACzDvC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,iBAC9B;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA8D;IAChDD,EAAA,CAAAI,MAAA,YAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,GAC/B;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IADsBH,EAAA,CAAAK,SAAA,GAC/B;IAD+BL,EAAA,CAAAM,kBAAA,MAAAkC,MAAA,CAAAhC,GAAA,CAAAS,MAAA,gDAC/B;;;;;;;;;;;;;IAIJjB,EAAA,CAAAC,cAAA,UAA2D;IAGnDD,EAAA,CAAAI,MAAA,uBACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAHiCH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAa,UAAA,eAAAb,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAyB,gBAAA1C,EAAA,CAAA2C,eAAA,IAAAC,GAAA,aAAAC,MAAA,CAAArC,GAAA,CAAAsC,EAAA;;;;;IAevE9C,EAAA,CAAAC,cAAA,QAAqD;IACjDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADAH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAyC,OAAA,CAAAvC,GAAA,CAAAwC,QAAA,kBAAAD,OAAA,CAAAvC,GAAA,CAAAwC,QAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAvC,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAAAC,kBAAA,MACJ;;;;;IACAlD,EAAA,CAAAC,cAAA,QAAsD;IAClDD,EAAA,CAAAI,MAAA,0CACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAXZH,EAAA,CAAAC,cAAA,mBAA6D;IAErCD,EAAA,CAAAI,MAAA,wBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAiB;IAEtDH,EAAA,CAAAC,cAAA,uBAAkB;IACVD,EAAA,CAAAI,MAAA,GAAqD;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAA4B,UAAA,IAAAuB,iDAAA,gBAEI;IACJnD,EAAA,CAAA4B,UAAA,IAAAwB,iDAAA,gBAEI;IACRpD,EAAA,CAAAG,YAAA,EAAmB;;;;IAPXH,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAU,iBAAA,EAAA2C,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,kBAAAK,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,kBAAAI,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAAAK,WAAA,eAAqD;IACrDtD,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAa,UAAA,SAAAwC,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,kBAAAK,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,kBAAAI,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAAAC,kBAAA,CAA+C;IAG/ClD,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAAa,UAAA,WAAAwC,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,kBAAAK,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,kBAAAI,MAAA,CAAA7C,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAAAC,kBAAA,EAAgD;;;;;IAvFhElD,EAAA,CAAAC,cAAA,aAAiD;IAGrBD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAiB;IAChDH,EAAA,CAAAC,cAAA,wBAAmB;IAEKD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,IAAqD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChGH,EAAA,CAAAC,cAAA,YAAM;IAAUD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,IAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,YAAM;IAAUD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAI,MAAA,IAA6B;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAA4B,UAAA,KAAA2B,yCAAA,mBAA4F;IAChGvD,EAAA,CAAAG,YAAA,EAAM;IAIdH,EAAA,CAAAC,cAAA,wBAAkB;IAGND,EAAA,CAAAI,MAAA,IACJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAI,MAAA,IAAkC;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGpEH,EAAA,CAAAC,cAAA,eAAyB;IACjBD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGhCH,EAAA,CAAA4B,UAAA,KAAA4B,wCAAA,kBAGM;IAGNxD,EAAA,CAAA4B,UAAA,KAAA6B,wCAAA,mBAQM;IACVzD,EAAA,CAAAG,YAAA,EAAmB;IAEnBH,EAAA,CAAAC,cAAA,wBAAkB;IAEdD,EAAA,CAAA4B,UAAA,KAAA8B,wCAAA,kBASM;IAGN1D,EAAA,CAAA4B,UAAA,KAAA+B,wCAAA,kBAOM;IAGN3D,EAAA,CAAA4B,UAAA,KAAAgC,wCAAA,kBAKM;IACV5D,EAAA,CAAAG,YAAA,EAAmB;IAIvBH,EAAA,CAAA4B,UAAA,KAAAiC,6CAAA,uBAaW;IACf7D,EAAA,CAAAG,YAAA,EAAM;;;;IAzFsBH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAU,iBAAA,CAAAoD,MAAA,CAAAtD,GAAA,CAAAuD,KAAA,CAAe;IAGa/D,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAM,kBAAA,OAAAwD,MAAA,CAAAtD,GAAA,CAAAwC,QAAA,kBAAAc,MAAA,CAAAtD,GAAA,CAAAwC,QAAA,CAAAC,OAAA,kBAAAa,MAAA,CAAAtD,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAAAK,WAAA,mBAAqD;IAClDtD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,kBAAA,MAAAwD,MAAA,CAAAtD,GAAA,CAAAwD,QAAA,KAAkB;IACzBhE,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAkB,WAAA,SAAA4C,MAAA,CAAAtD,GAAA,CAAAyD,OAAA,MAA6B;IACtDjE,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAtD,GAAA,CAAAC,WAAA,CAAqB;IAOtBT,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAa,UAAA,UAAAiD,MAAA,CAAAtD,GAAA,CAAAS,MAAA,mCAAsD;IAC5DjB,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAkB,WAAA,SAAA4C,MAAA,CAAAtD,GAAA,CAAAS,MAAA,OACJ;IACuBjB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,aAAAN,EAAA,CAAAkB,WAAA,SAAA4C,MAAA,CAAAtD,GAAA,CAAAW,SAAA,MAAkC;IAKtDnB,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAU,iBAAA,CAAAoD,MAAA,CAAAtD,GAAA,CAAA0D,WAAA,CAAqB;IAGtBlE,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAtD,GAAA,CAAAI,YAAA,CAAsB;IAMtBZ,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAK,eAAA,MAAAL,MAAA,CAAAM,WAAA,MAAAN,MAAA,CAAA9C,eAAA,CAA2D;IAa3DhB,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAM,WAAA,MAAAN,MAAA,CAAAtD,GAAA,CAAAS,MAAA,cAA8C;IAY9CjB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAO,UAAA,MAAAP,MAAA,CAAAQ,UAAA,GAAkC;IAUlCtE,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAa,UAAA,UAAAiD,MAAA,CAAAK,eAAA,MAAAL,MAAA,CAAAtD,GAAA,CAAAS,MAAA,cAAmD;IAUtDjB,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAa,UAAA,SAAAiD,MAAA,CAAAtD,GAAA,CAAAwC,QAAA,kBAAAc,MAAA,CAAAtD,GAAA,CAAAwC,QAAA,CAAAC,OAAA,CAA2B;;;;;;IAiB1CjD,EAAA,CAAAC,cAAA,cAAsD;IACxCD,EAAA,CAAAI,MAAA,YAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,yCAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzCH,EAAA,CAAAC,cAAA,iBAA6D;IAAnBD,EAAA,CAAAoB,UAAA,mBAAAmD,0DAAA;MAAAvE,EAAA,CAAAsB,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAAzE,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAA+C,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACxD1E,EAAA,CAAAI,MAAA,qBACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;ADpGjB,OAAM,MAAOwE,kBAAkB;EAM7BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,UAAsB,EACtBC,WAAwB,EACxBC,kBAAsC,EACtCC,QAAqB;IALrB,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,QAAQ,GAARA,QAAQ;IAXlB,KAAA1E,GAAG,GAAe,IAAI;IACtB,KAAA2E,OAAO,GAAG,IAAI;IACd,KAAAnD,QAAQ,GAAG,KAAK;IAChB,KAAAhB,eAAe,GAAuB,IAAI;EAStC;EAEJoE,QAAQA,CAAA;IACN,IAAI,CAACC,OAAO,EAAE;EAChB;EAEAA,OAAOA,CAAA;IACL,MAAMC,KAAK,GAAGC,MAAM,CAAC,IAAI,CAACV,KAAK,CAACW,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5D,IAAI,CAACJ,KAAK,EAAE;MACV,IAAI,CAACR,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B;;IAGF,IAAI,CAACR,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,UAAU,CAACa,UAAU,CAACN,KAAK,CAAC,CAACO,SAAS,CAAC;MAC1CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACvF,GAAG,GAAGuF,QAAQ,CAACC,IAAI,CAACxF,GAAG;QAC5B,IAAI,CAAC2E,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,IAAI,CAAChB,eAAe,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE,EAAE;UAChD,IAAI,CAAC6B,sBAAsB,CAACX,KAAK,CAAC;;MAEtC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAACD,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;UAC/EC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACnB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACL,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC;KACD,CAAC;EACJ;EAEAM,sBAAsBA,CAACX,KAAa;IAClC,IAAI,CAACL,kBAAkB,CAACsB,mBAAmB,EAAE,CAACV,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAMS,YAAY,GAAGT,QAAQ,CAACC,IAAI,CAACQ,YAAY;QAC/C,IAAI,CAACxF,eAAe,GAAGwF,YAAY,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpB,KAAK,KAAKA,KAAK,CAAC,IAAI,IAAI;MAC9E,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfS,OAAO,CAACT,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;KACD,CAAC;EACJ;EAEAvE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnB,GAAG,IAAI,CAAC,IAAI,CAACA,GAAG,CAACsC,EAAE,EAAE;IAE/B,IAAI,CAAC,IAAI,CAACqB,eAAe,EAAE,EAAE;MAC3B,IAAI,CAACW,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC/BiB,WAAW,EAAE;UAAEC,SAAS,EAAE,SAAS,IAAI,CAACrG,GAAG,CAACsC,EAAE;QAAE;OACjD,CAAC;MACF;;IAGF,IAAI,CAAC,IAAI,CAACsB,WAAW,EAAE,EAAE;MACvB,IAAI,CAACc,QAAQ,CAACiB,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;QACjEE,QAAQ,EAAE;OACX,CAAC;MACF;;IAGF;IACA;IACA;IACA,IAAI,CAACrE,QAAQ,GAAG,IAAI;IAEpB,MAAM8E,kBAAkB,GAAG;MACzBxB,KAAK,EAAE,IAAI,CAAC9E,GAAG,CAACsC,EAAE;MAClBiE,WAAW,EAAE;KACd;IAED,IAAI,CAAC9B,kBAAkB,CAACtD,WAAW,CAACmF,kBAAkB,CAAC,CAACjB,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACb,QAAQ,CAACiB,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;UACjEE,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,kBAAkB;SAChC,CAAC;QACF,IAAI,CAACtF,eAAe,GAAG+E,QAAQ,CAACC,IAAI,CAACgB,WAAW;QAChD,IAAI,CAAChF,QAAQ,GAAG,KAAK;MACvB,CAAC;MACDkE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAACD,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,8BAA8B,EAAE,OAAO,EAAE;UAClFC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QACF,IAAI,CAACtE,QAAQ,GAAG,KAAK;MACvB;KACD,CAAC;EACJ;EAEAmC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACa,WAAW,CAACiC,SAAS,EAAE;EACrC;EAEA5C,UAAUA,CAAA;IACR,OAAO,IAAI,CAACW,WAAW,CAACX,UAAU,EAAE;EACtC;EAEAD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACY,WAAW,CAACZ,WAAW,EAAE;EACvC;EAEAE,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC9D,GAAG,IAAI,CAAC,IAAI,CAAC2D,eAAe,EAAE,EAAE,OAAO,KAAK;IAEtD;IACA,MAAM+C,WAAW,GAAG,IAAI,CAAClC,WAAW,CAACmC,gBAAgB;IACrD,OAAOD,WAAW,EAAEpE,EAAE,KAAK,IAAI,CAACtC,GAAG,CAAC4G,UAAU;EAChD;EAEArG,yBAAyBA,CAACE,MAAc;IACtC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,aAAa;QAAE,OAAO,oBAAoB;MAC/C,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC;QAAS,OAAO,EAAE;;EAEtB;EAEAsB,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAAC/B,GAAG,IAAI,CAAC,IAAI,CAACA,GAAG,CAACsC,EAAE,EAAE;IAC/B,IAAI,CAACgC,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAjB,MAAMA,CAAA;IACJ,IAAI,CAACI,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBAhJWhB,kBAAkB,EAAA3E,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvH,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxH,EAAA,CAAAqH,iBAAA,CAAAI,EAAA,CAAAC,UAAA,GAAA1H,EAAA,CAAAqH,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA5H,EAAA,CAAAqH,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA,GAAA9H,EAAA,CAAAqH,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBrD,kBAAkB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd/BvI,EAAA,CAAAC,cAAA,aAAkC;UAGCD,EAAA,CAAAoB,UAAA,mBAAAqH,oDAAA;YAAA,OAASD,GAAA,CAAA9D,MAAA,EAAQ;UAAA,EAAC;UACzC1E,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAI,MAAA,iBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAACH,EAAA,CAAAI,MAAA,qBACpC;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAA4B,UAAA,IAAA8G,iCAAA,iBAEM;UAGN1I,EAAA,CAAA4B,UAAA,IAAA+G,iCAAA,mBA4FM;UAGN3I,EAAA,CAAA4B,UAAA,IAAAgH,iCAAA,iBAMM;UACV5I,EAAA,CAAAG,YAAA,EAAM;;;UA3GIH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAa,UAAA,SAAA2H,GAAA,CAAArD,OAAA,CAAa;UAKbnF,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAa,UAAA,UAAA2H,GAAA,CAAArD,OAAA,IAAAqD,GAAA,CAAAhI,GAAA,CAAqB;UA+FrBR,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAa,UAAA,UAAA2H,GAAA,CAAArD,OAAA,KAAAqD,GAAA,CAAAhI,GAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}