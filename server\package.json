{"name": "findit-server", "version": "1.0.0", "description": "Backend for FindIt job application platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:sync": "SYNC_DB=true nodemon server.js", "sync-db": "SYNC_DB=true node server.js", "init-db": "node utils/initDb.js", "test-api": "node utils/testApi.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"axios": "^1.9.0", "nodemon": "^3.1.10"}}