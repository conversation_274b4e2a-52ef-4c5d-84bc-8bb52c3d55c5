{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/table';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]]];\nconst _c1 = [\"caption\", \"colgroup, col\"];\nfunction MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r1.justify);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.dataAccessor(data_r2, ctx_r1.name), \" \");\n  }\n}\nclass MatRecycleRows {\n  static {\n    this.ɵfac = function MatRecycleRows_Factory(t) {\n      return new (t || MatRecycleRows)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRecycleRows,\n      selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\nclass MatTable extends CdkTable {\n  constructor() {\n    super(...arguments);\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    this.stickyCssClass = 'mat-mdc-table-sticky';\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    this.needsPositionStickyOnElement = false;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    // After ngOnInit, the `CdkTable` has created and inserted the table sections (thead, tbody,\n    // tfoot). MDC requires the `mdc-data-table__content` class to be added to the body. Note that\n    // this only applies to native tables, because we don't wrap the content of flexbox-based ones.\n    if (this._isNativeHtmlTable) {\n      const tbody = this._elementRef.nativeElement.querySelector('tbody');\n      tbody.classList.add('mdc-data-table__content');\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTable_BaseFactory;\n      return function MatTable_Factory(t) {\n        return (ɵMatTable_BaseFactory || (ɵMatTable_BaseFactory = i0.ɵɵgetInheritedFactory(MatTable)))(t || MatTable);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTable,\n      selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n      hostVars: 2,\n      hostBindings: function MatTable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n        }\n      },\n      exportAs: [\"matTable\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 0,\n      consts: [[\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n      template: function MatTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵelementContainer(2, 0)(3, 1)(4, 2)(5, 3);\n        }\n      },\n      dependencies: [i1.DataRowOutlet, i1.HeaderRowOutlet, i1.FooterRowOutlet, i1.NoDataRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{--mat-table-row-item-outline-width:1px;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTable, [{\n    type: Component,\n    args: [{\n      selector: 'mat-table, table[mat-table]',\n      exportAs: 'matTable',\n      template: `\n    <ng-content select=\"caption\"></ng-content>\n    <ng-content select=\"colgroup, col\"></ng-content>\n    <ng-container headerRowOutlet></ng-container>\n    <ng-container rowOutlet></ng-container>\n    <ng-container noDataRowOutlet></ng-container>\n    <ng-container footerRowOutlet></ng-container>\n  `,\n      host: {\n        'class': 'mat-mdc-table mdc-data-table__table',\n        '[class.mdc-table-fixed-layout]': 'fixedLayout',\n        'ngSkipHydration': ''\n      },\n      providers: [{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{--mat-table-row-item-outline-width:1px;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatCellDef_BaseFactory;\n      return function MatCellDef_Factory(t) {\n        return (ɵMatCellDef_BaseFactory || (ɵMatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatCellDef)))(t || MatCellDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCellDef,\n      selectors: [[\"\", \"matCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matCellDef]',\n      providers: [{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatHeaderCellDef_BaseFactory;\n      return function MatHeaderCellDef_Factory(t) {\n        return (ɵMatHeaderCellDef_BaseFactory || (ɵMatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCellDef)))(t || MatHeaderCellDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCellDef,\n      selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderCellDef]',\n      providers: [{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatFooterCellDef_BaseFactory;\n      return function MatFooterCellDef_Factory(t) {\n        return (ɵMatFooterCellDef_BaseFactory || (ɵMatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCellDef)))(t || MatFooterCellDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCellDef,\n      selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterCellDef]',\n      providers: [{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatColumnDef_BaseFactory;\n      return function MatColumnDef_Factory(t) {\n        return (ɵMatColumnDef_BaseFactory || (ɵMatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatColumnDef)))(t || MatColumnDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatColumnDef,\n      selectors: [[\"\", \"matColumnDef\", \"\"]],\n      inputs: {\n        sticky: \"sticky\",\n        name: [\"matColumnDef\", \"name\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matColumnDef]',\n      inputs: ['sticky'],\n      providers: [{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]\n    }]\n  }], null, {\n    name: [{\n      type: Input,\n      args: ['matColumnDef']\n    }]\n  });\n})();\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatHeaderCell_BaseFactory;\n      return function MatHeaderCell_Factory(t) {\n        return (ɵMatHeaderCell_BaseFactory || (ɵMatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCell)))(t || MatHeaderCell);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCell,\n      selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n      hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-header-cell, th[mat-header-cell]',\n      host: {\n        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], null, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatFooterCell_BaseFactory;\n      return function MatFooterCell_Factory(t) {\n        return (ɵMatFooterCell_BaseFactory || (ɵMatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCell)))(t || MatFooterCell);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCell,\n      selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-footer-cell, td[mat-footer-cell]',\n      host: {\n        'class': 'mat-mdc-footer-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatCell_BaseFactory;\n      return function MatCell_Factory(t) {\n        return (ɵMatCell_BaseFactory || (ɵMatCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatCell)))(t || MatCell);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCell,\n      selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-cell, td[mat-cell]',\n      host: {\n        'class': 'mat-mdc-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatHeaderRowDef_BaseFactory;\n      return function MatHeaderRowDef_Factory(t) {\n        return (ɵMatHeaderRowDef_BaseFactory || (ɵMatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRowDef)))(t || MatHeaderRowDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderRowDef,\n      selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n      inputs: {\n        columns: [\"matHeaderRowDef\", \"columns\"],\n        sticky: [\"matHeaderRowDefSticky\", \"sticky\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderRowDef]',\n      providers: [{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }],\n      inputs: ['columns: matHeaderRowDef', 'sticky: matHeaderRowDefSticky']\n    }]\n  }], null, null);\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatFooterRowDef_BaseFactory;\n      return function MatFooterRowDef_Factory(t) {\n        return (ɵMatFooterRowDef_BaseFactory || (ɵMatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRowDef)))(t || MatFooterRowDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterRowDef,\n      selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n      inputs: {\n        columns: [\"matFooterRowDef\", \"columns\"],\n        sticky: [\"matFooterRowDefSticky\", \"sticky\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterRowDef]',\n      providers: [{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }],\n      inputs: ['columns: matFooterRowDef', 'sticky: matFooterRowDefSticky']\n    }]\n  }], null, null);\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatRowDef_BaseFactory;\n      return function MatRowDef_Factory(t) {\n        return (ɵMatRowDef_BaseFactory || (ɵMatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatRowDef)))(t || MatRowDef);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRowDef,\n      selectors: [[\"\", \"matRowDef\", \"\"]],\n      inputs: {\n        columns: [\"matRowDefColumns\", \"columns\"],\n        when: [\"matRowDefWhen\", \"when\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matRowDef]',\n      providers: [{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }],\n      inputs: ['columns: matRowDefColumns', 'when: matRowDefWhen']\n    }]\n  }], null, null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatHeaderRow_BaseFactory;\n      return function MatHeaderRow_Factory(t) {\n        return (ɵMatHeaderRow_BaseFactory || (ɵMatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRow)))(t || MatHeaderRow);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatHeaderRow,\n      selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n      exportAs: [\"matHeaderRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatHeaderRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [i1.CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-header-row, tr[mat-header-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matHeaderRow',\n      providers: [{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatFooterRow_BaseFactory;\n      return function MatFooterRow_Factory(t) {\n        return (ɵMatFooterRow_BaseFactory || (ɵMatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRow)))(t || MatFooterRow);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFooterRow,\n      selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matFooterRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatFooterRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [i1.CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-footer-row, tr[mat-footer-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-footer-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matFooterRow',\n      providers: [{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatRow_BaseFactory;\n      return function MatRow_Factory(t) {\n        return (ɵMatRow_BaseFactory || (ɵMatRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatRow)))(t || MatRow);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRow,\n      selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRow,\n        useExisting: MatRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [i1.CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-row, tr[mat-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matRow',\n      providers: [{\n        provide: CdkRow,\n        useExisting: MatRow\n      }]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n  constructor() {\n    super(...arguments);\n    this._contentClassName = 'mat-mdc-no-data-row';\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatNoDataRow_BaseFactory;\n      return function MatNoDataRow_Factory(t) {\n        return (ɵMatNoDataRow_BaseFactory || (ɵMatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatNoDataRow)))(t || MatNoDataRow);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatNoDataRow,\n      selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matNoDataRow]',\n      providers: [{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTextColumn_BaseFactory;\n      return function MatTextColumn_Factory(t) {\n        return (ɵMatTextColumn_BaseFactory || (ɵMatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(MatTextColumn)))(t || MatTextColumn);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTextColumn,\n      selectors: [[\"mat-text-column\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n      template: function MatTextColumn_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, MatTextColumn_th_1_Template, 2, 3, \"th\", 1);\n          i0.ɵɵtemplate(2, MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n          i0.ɵɵelementContainerEnd();\n        }\n      },\n      dependencies: [MatHeaderCellDef, MatColumnDef, MatCellDef, MatHeaderCell, MatCell],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'mat-text-column',\n      template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default\n    }]\n  }], null, null);\n})();\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nclass MatTableModule {\n  static {\n    this.ɵfac = function MatTableModule_Factory(t) {\n      return new (t || MatTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkTableModule],\n      exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n      declarations: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/** Shared base class with MDC-based implementation. */\nclass _MatTableDataSource extends DataSource {\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  constructor(initialData = []) {\n    super();\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    this._renderData = new BehaviorSubject([]);\n    /** Stream that emits when a new filter string is set on the data source. */\n    this._filter = new BehaviorSubject('');\n    /** Used to react to internal changes of the paginator that are made by the data source itself. */\n    this._internalPageChanges = new Subject();\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    this._renderChangesSubscription = null;\n    /**\n     * Data accessor function that is used for accessing data properties for sorting through\n     * the default sortData function.\n     * This default function assumes that the sort header IDs (which defaults to the column name)\n     * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n     * May be set to a custom function for different behavior.\n     * @param data Data object that is being accessed.\n     * @param sortHeaderId The name of the column that represents the data.\n     */\n    this.sortingDataAccessor = (data, sortHeaderId) => {\n      const value = data[sortHeaderId];\n      if (_isNumberValue(value)) {\n        const numberValue = Number(value);\n        // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n        // leave them as strings. For more info: https://goo.gl/y5vbSg\n        return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n      }\n      return value;\n    };\n    /**\n     * Gets a sorted copy of the data array based on the state of the MatSort. Called\n     * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n     * By default, the function retrieves the active sort and its direction and compares data\n     * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n     * of data ordering.\n     * @param data The array of data that should be sorted.\n     * @param sort The connected MatSort that holds the current sort state.\n     */\n    this.sortData = (data, sort) => {\n      const active = sort.active;\n      const direction = sort.direction;\n      if (!active || direction == '') {\n        return data;\n      }\n      return data.sort((a, b) => {\n        let valueA = this.sortingDataAccessor(a, active);\n        let valueB = this.sortingDataAccessor(b, active);\n        // If there are data in the column that can be converted to a number,\n        // it must be ensured that the rest of the data\n        // is of the same type so as not to order incorrectly.\n        const valueAType = typeof valueA;\n        const valueBType = typeof valueB;\n        if (valueAType !== valueBType) {\n          if (valueAType === 'number') {\n            valueA += '';\n          }\n          if (valueBType === 'number') {\n            valueB += '';\n          }\n        }\n        // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n        // one value exists while the other doesn't. In this case, existing value should come last.\n        // This avoids inconsistent results when comparing values to undefined/null.\n        // If neither value exists, return 0 (equal).\n        let comparatorResult = 0;\n        if (valueA != null && valueB != null) {\n          // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n          if (valueA > valueB) {\n            comparatorResult = 1;\n          } else if (valueA < valueB) {\n            comparatorResult = -1;\n          }\n        } else if (valueA != null) {\n          comparatorResult = 1;\n        } else if (valueB != null) {\n          comparatorResult = -1;\n        }\n        return comparatorResult * (direction == 'asc' ? 1 : -1);\n      });\n    };\n    /**\n     * Checks if a data object matches the data source's filter string. By default, each data object\n     * is converted to a string of its properties and returns true if the filter has\n     * at least one occurrence in that string. By default, the filter string has its whitespace\n     * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n     * filter matching.\n     * @param data Data object used to check against the filter.\n     * @param filter Filter string that has been set on the data source.\n     * @returns Whether the filter matches against the data\n     */\n    this.filterPredicate = (data, filter) => {\n      // Transform the data into a lowercase string of all property values.\n      const dataStr = Object.keys(data).reduce((currentTerm, key) => {\n        // Use an obscure Unicode character to delimit the words in the concatenated string.\n        // This avoids matches where the values of two columns combined will match the user's query\n        // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n        // that has a very low chance of being typed in by somebody in a text field. This one in\n        // particular is \"White up-pointing triangle with dot\" from\n        // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n        return currentTerm + data[key] + '◬';\n      }, '').toLowerCase();\n      // Transform the filter by converting it to lowercase and removing whitespace.\n      const transformedFilter = filter.trim().toLowerCase();\n      return dataStr.indexOf(transformedFilter) != -1;\n    };\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends _MatTableDataSource {}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn, _MatTableDataSource };", "map": {"version": 3, "names": ["i0", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "i1", "CdkTable", "CDK_TABLE", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "STICKY_POSITIONING_LISTENER", "CdkCellDef", "CdkHeaderCellDef", "CdkFooterCellDef", "CdkColumnDef", "CdkHeaderCell", "CdkFooterCell", "CdkCell", "CdkHeaderRowDef", "CdkFooterRowDef", "CdkRowDef", "CdkHeaderRow", "CdkFooterRow", "CdkRow", "CdkNoDataRow", "CdkTextColumn", "CdkTableModule", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_DisposeViewRepeaterStrategy", "DataSource", "MatCommonModule", "BehaviorSubject", "Subject", "merge", "of", "combineLatest", "_isNumberValue", "map", "_c0", "_c1", "MatTextColumn_th_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "MatTextColumn_td_2_Template", "data_r2", "$implicit", "ctx_r1", "dataAccessor", "name", "MatRecycleRows", "ɵfac", "MatRecycleRows_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "features", "ɵɵProvidersFeature", "provide", "useClass", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MatTable", "constructor", "arguments", "stickyCssClass", "needsPositionStickyOnElement", "ngOnInit", "_isNativeHtmlTable", "tbody", "_elementRef", "nativeElement", "querySelector", "classList", "add", "ɵMatTable_BaseFactory", "MatTable_Factory", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "hostVars", "hostBindings", "MatTable_HostBindings", "ɵɵclassProp", "fixedLayout", "exportAs", "useExisting", "useValue", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatTable_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵelementContainer", "dependencies", "DataRowOutlet", "HeaderRowOutlet", "FooterRowOutlet", "NoDataRowOutlet", "styles", "encapsulation", "host", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "MatCellDef", "ɵMatCellDef_BaseFactory", "MatCellDef_Factory", "MatHeaderCellDef", "ɵMatHeaderCellDef_BaseFactory", "MatHeaderCellDef_Factory", "MatFooterCellDef", "ɵMatFooterCellDef_BaseFactory", "MatFooterCellDef_Factory", "MatColumnDef", "_name", "_setNameInput", "_updateColumnCssClassName", "_columnCssClassName", "push", "cssClassFriendlyName", "ɵMatColumnDef_BaseFactory", "MatColumnDef_Factory", "inputs", "sticky", "MatHeaderCell", "ɵMatHeaderCell_BaseFactory", "MatHeaderCell_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵMatFooterCell_BaseFactory", "MatFooterCell_Factory", "Mat<PERSON>ell", "ɵMatCell_BaseFactory", "MatCell_Factory", "ROW_TEMPLATE", "MatHeaderRowDef", "ɵMatHeaderRowDef_BaseFactory", "MatHeaderRowDef_Factory", "columns", "MatFooterRowDef", "ɵMatFooterRowDef_BaseFactory", "MatFooterRowDef_Factory", "MatRowDef", "ɵMatRowDef_BaseFactory", "MatRowDef_Factory", "when", "MatHeaderRow", "ɵMatHeaderRow_BaseFactory", "MatHeaderRow_Factory", "MatHeaderRow_Template", "CdkCellOutlet", "MatFooterRow", "ɵMatFooterRow_BaseFactory", "MatFooterRow_Factory", "MatFooterRow_Template", "MatRow", "ɵMatRow_BaseFactory", "MatRow_Factory", "MatRow_Template", "MatNoDataRow", "_contentClassName", "ɵMatNoDataRow_BaseFactory", "MatNoDataRow_Factory", "MatTextColumn", "ɵMatTextColumn_BaseFactory", "MatTextColumn_Factory", "MatTextColumn_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "EXPORTED_DECLARATIONS", "MatTableModule", "MatTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations", "MAX_SAFE_INTEGER", "_MatTableDataSource", "data", "_data", "value", "Array", "isArray", "next", "_renderChangesSubscription", "_filterData", "filter", "_filter", "sort", "_sort", "_updateChangeSubscription", "paginator", "_paginator", "initialData", "_renderData", "_internalPageChanges", "sortingDataAccessor", "sortHeaderId", "numberValue", "Number", "sortData", "active", "direction", "a", "b", "valueA", "valueB", "valueAType", "valueBType", "comparatorResult", "filterPredicate", "dataStr", "Object", "keys", "reduce", "currentTerm", "key", "toLowerCase", "<PERSON><PERSON><PERSON>er", "trim", "indexOf", "sortChange", "initialized", "pageChange", "page", "dataStream", "filteredData", "pipe", "orderedData", "_orderData", "paginatedData", "_pageData", "unsubscribe", "subscribe", "obj", "_updatePaginator", "length", "slice", "startIndex", "pageIndex", "pageSize", "filteredDataLength", "Promise", "resolve", "then", "lastPageIndex", "Math", "ceil", "newPageIndex", "min", "connect", "disconnect", "MatTableDataSource"], "sources": ["D:/findit/client/node_modules/@angular/material/fesm2022/table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/table';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass MatRecycleRows {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRecycleRows, selector: \"mat-table[recycleRows], table[mat-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\nclass MatTable extends CdkTable {\n    constructor() {\n        super(...arguments);\n        /** Overrides the sticky CSS class set by the `CdkTable`. */\n        this.stickyCssClass = 'mat-mdc-table-sticky';\n        /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n        this.needsPositionStickyOnElement = false;\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        // After ngOnInit, the `CdkTable` has created and inserted the table sections (thead, tbody,\n        // tfoot). MDC requires the `mdc-data-table__content` class to be added to the body. Note that\n        // this only applies to native tables, because we don't wrap the content of flexbox-based ones.\n        if (this._isNativeHtmlTable) {\n            const tbody = this._elementRef.nativeElement.querySelector('tbody');\n            tbody.classList.add('mdc-data-table__content');\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTable, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTable, selector: \"mat-table, table[mat-table]\", host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"class.mdc-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"mat-mdc-table mdc-data-table__table\" }, providers: [\n            { provide: CdkTable, useExisting: MatTable },\n            { provide: CDK_TABLE, useExisting: MatTable },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n            //  is only included in the build if used.\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], exportAs: [\"matTable\"], usesInheritance: true, ngImport: i0, template: `\n    <ng-content select=\"caption\"></ng-content>\n    <ng-content select=\"colgroup, col\"></ng-content>\n    <ng-container headerRowOutlet></ng-container>\n    <ng-container rowOutlet></ng-container>\n    <ng-container noDataRowOutlet></ng-container>\n    <ng-container footerRowOutlet></ng-container>\n  `, isInline: true, styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{--mat-table-row-item-outline-width:1px;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"], dependencies: [{ kind: \"directive\", type: i1.DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: i1.HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: i1.FooterRowOutlet, selector: \"[footerRowOutlet]\" }, { kind: \"directive\", type: i1.NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-table, table[mat-table]', exportAs: 'matTable', template: `\n    <ng-content select=\"caption\"></ng-content>\n    <ng-content select=\"colgroup, col\"></ng-content>\n    <ng-container headerRowOutlet></ng-container>\n    <ng-container rowOutlet></ng-container>\n    <ng-container noDataRowOutlet></ng-container>\n    <ng-container footerRowOutlet></ng-container>\n  `, host: {\n                        'class': 'mat-mdc-table mdc-data-table__table',\n                        '[class.mdc-table-fixed-layout]': 'fixedLayout',\n                        'ngSkipHydration': '',\n                    }, providers: [\n                        { provide: CdkTable, useExisting: MatTable },\n                        { provide: CDK_TABLE, useExisting: MatTable },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n                        //  is only included in the build if used.\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{--mat-table-row-item-outline-width:1px;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"] }]\n        }] });\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCellDef, selector: \"[matCellDef]\", providers: [{ provide: CdkCellDef, useExisting: MatCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matCellDef]',\n                    providers: [{ provide: CdkCellDef, useExisting: MatCellDef }],\n                }]\n        }] });\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\", providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderCellDef]',\n                    providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }],\n                }]\n        }] });\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatFooterCellDef, selector: \"[matFooterCellDef]\", providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterCellDef]',\n                    providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }],\n                }]\n        }] });\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /**\n     * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n     * In the future, this will only add \"mat-column-\" and columnCssClassName\n     * will change from type string[] to string.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        super._updateColumnCssClassName();\n        this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatColumnDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: { sticky: \"sticky\", name: [\"matColumnDef\", \"name\"] }, providers: [\n            { provide: CdkColumnDef, useExisting: MatColumnDef },\n            { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n        ], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matColumnDef]',\n                    inputs: ['sticky'],\n                    providers: [\n                        { provide: CdkColumnDef, useExisting: MatColumnDef },\n                        { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n                    ],\n                }]\n        }], propDecorators: { name: [{\n                type: Input,\n                args: ['matColumnDef']\n            }] } });\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"mat-mdc-header-cell mdc-data-table__header-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-header-cell, th[mat-header-cell]',\n                    host: {\n                        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }] });\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatFooterCell, selector: \"mat-footer-cell, td[mat-footer-cell]\", host: { classAttribute: \"mat-mdc-footer-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-footer-cell, td[mat-footer-cell]',\n                    host: {\n                        'class': 'mat-mdc-footer-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCell, selector: \"mat-cell, td[mat-cell]\", host: { classAttribute: \"mat-mdc-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-cell, td[mat-cell]',\n                    host: {\n                        'class': 'mat-mdc-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatHeaderRowDef, selector: \"[matHeaderRowDef]\", inputs: { columns: [\"matHeaderRowDef\", \"columns\"], sticky: [\"matHeaderRowDefSticky\", \"sticky\"] }, providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderRowDef]',\n                    providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }],\n                    inputs: ['columns: matHeaderRowDef', 'sticky: matHeaderRowDefSticky'],\n                }]\n        }] });\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatFooterRowDef, selector: \"[matFooterRowDef]\", inputs: { columns: [\"matFooterRowDef\", \"columns\"], sticky: [\"matFooterRowDefSticky\", \"sticky\"] }, providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterRowDef]',\n                    providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }],\n                    inputs: ['columns: matFooterRowDef', 'sticky: matFooterRowDefSticky'],\n                }]\n        }] });\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRowDef, selector: \"[matRowDef]\", inputs: { columns: [\"matRowDefColumns\", \"columns\"], when: [\"matRowDefWhen\", \"when\"] }, providers: [{ provide: CdkRowDef, useExisting: MatRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matRowDef]',\n                    providers: [{ provide: CdkRowDef, useExisting: MatRowDef }],\n                    inputs: ['columns: matRowDefColumns', 'when: matRowDefWhen'],\n                }]\n        }] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatHeaderRow, selector: \"mat-header-row, tr[mat-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-header-row mdc-data-table__header-row\" }, providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }], exportAs: [\"matHeaderRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-header-row, tr[mat-header-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matHeaderRow',\n                    providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatFooterRow, selector: \"mat-footer-row, tr[mat-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-footer-row mdc-data-table__row\" }, providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }], exportAs: [\"matFooterRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-footer-row, tr[mat-footer-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-footer-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matFooterRow',\n                    providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRow, selector: \"mat-row, tr[mat-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-row mdc-data-table__row\" }, providers: [{ provide: CdkRow, useExisting: MatRow }], exportAs: [\"matRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: i1.CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-row, tr[mat-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matRow',\n                    providers: [{ provide: CdkRow, useExisting: MatRow }],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n    constructor() {\n        super(...arguments);\n        this._contentClassName = 'mat-mdc-no-data-row';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNoDataRow, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatNoDataRow, selector: \"ng-template[matNoDataRow]\", providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matNoDataRow]',\n                    providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }],\n                }]\n        }] });\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTextColumn, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTextColumn, selector: \"mat-text-column\", usesInheritance: true, ngImport: i0, template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\" }, { kind: \"directive\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: [\"sticky\", \"matColumnDef\"] }, { kind: \"directive\", type: MatCellDef, selector: \"[matCellDef]\" }, { kind: \"directive\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\" }, { kind: \"directive\", type: MatCell, selector: \"mat-cell, td[mat-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-text-column',\n                    template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                }]\n        }] });\n\nconst EXPORTED_DECLARATIONS = [\n    // Table\n    MatTable,\n    MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef,\n    MatHeaderRowDef,\n    MatColumnDef,\n    MatCellDef,\n    MatRowDef,\n    MatFooterCellDef,\n    MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell,\n    MatCell,\n    MatFooterCell,\n    // Row directives\n    MatHeaderRow,\n    MatRow,\n    MatFooterRow,\n    MatNoDataRow,\n    MatTextColumn,\n];\nclass MatTableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTableModule, declarations: [\n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn], imports: [MatCommonModule, CdkTableModule], exports: [MatCommonModule, \n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CdkTableModule],\n                    exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n                    declarations: EXPORTED_DECLARATIONS,\n                }]\n        }] });\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/** Shared base class with MDC-based implementation. */\nclass _MatTableDataSource extends DataSource {\n    /** Array of data that should be rendered by the table, where each object represents one row. */\n    get data() {\n        return this._data.value;\n    }\n    set data(data) {\n        data = Array.isArray(data) ? data : [];\n        this._data.next(data);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(data);\n        }\n    }\n    /**\n     * Filter term that should be used to filter out objects from the data array. To override how\n     * data objects match to this filter string, provide a custom function for filterPredicate.\n     */\n    get filter() {\n        return this._filter.value;\n    }\n    set filter(filter) {\n        this._filter.next(filter);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(this.data);\n        }\n    }\n    /**\n     * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n     * emitted by the MatSort will trigger an update to the table's rendered data.\n     */\n    get sort() {\n        return this._sort;\n    }\n    set sort(sort) {\n        this._sort = sort;\n        this._updateChangeSubscription();\n    }\n    /**\n     * Instance of the paginator component used by the table to control what page of the data is\n     * displayed. Page changes emitted by the paginator will trigger an update to the\n     * table's rendered data.\n     *\n     * Note that the data source uses the paginator's properties to calculate which page of data\n     * should be displayed. If the paginator receives its properties as template inputs,\n     * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n     * initialized before assigning it to this data source.\n     */\n    get paginator() {\n        return this._paginator;\n    }\n    set paginator(paginator) {\n        this._paginator = paginator;\n        this._updateChangeSubscription();\n    }\n    constructor(initialData = []) {\n        super();\n        /** Stream emitting render data to the table (depends on ordered data changes). */\n        this._renderData = new BehaviorSubject([]);\n        /** Stream that emits when a new filter string is set on the data source. */\n        this._filter = new BehaviorSubject('');\n        /** Used to react to internal changes of the paginator that are made by the data source itself. */\n        this._internalPageChanges = new Subject();\n        /**\n         * Subscription to the changes that should trigger an update to the table's rendered rows, such\n         * as filtering, sorting, pagination, or base data changes.\n         */\n        this._renderChangesSubscription = null;\n        /**\n         * Data accessor function that is used for accessing data properties for sorting through\n         * the default sortData function.\n         * This default function assumes that the sort header IDs (which defaults to the column name)\n         * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n         * May be set to a custom function for different behavior.\n         * @param data Data object that is being accessed.\n         * @param sortHeaderId The name of the column that represents the data.\n         */\n        this.sortingDataAccessor = (data, sortHeaderId) => {\n            const value = data[sortHeaderId];\n            if (_isNumberValue(value)) {\n                const numberValue = Number(value);\n                // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n                // leave them as strings. For more info: https://goo.gl/y5vbSg\n                return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n            }\n            return value;\n        };\n        /**\n         * Gets a sorted copy of the data array based on the state of the MatSort. Called\n         * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n         * By default, the function retrieves the active sort and its direction and compares data\n         * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n         * of data ordering.\n         * @param data The array of data that should be sorted.\n         * @param sort The connected MatSort that holds the current sort state.\n         */\n        this.sortData = (data, sort) => {\n            const active = sort.active;\n            const direction = sort.direction;\n            if (!active || direction == '') {\n                return data;\n            }\n            return data.sort((a, b) => {\n                let valueA = this.sortingDataAccessor(a, active);\n                let valueB = this.sortingDataAccessor(b, active);\n                // If there are data in the column that can be converted to a number,\n                // it must be ensured that the rest of the data\n                // is of the same type so as not to order incorrectly.\n                const valueAType = typeof valueA;\n                const valueBType = typeof valueB;\n                if (valueAType !== valueBType) {\n                    if (valueAType === 'number') {\n                        valueA += '';\n                    }\n                    if (valueBType === 'number') {\n                        valueB += '';\n                    }\n                }\n                // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n                // one value exists while the other doesn't. In this case, existing value should come last.\n                // This avoids inconsistent results when comparing values to undefined/null.\n                // If neither value exists, return 0 (equal).\n                let comparatorResult = 0;\n                if (valueA != null && valueB != null) {\n                    // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n                    if (valueA > valueB) {\n                        comparatorResult = 1;\n                    }\n                    else if (valueA < valueB) {\n                        comparatorResult = -1;\n                    }\n                }\n                else if (valueA != null) {\n                    comparatorResult = 1;\n                }\n                else if (valueB != null) {\n                    comparatorResult = -1;\n                }\n                return comparatorResult * (direction == 'asc' ? 1 : -1);\n            });\n        };\n        /**\n         * Checks if a data object matches the data source's filter string. By default, each data object\n         * is converted to a string of its properties and returns true if the filter has\n         * at least one occurrence in that string. By default, the filter string has its whitespace\n         * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n         * filter matching.\n         * @param data Data object used to check against the filter.\n         * @param filter Filter string that has been set on the data source.\n         * @returns Whether the filter matches against the data\n         */\n        this.filterPredicate = (data, filter) => {\n            // Transform the data into a lowercase string of all property values.\n            const dataStr = Object.keys(data)\n                .reduce((currentTerm, key) => {\n                // Use an obscure Unicode character to delimit the words in the concatenated string.\n                // This avoids matches where the values of two columns combined will match the user's query\n                // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n                // that has a very low chance of being typed in by somebody in a text field. This one in\n                // particular is \"White up-pointing triangle with dot\" from\n                // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n                return currentTerm + data[key] + '◬';\n            }, '')\n                .toLowerCase();\n            // Transform the filter by converting it to lowercase and removing whitespace.\n            const transformedFilter = filter.trim().toLowerCase();\n            return dataStr.indexOf(transformedFilter) != -1;\n        };\n        this._data = new BehaviorSubject(initialData);\n        this._updateChangeSubscription();\n    }\n    /**\n     * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n     * changes occur, process the current state of the filter, sort, and pagination along with\n     * the provided base data and send it to the table for rendering.\n     */\n    _updateChangeSubscription() {\n        // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n        // The events should emit whenever the component emits a change or initializes, or if no\n        // component is provided, a stream with just a null event should be provided.\n        // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n        // pipeline can progress to the next step. Note that the value from these streams are not used,\n        // they purely act as a signal to progress in the pipeline.\n        const sortChange = this._sort\n            ? merge(this._sort.sortChange, this._sort.initialized)\n            : of(null);\n        const pageChange = this._paginator\n            ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized)\n            : of(null);\n        const dataStream = this._data;\n        // Watch for base data or filter changes to provide a filtered set of data.\n        const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n        // Watch for filtered data or sort changes to provide an ordered set of data.\n        const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n        // Watch for ordered data or page changes to provide a paged set of data.\n        const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n        // Watched for paged data changes and send the result to the table to render.\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n    }\n    /**\n     * Returns a filtered data array where each filter object contains the filter string within\n     * the result of the filterPredicate function. If no filter is set, returns the data array\n     * as provided.\n     */\n    _filterData(data) {\n        // If there is a filter string, filter out data that does not contain it.\n        // Each data object is converted to a string using the function defined by filterPredicate.\n        // May be overridden for customization.\n        this.filteredData =\n            this.filter == null || this.filter === ''\n                ? data\n                : data.filter(obj => this.filterPredicate(obj, this.filter));\n        if (this.paginator) {\n            this._updatePaginator(this.filteredData.length);\n        }\n        return this.filteredData;\n    }\n    /**\n     * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n     * data array as provided. Uses the default data accessor for data lookup, unless a\n     * sortDataAccessor function is defined.\n     */\n    _orderData(data) {\n        // If there is no active sort or direction, return the data without trying to sort.\n        if (!this.sort) {\n            return data;\n        }\n        return this.sortData(data.slice(), this.sort);\n    }\n    /**\n     * Returns a paged slice of the provided data array according to the provided paginator's page\n     * index and length. If there is no paginator provided, returns the data array as provided.\n     */\n    _pageData(data) {\n        if (!this.paginator) {\n            return data;\n        }\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        return data.slice(startIndex, startIndex + this.paginator.pageSize);\n    }\n    /**\n     * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n     * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n     * guard against making property changes within a round of change detection.\n     */\n    _updatePaginator(filteredDataLength) {\n        Promise.resolve().then(() => {\n            const paginator = this.paginator;\n            if (!paginator) {\n                return;\n            }\n            paginator.length = filteredDataLength;\n            // If the page index is set beyond the page, reduce it to the last page.\n            if (paginator.pageIndex > 0) {\n                const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n                const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n                if (newPageIndex !== paginator.pageIndex) {\n                    paginator.pageIndex = newPageIndex;\n                    // Since the paginator only emits after user-generated changes,\n                    // we need our own stream so we know to should re-render the data.\n                    this._internalPageChanges.next();\n                }\n            }\n        });\n    }\n    /**\n     * Used by the MatTable. Called when it connects to the data source.\n     * @docs-private\n     */\n    connect() {\n        if (!this._renderChangesSubscription) {\n            this._updateChangeSubscription();\n        }\n        return this._renderData;\n    }\n    /**\n     * Used by the MatTable. Called when it disconnects from the data source.\n     * @docs-private\n     */\n    disconnect() {\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = null;\n    }\n}\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends _MatTableDataSource {\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn, _MatTableDataSource };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACjH,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,QAAQ,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAC9W,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,UAAU,QAAQ,0BAA0B;AAC1I,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACzE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,GAAG,QAAQ,gBAAgB;;AAEpC;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAKoG5C,EAAE,CAAA8C,cAAA,WA8V9B,CAAC;IA9V2B9C,EAAE,CAAA+C,MAAA,EAgWjG,CAAC;IAhW8F/C,EAAE,CAAAgD,YAAA,CAgW5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAhWyFjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAmD,WAAA,eAAAF,MAAA,CAAAG,OA8V/B,CAAC;IA9V4BpD,EAAE,CAAAqD,SAAA,EAgWjG,CAAC;IAhW8FrD,EAAE,CAAAsD,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KAgWjG,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhW8F5C,EAAE,CAAA8C,cAAA,WAiWhC,CAAC;IAjW6B9C,EAAE,CAAA+C,MAAA,EAmWjG,CAAC;IAnW8F/C,EAAE,CAAAgD,YAAA,CAmW5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAa,OAAA,GAAAZ,GAAA,CAAAa,SAAA;IAAA,MAAAC,MAAA,GAnWyF3D,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAmD,WAAA,eAAAQ,MAAA,CAAAP,OAiWjC,CAAC;IAjW8BpD,EAAE,CAAAqD,SAAA,EAmWjG,CAAC;IAnW8FrD,EAAE,CAAAsD,kBAAA,MAAAK,MAAA,CAAAC,YAAA,CAAAH,OAAA,EAAAE,MAAA,CAAAE,IAAA,MAmWjG,CAAC;EAAA;AAAA;AApWN,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACI,IAAI,kBAD8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,QAAA,GADZtE,EAAE,CAAAuE,kBAAA,CAC0F,CAAC;QAAEC,OAAO,EAAE3C,uBAAuB;QAAE4C,QAAQ,EAAE3C;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACjS;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KAHoG1E,EAAE,CAAA2E,iBAAA,CAGXb,cAAc,EAAc,CAAC;IAC5GM,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE3C,uBAAuB;QAAE4C,QAAQ,EAAE3C;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMiD,QAAQ,SAASvE,QAAQ,CAAC;EAC5BwE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,cAAc,GAAG,sBAAsB;IAC5C;IACA,IAAI,CAACC,4BAA4B,GAAG,KAAK;EAC7C;EACAC,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB;IACA;IACA;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MACzB,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACnEH,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IAClD;EACJ;EACA;IAAS,IAAI,CAAC5B,IAAI;MAAA,IAAA6B,qBAAA;MAAA,gBAAAC,iBAAA5B,CAAA;QAAA,QAAA2B,qBAAA,KAAAA,qBAAA,GA5B8E5F,EAAE,CAAA8F,qBAAA,CA4BQf,QAAQ,IAAAd,CAAA,IAARc,QAAQ;MAAA;IAAA,GAAqD;EAAE;EACzK;IAAS,IAAI,CAACgB,IAAI,kBA7B8E/F,EAAE,CAAAgG,iBAAA;MAAA5B,IAAA,EA6BJW,QAAQ;MAAAV,SAAA;MAAA4B,SAAA,sBAAoF,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAxD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7B5F5C,EAAE,CAAAqG,WAAA,2BAAAxD,GAAA,CAAAyD,WAAA;QAAA;MAAA;MAAAC,QAAA;MAAAjC,QAAA,GAAFtE,EAAE,CAAAuE,kBAAA,CA6BiO,CAC3T;QAAEC,OAAO,EAAEhE,QAAQ;QAAEgG,WAAW,EAAEzB;MAAS,CAAC,EAC5C;QAAEP,OAAO,EAAE/D,SAAS;QAAE+F,WAAW,EAAEzB;MAAS,CAAC,EAC7C;QAAEP,OAAO,EAAE9D,0BAA0B;QAAE+D,QAAQ,EAAE9D;MAAyB,CAAC;MAC3E;MACA;MACA;QAAE6D,OAAO,EAAE3C,uBAAuB;QAAE4C,QAAQ,EAAE1C;MAA6B,CAAC;MAC5E;MACA;QAAEyC,OAAO,EAAE5D,2BAA2B;QAAE6F,QAAQ,EAAE;MAAK,CAAC,CAC3D,GAtC2FzG,EAAE,CAAA0G,0BAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAApE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAAiH,eAAA,CAAAxE,GAAA;UAAFzC,EAAE,CAAAkH,YAAA,EAuCzD,CAAC;UAvCsDlH,EAAE,CAAAkH,YAAA,KAwCnD,CAAC;UAxCgDlH,EAAE,CAAAmH,kBAAA,KAyCtD,CAAC,KAAD,CAAC,KAAD,CAAC,KAAD,CAAC;QAAA;MAAA;MAAAC,YAAA,GAIs2K7G,EAAE,CAAC8G,aAAa,EAAwD9G,EAAE,CAAC+G,eAAe,EAA8D/G,EAAE,CAACgH,eAAe,EAA8DhH,EAAE,CAACiH,eAAe;MAAAC,MAAA;MAAAC,aAAA;IAAA,EAAoI;EAAE;AACvxL;AACA;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KA/CoG1E,EAAE,CAAA2E,iBAAA,CA+CXI,QAAQ,EAAc,CAAC;IACtGX,IAAI,EAAElE,SAAS;IACf0E,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAE0B,QAAQ,EAAE,UAAU;MAAEQ,QAAQ,EAAG;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEY,IAAI,EAAE;QACa,OAAO,EAAE,qCAAqC;QAC9C,gCAAgC,EAAE,aAAa;QAC/C,iBAAiB,EAAE;MACvB,CAAC;MAAE7C,SAAS,EAAE,CACV;QAAEN,OAAO,EAAEhE,QAAQ;QAAEgG,WAAW,EAAEzB;MAAS,CAAC,EAC5C;QAAEP,OAAO,EAAE/D,SAAS;QAAE+F,WAAW,EAAEzB;MAAS,CAAC,EAC7C;QAAEP,OAAO,EAAE9D,0BAA0B;QAAE+D,QAAQ,EAAE9D;MAAyB,CAAC;MAC3E;MACA;MACA;QAAE6D,OAAO,EAAE3C,uBAAuB;QAAE4C,QAAQ,EAAE1C;MAA6B,CAAC;MAC5E;MACA;QAAEyC,OAAO,EAAE5D,2BAA2B;QAAE6F,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEiB,aAAa,EAAEvH,iBAAiB,CAACyH,IAAI;MAAEC,eAAe,EAAEzH,uBAAuB,CAAC0H,OAAO;MAAEL,MAAM,EAAE,CAAC,40KAA40K;IAAE,CAAC;EACh8K,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMM,UAAU,SAASlH,UAAU,CAAC;EAChC;IAAS,IAAI,CAACkD,IAAI;MAAA,IAAAiE,uBAAA;MAAA,gBAAAC,mBAAAhE,CAAA;QAAA,QAAA+D,uBAAA,KAAAA,uBAAA,GA7E8EhI,EAAE,CAAA8F,qBAAA,CA6EQiC,UAAU,IAAA9D,CAAA,IAAV8D,UAAU;MAAA;IAAA,GAAqD;EAAE;EAC3K;IAAS,IAAI,CAAC7D,IAAI,kBA9E8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EA8EJ2D,UAAU;MAAA1D,SAAA;MAAAC,QAAA,GA9ERtE,EAAE,CAAAuE,kBAAA,CA8E6C,CAAC;QAAEC,OAAO,EAAE3D,UAAU;QAAE2F,WAAW,EAAEuB;MAAW,CAAC,CAAC,GA9EjG/H,EAAE,CAAA0G,0BAAA;IAAA,EA8EuI;EAAE;AAC/O;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAhFoG1E,EAAE,CAAA2E,iBAAA,CAgFXoD,UAAU,EAAc,CAAC;IACxG3D,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE3D,UAAU;QAAE2F,WAAW,EAAEuB;MAAW,CAAC;IAChE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,SAASpH,gBAAgB,CAAC;EAC5C;IAAS,IAAI,CAACiD,IAAI;MAAA,IAAAoE,6BAAA;MAAA,gBAAAC,yBAAAnE,CAAA;QAAA,QAAAkE,6BAAA,KAAAA,6BAAA,GA5F8EnI,EAAE,CAAA8F,qBAAA,CA4FQoC,gBAAgB,IAAAjE,CAAA,IAAhBiE,gBAAgB;MAAA;IAAA,GAAqD;EAAE;EACjL;IAAS,IAAI,CAAChE,IAAI,kBA7F8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EA6FJ8D,gBAAgB;MAAA7D,SAAA;MAAAC,QAAA,GA7FdtE,EAAE,CAAAuE,kBAAA,CA6FyD,CAAC;QAAEC,OAAO,EAAE1D,gBAAgB;QAAE0F,WAAW,EAAE0B;MAAiB,CAAC,CAAC,GA7FzHlI,EAAE,CAAA0G,0BAAA;IAAA,EA6F+J;EAAE;AACvQ;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KA/FoG1E,EAAE,CAAA2E,iBAAA,CA+FXuD,gBAAgB,EAAc,CAAC;IAC9G9D,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE1D,gBAAgB;QAAE0F,WAAW,EAAE0B;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,SAAStH,gBAAgB,CAAC;EAC5C;IAAS,IAAI,CAACgD,IAAI;MAAA,IAAAuE,6BAAA;MAAA,gBAAAC,yBAAAtE,CAAA;QAAA,QAAAqE,6BAAA,KAAAA,6BAAA,GA3G8EtI,EAAE,CAAA8F,qBAAA,CA2GQuC,gBAAgB,IAAApE,CAAA,IAAhBoE,gBAAgB;MAAA;IAAA,GAAqD;EAAE;EACjL;IAAS,IAAI,CAACnE,IAAI,kBA5G8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EA4GJiE,gBAAgB;MAAAhE,SAAA;MAAAC,QAAA,GA5GdtE,EAAE,CAAAuE,kBAAA,CA4GyD,CAAC;QAAEC,OAAO,EAAEzD,gBAAgB;QAAEyF,WAAW,EAAE6B;MAAiB,CAAC,CAAC,GA5GzHrI,EAAE,CAAA0G,0BAAA;IAAA,EA4G+J;EAAE;AACvQ;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KA9GoG1E,EAAE,CAAA2E,iBAAA,CA8GX0D,gBAAgB,EAAc,CAAC;IAC9GjE,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEzD,gBAAgB;QAAEyF,WAAW,EAAE6B;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMG,YAAY,SAASxH,YAAY,CAAC;EACpC;EACA,IAAI6C,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC4E,KAAK;EACrB;EACA,IAAI5E,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAAC6E,aAAa,CAAC7E,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI8E,yBAAyBA,CAAA,EAAG;IACxB,KAAK,CAACA,yBAAyB,CAAC,CAAC;IACjC,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAE,cAAa,IAAI,CAACC,oBAAqB,EAAC,CAAC;EAC5E;EACA;IAAS,IAAI,CAAC/E,IAAI;MAAA,IAAAgF,yBAAA;MAAA,gBAAAC,qBAAA/E,CAAA;QAAA,QAAA8E,yBAAA,KAAAA,yBAAA,GA3I8E/I,EAAE,CAAA8F,qBAAA,CA2IQ0C,YAAY,IAAAvE,CAAA,IAAZuE,YAAY;MAAA;IAAA,GAAqD;EAAE;EAC7K;IAAS,IAAI,CAACtE,IAAI,kBA5I8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EA4IJoE,YAAY;MAAAnE,SAAA;MAAA4E,MAAA;QAAAC,MAAA;QAAArF,IAAA;MAAA;MAAAS,QAAA,GA5IVtE,EAAE,CAAAuE,kBAAA,CA4I+G,CACzM;QAAEC,OAAO,EAAExD,YAAY;QAAEwF,WAAW,EAAEgC;MAAa,CAAC,EACpD;QAAEhE,OAAO,EAAE,4BAA4B;QAAEgC,WAAW,EAAEgC;MAAa,CAAC,CACvE,GA/I2FxI,EAAE,CAAA0G,0BAAA;IAAA,EA+IrD;EAAE;AACnD;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAjJoG1E,EAAE,CAAA2E,iBAAA,CAiJX6D,YAAY,EAAc,CAAC;IAC1GpE,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BoE,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClBnE,SAAS,EAAE,CACP;QAAEN,OAAO,EAAExD,YAAY;QAAEwF,WAAW,EAAEgC;MAAa,CAAC,EACpD;QAAEhE,OAAO,EAAE,4BAA4B;QAAEgC,WAAW,EAAEgC;MAAa,CAAC;IAE5E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE3E,IAAI,EAAE,CAAC;MACrBO,IAAI,EAAE/D,KAAK;MACXuE,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMuE,aAAa,SAASlI,aAAa,CAAC;EACtC;IAAS,IAAI,CAAC8C,IAAI;MAAA,IAAAqF,0BAAA;MAAA,gBAAAC,sBAAApF,CAAA;QAAA,QAAAmF,0BAAA,KAAAA,0BAAA,GAjK8EpJ,EAAE,CAAA8F,qBAAA,CAiKQqD,aAAa,IAAAlF,CAAA,IAAbkF,aAAa;MAAA;IAAA,GAAqD;EAAE;EAC9K;IAAS,IAAI,CAACjF,IAAI,kBAlK8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAkKJ+E,aAAa;MAAA9E,SAAA;MAAA4B,SAAA,WAAkF,cAAc;MAAA3B,QAAA,GAlK3GtE,EAAE,CAAA0G,0BAAA;IAAA,EAkKwN;EAAE;AAChU;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KApKoG1E,EAAE,CAAA2E,iBAAA,CAoKXwE,aAAa,EAAc,CAAC;IAC3G/E,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChD8C,IAAI,EAAE;QACF,OAAO,EAAE,iDAAiD;QAC1D,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM2B,aAAa,SAASpI,aAAa,CAAC;EACtC;IAAS,IAAI,CAAC6C,IAAI;MAAA,IAAAwF,0BAAA;MAAA,gBAAAC,sBAAAvF,CAAA;QAAA,QAAAsF,0BAAA,KAAAA,0BAAA,GAhL8EvJ,EAAE,CAAA8F,qBAAA,CAgLQwD,aAAa,IAAArF,CAAA,IAAbqF,aAAa;MAAA;IAAA,GAAqD;EAAE;EAC9K;IAAS,IAAI,CAACpF,IAAI,kBAjL8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAiLJkF,aAAa;MAAAjF,SAAA;MAAA4B,SAAA;MAAA3B,QAAA,GAjLXtE,EAAE,CAAA0G,0BAAA;IAAA,EAiLyK;EAAE;AACjR;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAnLoG1E,EAAE,CAAA2E,iBAAA,CAmLX2E,aAAa,EAAc,CAAC;IAC3GlF,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChD8C,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM8B,OAAO,SAAStI,OAAO,CAAC;EAC1B;IAAS,IAAI,CAAC4C,IAAI;MAAA,IAAA2F,oBAAA;MAAA,gBAAAC,gBAAA1F,CAAA;QAAA,QAAAyF,oBAAA,KAAAA,oBAAA,GA9L8E1J,EAAE,CAAA8F,qBAAA,CA8LQ2D,OAAO,IAAAxF,CAAA,IAAPwF,OAAO;MAAA;IAAA,GAAqD;EAAE;EACxK;IAAS,IAAI,CAACvF,IAAI,kBA/L8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EA+LJqF,OAAO;MAAApF,SAAA;MAAA4B,SAAA;MAAA3B,QAAA,GA/LLtE,EAAE,CAAA0G,0BAAA;IAAA,EA+L8I;EAAE;AACtP;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAjMoG1E,EAAE,CAAA2E,iBAAA,CAiMX8E,OAAO,EAAc,CAAC;IACrGrF,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClC8C,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMiC,YAAY,GAAI,6CAA4C;AAClE;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASzI,eAAe,CAAC;EAC1C;IAAS,IAAI,CAAC2C,IAAI;MAAA,IAAA+F,4BAAA;MAAA,gBAAAC,wBAAA9F,CAAA;QAAA,QAAA6F,4BAAA,KAAAA,4BAAA,GAlN8E9J,EAAE,CAAA8F,qBAAA,CAkNQ+D,eAAe,IAAA5F,CAAA,IAAf4F,eAAe;MAAA;IAAA,GAAqD;EAAE;EAChL;IAAS,IAAI,CAAC3F,IAAI,kBAnN8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAmNJyF,eAAe;MAAAxF,SAAA;MAAA4E,MAAA;QAAAe,OAAA;QAAAd,MAAA;MAAA;MAAA5E,QAAA,GAnNbtE,EAAE,CAAAuE,kBAAA,CAmNyJ,CAAC;QAAEC,OAAO,EAAEpD,eAAe;QAAEoF,WAAW,EAAEqD;MAAgB,CAAC,CAAC,GAnNvN7J,EAAE,CAAA0G,0BAAA;IAAA,EAmN6P;EAAE;AACrW;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KArNoG1E,EAAE,CAAA2E,iBAAA,CAqNXkF,eAAe,EAAc,CAAC;IAC7GzF,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEpD,eAAe;QAAEoF,WAAW,EAAEqD;MAAgB,CAAC,CAAC;MACvEZ,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMgB,eAAe,SAAS5I,eAAe,CAAC;EAC1C;IAAS,IAAI,CAAC0C,IAAI;MAAA,IAAAmG,4BAAA;MAAA,gBAAAC,wBAAAlG,CAAA;QAAA,QAAAiG,4BAAA,KAAAA,4BAAA,GAlO8ElK,EAAE,CAAA8F,qBAAA,CAkOQmE,eAAe,IAAAhG,CAAA,IAAfgG,eAAe;MAAA;IAAA,GAAqD;EAAE;EAChL;IAAS,IAAI,CAAC/F,IAAI,kBAnO8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAmOJ6F,eAAe;MAAA5F,SAAA;MAAA4E,MAAA;QAAAe,OAAA;QAAAd,MAAA;MAAA;MAAA5E,QAAA,GAnObtE,EAAE,CAAAuE,kBAAA,CAmOyJ,CAAC;QAAEC,OAAO,EAAEnD,eAAe;QAAEmF,WAAW,EAAEyD;MAAgB,CAAC,CAAC,GAnOvNjK,EAAE,CAAA0G,0BAAA;IAAA,EAmO6P;EAAE;AACrW;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KArOoG1E,EAAE,CAAA2E,iBAAA,CAqOXsF,eAAe,EAAc,CAAC;IAC7G7F,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEnD,eAAe;QAAEmF,WAAW,EAAEyD;MAAgB,CAAC,CAAC;MACvEhB,MAAM,EAAE,CAAC,0BAA0B,EAAE,+BAA+B;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMmB,SAAS,SAAS9I,SAAS,CAAC;EAC9B;IAAS,IAAI,CAACyC,IAAI;MAAA,IAAAsG,sBAAA;MAAA,gBAAAC,kBAAArG,CAAA;QAAA,QAAAoG,sBAAA,KAAAA,sBAAA,GAnP8ErK,EAAE,CAAA8F,qBAAA,CAmPQsE,SAAS,IAAAnG,CAAA,IAATmG,SAAS;MAAA;IAAA,GAAqD;EAAE;EAC1K;IAAS,IAAI,CAAClG,IAAI,kBApP8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAoPJgG,SAAS;MAAA/F,SAAA;MAAA4E,MAAA;QAAAe,OAAA;QAAAO,IAAA;MAAA;MAAAjG,QAAA,GApPPtE,EAAE,CAAAuE,kBAAA,CAoPkI,CAAC;QAAEC,OAAO,EAAElD,SAAS;QAAEkF,WAAW,EAAE4D;MAAU,CAAC,CAAC,GApPpLpK,EAAE,CAAA0G,0BAAA;IAAA,EAoP0N;EAAE;AAClU;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAtPoG1E,EAAE,CAAA2E,iBAAA,CAsPXyF,SAAS,EAAc,CAAC;IACvGhG,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAElD,SAAS;QAAEkF,WAAW,EAAE4D;MAAU,CAAC,CAAC;MAC3DnB,MAAM,EAAE,CAAC,2BAA2B,EAAE,qBAAqB;IAC/D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMuB,YAAY,SAASjJ,YAAY,CAAC;EACpC;IAAS,IAAI,CAACwC,IAAI;MAAA,IAAA0G,yBAAA;MAAA,gBAAAC,qBAAAzG,CAAA;QAAA,QAAAwG,yBAAA,KAAAA,yBAAA,GAhQ8EzK,EAAE,CAAA8F,qBAAA,CAgQQ0E,YAAY,IAAAvG,CAAA,IAAZuG,YAAY;MAAA;IAAA,GAAqD;EAAE;EAC7K;IAAS,IAAI,CAACzE,IAAI,kBAjQ8E/F,EAAE,CAAAgG,iBAAA;MAAA5B,IAAA,EAiQJoG,YAAY;MAAAnG,SAAA;MAAA4B,SAAA,WAAgF,KAAK;MAAAM,QAAA;MAAAjC,QAAA,GAjQ/FtE,EAAE,CAAAuE,kBAAA,CAiQ+K,CAAC;QAAEC,OAAO,EAAEjD,YAAY;QAAEiF,WAAW,EAAEgE;MAAa,CAAC,CAAC,GAjQvOxK,EAAE,CAAA0G,0BAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4D,sBAAA/H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAAmH,kBAAA,KAiQ6V,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6D7G,EAAE,CAACqK,aAAa;MAAAlD,aAAA;IAAA,EAAkI;EAAE;AACrpB;AACA;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KAnQoG1E,EAAE,CAAA2E,iBAAA,CAmQX6F,YAAY,EAAc,CAAC;IAC1GpG,IAAI,EAAElE,SAAS;IACf0E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CkC,QAAQ,EAAE6C,YAAY;MACtBjC,IAAI,EAAE;QACF,OAAO,EAAE,+CAA+C;QACxD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEzH,uBAAuB,CAAC0H,OAAO;MAChDJ,aAAa,EAAEvH,iBAAiB,CAACyH,IAAI;MACrCrB,QAAQ,EAAE,cAAc;MACxBzB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEjD,YAAY;QAAEiF,WAAW,EAAEgE;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,YAAY,SAASrJ,YAAY,CAAC;EACpC;IAAS,IAAI,CAACuC,IAAI;MAAA,IAAA+G,yBAAA;MAAA,gBAAAC,qBAAA9G,CAAA;QAAA,QAAA6G,yBAAA,KAAAA,yBAAA,GAtR8E9K,EAAE,CAAA8F,qBAAA,CAsRQ+E,YAAY,IAAA5G,CAAA,IAAZ4G,YAAY;MAAA;IAAA,GAAqD;EAAE;EAC7K;IAAS,IAAI,CAAC9E,IAAI,kBAvR8E/F,EAAE,CAAAgG,iBAAA;MAAA5B,IAAA,EAuRJyG,YAAY;MAAAxG,SAAA;MAAA4B,SAAA,WAAgF,KAAK;MAAAM,QAAA;MAAAjC,QAAA,GAvR/FtE,EAAE,CAAAuE,kBAAA,CAuRwK,CAAC;QAAEC,OAAO,EAAEhD,YAAY;QAAEgF,WAAW,EAAEqE;MAAa,CAAC,CAAC,GAvRhO7K,EAAE,CAAA0G,0BAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiE,sBAAApI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAAmH,kBAAA,KAuRsV,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6D7G,EAAE,CAACqK,aAAa;MAAAlD,aAAA;IAAA,EAAkI;EAAE;AAC9oB;AACA;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KAzRoG1E,EAAE,CAAA2E,iBAAA,CAyRXkG,YAAY,EAAc,CAAC;IAC1GzG,IAAI,EAAElE,SAAS;IACf0E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CkC,QAAQ,EAAE6C,YAAY;MACtBjC,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEzH,uBAAuB,CAAC0H,OAAO;MAChDJ,aAAa,EAAEvH,iBAAiB,CAACyH,IAAI;MACrCrB,QAAQ,EAAE,cAAc;MACxBzB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEhD,YAAY;QAAEgF,WAAW,EAAEqE;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,MAAM,SAASxJ,MAAM,CAAC;EACxB;IAAS,IAAI,CAACsC,IAAI;MAAA,IAAAmH,mBAAA;MAAA,gBAAAC,eAAAlH,CAAA;QAAA,QAAAiH,mBAAA,KAAAA,mBAAA,GA5S8ElL,EAAE,CAAA8F,qBAAA,CA4SQmF,MAAM,IAAAhH,CAAA,IAANgH,MAAM;MAAA;IAAA,GAAqD;EAAE;EACvK;IAAS,IAAI,CAAClF,IAAI,kBA7S8E/F,EAAE,CAAAgG,iBAAA;MAAA5B,IAAA,EA6SJ6G,MAAM;MAAA5G,SAAA;MAAA4B,SAAA,WAAkE,KAAK;MAAAM,QAAA;MAAAjC,QAAA,GA7S3EtE,EAAE,CAAAuE,kBAAA,CA6S6I,CAAC;QAAEC,OAAO,EAAE/C,MAAM;QAAE+E,WAAW,EAAEyE;MAAO,CAAC,CAAC,GA7SzLjL,EAAE,CAAA0G,0BAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqE,gBAAAxI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAAmH,kBAAA,KA6SyS,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6D7G,EAAE,CAACqK,aAAa;MAAAlD,aAAA;IAAA,EAAkI;EAAE;AACjmB;AACA;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KA/SoG1E,EAAE,CAAA2E,iBAAA,CA+SXsG,MAAM,EAAc,CAAC;IACpG7G,IAAI,EAAElE,SAAS;IACf0E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCkC,QAAQ,EAAE6C,YAAY;MACtBjC,IAAI,EAAE;QACF,OAAO,EAAE,iCAAiC;QAC1C,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEzH,uBAAuB,CAAC0H,OAAO;MAChDJ,aAAa,EAAEvH,iBAAiB,CAACyH,IAAI;MACrCrB,QAAQ,EAAE,QAAQ;MAClBzB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE/C,MAAM;QAAE+E,WAAW,EAAEyE;MAAO,CAAC;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,YAAY,SAAS3J,YAAY,CAAC;EACpCsD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACqG,iBAAiB,GAAG,qBAAqB;EAClD;EACA;IAAS,IAAI,CAACvH,IAAI;MAAA,IAAAwH,yBAAA;MAAA,gBAAAC,qBAAAvH,CAAA;QAAA,QAAAsH,yBAAA,KAAAA,yBAAA,GAtU8EvL,EAAE,CAAA8F,qBAAA,CAsUQuF,YAAY,IAAApH,CAAA,IAAZoH,YAAY;MAAA;IAAA,GAAqD;EAAE;EAC7K;IAAS,IAAI,CAACnH,IAAI,kBAvU8ElE,EAAE,CAAAmE,iBAAA;MAAAC,IAAA,EAuUJiH,YAAY;MAAAhH,SAAA;MAAAC,QAAA,GAvUVtE,EAAE,CAAAuE,kBAAA,CAuU4D,CAAC;QAAEC,OAAO,EAAE9C,YAAY;QAAE8E,WAAW,EAAE6E;MAAa,CAAC,CAAC,GAvUpHrL,EAAE,CAAA0G,0BAAA;IAAA,EAuU0J;EAAE;AAClQ;AACA;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KAzUoG1E,EAAE,CAAA2E,iBAAA,CAyUX0G,YAAY,EAAc,CAAC;IAC1GjH,IAAI,EAAEnE,SAAS;IACf2E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE9C,YAAY;QAAE8E,WAAW,EAAE6E;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,SAAS9J,aAAa,CAAC;EACtC;IAAS,IAAI,CAACoC,IAAI;MAAA,IAAA2H,0BAAA;MAAA,gBAAAC,sBAAA1H,CAAA;QAAA,QAAAyH,0BAAA,KAAAA,0BAAA,GA3V8E1L,EAAE,CAAA8F,qBAAA,CA2VQ2F,aAAa,IAAAxH,CAAA,IAAbwH,aAAa;MAAA;IAAA,GAAqD;EAAE;EAC9K;IAAS,IAAI,CAAC1F,IAAI,kBA5V8E/F,EAAE,CAAAgG,iBAAA;MAAA5B,IAAA,EA4VJqH,aAAa;MAAApH,SAAA;MAAAC,QAAA,GA5VXtE,EAAE,CAAA0G,0BAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6E,uBAAAhJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5C,EAAE,CAAA6L,uBAAA,KA6VxE,CAAC;UA7VqE7L,EAAE,CAAA8L,UAAA,IAAAnJ,2BAAA,eAgW5F,CAAC;UAhWyF3C,EAAE,CAAA8L,UAAA,IAAAtI,2BAAA,eAmW5F,CAAC;UAnWyFxD,EAAE,CAAA+L,qBAAA,CAoWpF,CAAC;QAAA;MAAA;MAAA3E,YAAA,GAC4Cc,gBAAgB,EAA+DM,YAAY,EAA+FT,UAAU,EAAyDoB,aAAa,EAAiFM,OAAO;MAAA/B,aAAA;IAAA,EAAyI;EAAE;AAC5iB;AACA;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KAvWoG1E,EAAE,CAAA2E,iBAAA,CAuWX8G,aAAa,EAAc,CAAC;IAC3GrH,IAAI,EAAElE,SAAS;IACf0E,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BkC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBW,aAAa,EAAEvH,iBAAiB,CAACyH,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAC,eAAe,EAAEzH,uBAAuB,CAAC0H;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMkE,qBAAqB,GAAG;AAC1B;AACAjH,QAAQ,EACRjB,cAAc;AACd;AACAoE,gBAAgB,EAChB2B,eAAe,EACfrB,YAAY,EACZT,UAAU,EACVqC,SAAS,EACT/B,gBAAgB,EAChB4B,eAAe;AACf;AACAd,aAAa,EACbM,OAAO,EACPH,aAAa;AACb;AACAkB,YAAY,EACZS,MAAM,EACNJ,YAAY,EACZQ,YAAY,EACZI,aAAa,CAChB;AACD,MAAMQ,cAAc,CAAC;EACjB;IAAS,IAAI,CAAClI,IAAI,YAAAmI,uBAAAjI,CAAA;MAAA,YAAAA,CAAA,IAAwFgI,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAzZ8EnM,EAAE,CAAAoM,gBAAA;MAAAhI,IAAA,EAyZS6H;IAAc,EA0ChG;EAAE;EAC3B;IAAS,IAAI,CAACI,IAAI,kBApc8ErM,EAAE,CAAAsM,gBAAA;MAAAC,OAAA,GAocmCtK,eAAe,EAAEL,cAAc,EAAEK,eAAe;IAAA,EAAI;EAAE;AAC/L;AACA;EAAA,QAAAyC,SAAA,oBAAAA,SAAA,KAtcoG1E,EAAE,CAAA2E,iBAAA,CAscXsH,cAAc,EAAc,CAAC;IAC5G7H,IAAI,EAAE9D,QAAQ;IACdsE,IAAI,EAAE,CAAC;MACC2H,OAAO,EAAE,CAACtK,eAAe,EAAEL,cAAc,CAAC;MAC1C4K,OAAO,EAAE,CAACvK,eAAe,EAAE+J,qBAAqB,CAAC;MACjDS,YAAY,EAAET;IAClB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMU,gBAAgB,GAAG,gBAAgB;AACzC;AACA,MAAMC,mBAAmB,SAAS3K,UAAU,CAAC;EACzC;EACA,IAAI4K,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,KAAK;EAC3B;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACXA,IAAI,GAAGG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACtC,IAAI,CAACC,KAAK,CAACI,IAAI,CAACL,IAAI,CAAC;IACrB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIQ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAACP,KAAK;EAC7B;EACA,IAAIM,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACC,OAAO,CAACJ,IAAI,CAACG,MAAM,CAAC;IACzB;IACA;IACA,IAAI,CAAC,IAAI,CAACF,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,IAAI,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIU,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAACD,yBAAyB,CAAC,CAAC;EACpC;EACAxI,WAAWA,CAAC2I,WAAW,GAAG,EAAE,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP;IACA,IAAI,CAACC,WAAW,GAAG,IAAI1L,eAAe,CAAC,EAAE,CAAC;IAC1C;IACA,IAAI,CAACmL,OAAO,GAAG,IAAInL,eAAe,CAAC,EAAE,CAAC;IACtC;IACA,IAAI,CAAC2L,oBAAoB,GAAG,IAAI1L,OAAO,CAAC,CAAC;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC+K,0BAA0B,GAAG,IAAI;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACY,mBAAmB,GAAG,CAAClB,IAAI,EAAEmB,YAAY,KAAK;MAC/C,MAAMjB,KAAK,GAAGF,IAAI,CAACmB,YAAY,CAAC;MAChC,IAAIxL,cAAc,CAACuK,KAAK,CAAC,EAAE;QACvB,MAAMkB,WAAW,GAAGC,MAAM,CAACnB,KAAK,CAAC;QACjC;QACA;QACA,OAAOkB,WAAW,GAAGtB,gBAAgB,GAAGsB,WAAW,GAAGlB,KAAK;MAC/D;MACA,OAAOA,KAAK;IAChB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoB,QAAQ,GAAG,CAACtB,IAAI,EAAEU,IAAI,KAAK;MAC5B,MAAMa,MAAM,GAAGb,IAAI,CAACa,MAAM;MAC1B,MAAMC,SAAS,GAAGd,IAAI,CAACc,SAAS;MAChC,IAAI,CAACD,MAAM,IAAIC,SAAS,IAAI,EAAE,EAAE;QAC5B,OAAOxB,IAAI;MACf;MACA,OAAOA,IAAI,CAACU,IAAI,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAK;QACvB,IAAIC,MAAM,GAAG,IAAI,CAACT,mBAAmB,CAACO,CAAC,EAAEF,MAAM,CAAC;QAChD,IAAIK,MAAM,GAAG,IAAI,CAACV,mBAAmB,CAACQ,CAAC,EAAEH,MAAM,CAAC;QAChD;QACA;QACA;QACA,MAAMM,UAAU,GAAG,OAAOF,MAAM;QAChC,MAAMG,UAAU,GAAG,OAAOF,MAAM;QAChC,IAAIC,UAAU,KAAKC,UAAU,EAAE;UAC3B,IAAID,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;UACA,IAAIG,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;QACJ;QACA;QACA;QACA;QACA;QACA,IAAIG,gBAAgB,GAAG,CAAC;QACxB,IAAIJ,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClC;UACA,IAAID,MAAM,GAAGC,MAAM,EAAE;YACjBG,gBAAgB,GAAG,CAAC;UACxB,CAAC,MACI,IAAIJ,MAAM,GAAGC,MAAM,EAAE;YACtBG,gBAAgB,GAAG,CAAC,CAAC;UACzB;QACJ,CAAC,MACI,IAAIJ,MAAM,IAAI,IAAI,EAAE;UACrBI,gBAAgB,GAAG,CAAC;QACxB,CAAC,MACI,IAAIH,MAAM,IAAI,IAAI,EAAE;UACrBG,gBAAgB,GAAG,CAAC,CAAC;QACzB;QACA,OAAOA,gBAAgB,IAAIP,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACQ,eAAe,GAAG,CAAChC,IAAI,EAAEQ,MAAM,KAAK;MACrC;MACA,MAAMyB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACnC,IAAI,CAAC,CAC5BoC,MAAM,CAAC,CAACC,WAAW,EAAEC,GAAG,KAAK;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA,OAAOD,WAAW,GAAGrC,IAAI,CAACsC,GAAG,CAAC,GAAG,GAAG;MACxC,CAAC,EAAE,EAAE,CAAC,CACDC,WAAW,CAAC,CAAC;MAClB;MACA,MAAMC,iBAAiB,GAAGhC,MAAM,CAACiC,IAAI,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC;MACrD,OAAON,OAAO,CAACS,OAAO,CAACF,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IACD,IAAI,CAACvC,KAAK,GAAG,IAAI3K,eAAe,CAACyL,WAAW,CAAC;IAC7C,IAAI,CAACH,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIA,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM+B,UAAU,GAAG,IAAI,CAAChC,KAAK,GACvBnL,KAAK,CAAC,IAAI,CAACmL,KAAK,CAACgC,UAAU,EAAE,IAAI,CAAChC,KAAK,CAACiC,WAAW,CAAC,GACpDnN,EAAE,CAAC,IAAI,CAAC;IACd,MAAMoN,UAAU,GAAG,IAAI,CAAC/B,UAAU,GAC5BtL,KAAK,CAAC,IAAI,CAACsL,UAAU,CAACgC,IAAI,EAAE,IAAI,CAAC7B,oBAAoB,EAAE,IAAI,CAACH,UAAU,CAAC8B,WAAW,CAAC,GACnFnN,EAAE,CAAC,IAAI,CAAC;IACd,MAAMsN,UAAU,GAAG,IAAI,CAAC9C,KAAK;IAC7B;IACA,MAAM+C,YAAY,GAAGtN,aAAa,CAAC,CAACqN,UAAU,EAAE,IAAI,CAACtC,OAAO,CAAC,CAAC,CAACwC,IAAI,CAACrN,GAAG,CAAC,CAAC,CAACoK,IAAI,CAAC,KAAK,IAAI,CAACO,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5G;IACA,MAAMkD,WAAW,GAAGxN,aAAa,CAAC,CAACsN,YAAY,EAAEL,UAAU,CAAC,CAAC,CAACM,IAAI,CAACrN,GAAG,CAAC,CAAC,CAACoK,IAAI,CAAC,KAAK,IAAI,CAACmD,UAAU,CAACnD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,MAAMoD,aAAa,GAAG1N,aAAa,CAAC,CAACwN,WAAW,EAAEL,UAAU,CAAC,CAAC,CAACI,IAAI,CAACrN,GAAG,CAAC,CAAC,CAACoK,IAAI,CAAC,KAAK,IAAI,CAACqD,SAAS,CAACrD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,IAAI,CAACM,0BAA0B,EAAEgD,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAChD,0BAA0B,GAAG8C,aAAa,CAACG,SAAS,CAACvD,IAAI,IAAI,IAAI,CAACgB,WAAW,CAACX,IAAI,CAACL,IAAI,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACP,IAAI,EAAE;IACd;IACA;IACA;IACA,IAAI,CAACgD,YAAY,GACb,IAAI,CAACxC,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,EAAE,GACnCR,IAAI,GACJA,IAAI,CAACQ,MAAM,CAACgD,GAAG,IAAI,IAAI,CAACxB,eAAe,CAACwB,GAAG,EAAE,IAAI,CAAChD,MAAM,CAAC,CAAC;IACpE,IAAI,IAAI,CAACK,SAAS,EAAE;MAChB,IAAI,CAAC4C,gBAAgB,CAAC,IAAI,CAACT,YAAY,CAACU,MAAM,CAAC;IACnD;IACA,OAAO,IAAI,CAACV,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIG,UAAUA,CAACnD,IAAI,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE;MACZ,OAAOV,IAAI;IACf;IACA,OAAO,IAAI,CAACsB,QAAQ,CAACtB,IAAI,CAAC2D,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjD,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACI2C,SAASA,CAACrD,IAAI,EAAE;IACZ,IAAI,CAAC,IAAI,CAACa,SAAS,EAAE;MACjB,OAAOb,IAAI;IACf;IACA,MAAM4D,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACgD,SAAS,GAAG,IAAI,CAAChD,SAAS,CAACiD,QAAQ;IACrE,OAAO9D,IAAI,CAAC2D,KAAK,CAACC,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACiD,QAAQ,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIL,gBAAgBA,CAACM,kBAAkB,EAAE;IACjCC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,MAAMrD,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAI,CAACA,SAAS,EAAE;QACZ;MACJ;MACAA,SAAS,CAAC6C,MAAM,GAAGK,kBAAkB;MACrC;MACA,IAAIlD,SAAS,CAACgD,SAAS,GAAG,CAAC,EAAE;QACzB,MAAMM,aAAa,GAAGC,IAAI,CAACC,IAAI,CAACxD,SAAS,CAAC6C,MAAM,GAAG7C,SAAS,CAACiD,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;QAC/E,MAAMQ,YAAY,GAAGF,IAAI,CAACG,GAAG,CAAC1D,SAAS,CAACgD,SAAS,EAAEM,aAAa,CAAC;QACjE,IAAIG,YAAY,KAAKzD,SAAS,CAACgD,SAAS,EAAE;UACtChD,SAAS,CAACgD,SAAS,GAAGS,YAAY;UAClC;UACA;UACA,IAAI,CAACrD,oBAAoB,CAACZ,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACImE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAClE,0BAA0B,EAAE;MAClC,IAAI,CAACM,yBAAyB,CAAC,CAAC;IACpC;IACA,OAAO,IAAI,CAACI,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIyD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnE,0BAA0B,EAAEgD,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAChD,0BAA0B,GAAG,IAAI;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,kBAAkB,SAAS3E,mBAAmB,CAAC;;AAGrD;AACA;AACA;;AAEA,SAASlD,OAAO,EAAE1B,UAAU,EAAES,YAAY,EAAEc,aAAa,EAAEjB,gBAAgB,EAAEwC,YAAY,EAAEZ,eAAe,EAAEd,aAAa,EAAEjB,gBAAgB,EAAEsC,YAAY,EAAEX,eAAe,EAAEwB,YAAY,EAAEvH,cAAc,EAAEmH,MAAM,EAAEb,SAAS,EAAErF,QAAQ,EAAEuM,kBAAkB,EAAErF,cAAc,EAAER,aAAa,EAAEkB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}