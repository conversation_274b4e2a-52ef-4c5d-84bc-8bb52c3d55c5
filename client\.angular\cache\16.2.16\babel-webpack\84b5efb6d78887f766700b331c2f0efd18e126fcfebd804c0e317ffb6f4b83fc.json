{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  /**\n   * Generic GET request\n   * @param endpoint API endpoint\n   * @param params Query parameters\n   * @param requiresAuth Whether the request requires authentication\n   * @returns Observable of response\n   */\n  get(endpoint, params, requiresAuth = true) {\n    const url = `${this.apiUrl}/${endpoint}`;\n    const options = this.buildRequestOptions(params, requiresAuth);\n    return this.http.get(url, {\n      headers: options.headers,\n      params: options.params\n    });\n  }\n  /**\n   * Generic POST request\n   * @param endpoint API endpoint\n   * @param body Request body\n   * @param requiresAuth Whether the request requires authentication\n   * @returns Observable of response\n   */\n  post(endpoint, body, requiresAuth = true) {\n    const url = `${this.apiUrl}/${endpoint}`;\n    const options = this.buildRequestOptions(null, requiresAuth);\n    return this.http.post(url, body, {\n      headers: options.headers,\n      params: options.params\n    });\n  }\n  /**\n   * Generic PUT request\n   * @param endpoint API endpoint\n   * @param body Request body\n   * @param requiresAuth Whether the request requires authentication\n   * @returns Observable of response\n   */\n  put(endpoint, body, requiresAuth = true) {\n    const url = `${this.apiUrl}/${endpoint}`;\n    const options = this.buildRequestOptions(null, requiresAuth);\n    return this.http.put(url, body, {\n      headers: options.headers,\n      params: options.params\n    });\n  }\n  /**\n   * Generic DELETE request\n   * @param endpoint API endpoint\n   * @param requiresAuth Whether the request requires authentication\n   * @returns Observable of response\n   */\n  delete(endpoint, requiresAuth = true) {\n    const url = `${this.apiUrl}/${endpoint}`;\n    const options = this.buildRequestOptions(null, requiresAuth);\n    return this.http.delete(url, {\n      headers: options.headers,\n      params: options.params\n    });\n  }\n  /**\n   * Build request options\n   * @param params Query parameters\n   * @param requiresAuth Whether the request requires authentication\n   * @returns Request options\n   */\n  buildRequestOptions(params, requiresAuth = true) {\n    let httpParams = new HttpParams();\n    let httpHeaders = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    // Add query parameters\n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== null && params[key] !== undefined) {\n          httpParams = httpParams.append(key, params[key]);\n        }\n      });\n    }\n    // Add authorization header\n    if (requiresAuth) {\n      const token = localStorage.getItem('token');\n      if (token) {\n        httpHeaders = httpHeaders.append('Authorization', `Bearer ${token}`);\n      }\n    }\n    return {\n      headers: httpHeaders,\n      params: httpParams\n    };\n  }\n  static {\n    this.ɵfac = function ApiService_Factory(t) {\n      return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiService,\n      factory: ApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "environment", "ApiService", "constructor", "http", "apiUrl", "get", "endpoint", "params", "requiresAuth", "url", "options", "buildRequestOptions", "headers", "post", "body", "put", "delete", "httpParams", "httpHeaders", "Object", "keys", "for<PERSON>ach", "key", "undefined", "append", "token", "localStorage", "getItem", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\findit\\client\\src\\app\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ApiService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  /**\r\n   * Generic GET request\r\n   * @param endpoint API endpoint\r\n   * @param params Query parameters\r\n   * @param requiresAuth Whether the request requires authentication\r\n   * @returns Observable of response\r\n   */\r\n  get<T>(endpoint: string, params?: any, requiresAuth: boolean = true): Observable<T> {\r\n    const url = `${this.apiUrl}/${endpoint}`;\r\n    const options = this.buildRequestOptions(params, requiresAuth);\r\n    return this.http.get<T>(url, {\r\n      headers: options.headers,\r\n      params: options.params\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generic POST request\r\n   * @param endpoint API endpoint\r\n   * @param body Request body\r\n   * @param requiresAuth Whether the request requires authentication\r\n   * @returns Observable of response\r\n   */\r\n  post<T>(endpoint: string, body: any, requiresAuth: boolean = true): Observable<T> {\r\n    const url = `${this.apiUrl}/${endpoint}`;\r\n    const options = this.buildRequestOptions(null, requiresAuth);\r\n    return this.http.post<T>(url, body, {\r\n      headers: options.headers,\r\n      params: options.params\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generic PUT request\r\n   * @param endpoint API endpoint\r\n   * @param body Request body\r\n   * @param requiresAuth Whether the request requires authentication\r\n   * @returns Observable of response\r\n   */\r\n  put<T>(endpoint: string, body: any, requiresAuth: boolean = true): Observable<T> {\r\n    const url = `${this.apiUrl}/${endpoint}`;\r\n    const options = this.buildRequestOptions(null, requiresAuth);\r\n    return this.http.put<T>(url, body, {\r\n      headers: options.headers,\r\n      params: options.params\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generic DELETE request\r\n   * @param endpoint API endpoint\r\n   * @param requiresAuth Whether the request requires authentication\r\n   * @returns Observable of response\r\n   */\r\n  delete<T>(endpoint: string, requiresAuth: boolean = true): Observable<T> {\r\n    const url = `${this.apiUrl}/${endpoint}`;\r\n    const options = this.buildRequestOptions(null, requiresAuth);\r\n    return this.http.delete<T>(url, {\r\n      headers: options.headers,\r\n      params: options.params\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Build request options\r\n   * @param params Query parameters\r\n   * @param requiresAuth Whether the request requires authentication\r\n   * @returns Request options\r\n   */\r\n  private buildRequestOptions(params?: any, requiresAuth: boolean = true): any {\r\n    let httpParams = new HttpParams();\r\n    let httpHeaders = new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    });\r\n\r\n    // Add query parameters\r\n    if (params) {\r\n      Object.keys(params).forEach(key => {\r\n        if (params[key] !== null && params[key] !== undefined) {\r\n          httpParams = httpParams.append(key, params[key]);\r\n        }\r\n      });\r\n    }\r\n\r\n    // Add authorization header\r\n    if (requiresAuth) {\r\n      const token = localStorage.getItem('token');\r\n      if (token) {\r\n        httpHeaders = httpHeaders.append('Authorization', `Bearer ${token}`);\r\n      }\r\n    }\r\n\r\n    return {\r\n      headers: httpHeaders,\r\n      params: httpParams\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAE1E,SAASC,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,UAAU;EAGrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAEK;EAExC;;;;;;;EAOAC,GAAGA,CAAIC,QAAgB,EAAEC,MAAY,EAAEC,YAAA,GAAwB,IAAI;IACjE,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,IAAIE,QAAQ,EAAE;IACxC,MAAMI,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAACJ,MAAM,EAAEC,YAAY,CAAC;IAC9D,OAAO,IAAI,CAACL,IAAI,CAACE,GAAG,CAAII,GAAG,EAAE;MAC3BG,OAAO,EAAEF,OAAO,CAACE,OAAO;MACxBL,MAAM,EAAEG,OAAO,CAACH;KACjB,CAAC;EACJ;EAEA;;;;;;;EAOAM,IAAIA,CAAIP,QAAgB,EAAEQ,IAAS,EAAEN,YAAA,GAAwB,IAAI;IAC/D,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,IAAIE,QAAQ,EAAE;IACxC,MAAMI,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,EAAEH,YAAY,CAAC;IAC5D,OAAO,IAAI,CAACL,IAAI,CAACU,IAAI,CAAIJ,GAAG,EAAEK,IAAI,EAAE;MAClCF,OAAO,EAAEF,OAAO,CAACE,OAAO;MACxBL,MAAM,EAAEG,OAAO,CAACH;KACjB,CAAC;EACJ;EAEA;;;;;;;EAOAQ,GAAGA,CAAIT,QAAgB,EAAEQ,IAAS,EAAEN,YAAA,GAAwB,IAAI;IAC9D,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,IAAIE,QAAQ,EAAE;IACxC,MAAMI,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,EAAEH,YAAY,CAAC;IAC5D,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAIN,GAAG,EAAEK,IAAI,EAAE;MACjCF,OAAO,EAAEF,OAAO,CAACE,OAAO;MACxBL,MAAM,EAAEG,OAAO,CAACH;KACjB,CAAC;EACJ;EAEA;;;;;;EAMAS,MAAMA,CAAIV,QAAgB,EAAEE,YAAA,GAAwB,IAAI;IACtD,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,IAAIE,QAAQ,EAAE;IACxC,MAAMI,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,EAAEH,YAAY,CAAC;IAC5D,OAAO,IAAI,CAACL,IAAI,CAACa,MAAM,CAAIP,GAAG,EAAE;MAC9BG,OAAO,EAAEF,OAAO,CAACE,OAAO;MACxBL,MAAM,EAAEG,OAAO,CAACH;KACjB,CAAC;EACJ;EAEA;;;;;;EAMQI,mBAAmBA,CAACJ,MAAY,EAAEC,YAAA,GAAwB,IAAI;IACpE,IAAIS,UAAU,GAAG,IAAIlB,UAAU,EAAE;IACjC,IAAImB,WAAW,GAAG,IAAIpB,WAAW,CAAC;MAChC,cAAc,EAAE;KACjB,CAAC;IAEF;IACA,IAAIS,MAAM,EAAE;MACVY,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,OAAO,CAACC,GAAG,IAAG;QAChC,IAAIf,MAAM,CAACe,GAAG,CAAC,KAAK,IAAI,IAAIf,MAAM,CAACe,GAAG,CAAC,KAAKC,SAAS,EAAE;UACrDN,UAAU,GAAGA,UAAU,CAACO,MAAM,CAACF,GAAG,EAAEf,MAAM,CAACe,GAAG,CAAC,CAAC;;MAEpD,CAAC,CAAC;;IAGJ;IACA,IAAId,YAAY,EAAE;MAChB,MAAMiB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTP,WAAW,GAAGA,WAAW,CAACM,MAAM,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE,CAAC;;;IAIxE,OAAO;MACLb,OAAO,EAAEM,WAAW;MACpBX,MAAM,EAAEU;KACT;EACH;;;uBArGWhB,UAAU,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAV9B,UAAU;MAAA+B,OAAA,EAAV/B,UAAU,CAAAgC,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}